# uautopilot_ros2_ws

## 编译和运行

### Docker环境

```
docker pull glcr.rd.ubtrobot.com/pub/docker/ros2:ubuntu22.04
```

### 工作空间内编译
```
colcon build
```

### 只编译指定目标
利用脚本文件only_build.sh，只编译指定的目标
例如，只编译custom_msgs, 则使用如下指令：
```
./only_build.sh custom_msgs
```
### 启动赤兔小车模型 (gazebo仿真)

相关编译完成后
```
source install/setup.bash
ros2 launch velodyne_description chitu.launch.py
```

### 启动Rviz2 Panel（使用本仓库内的相关rviz插件）

相关编译完成后
```
source install/setup.bash
rviz2
```

## Rviz Plugin

### task_rviz_plugin

任务下发面板，包含建图、定位、规划、逐点导航、设置地图等功能

**主面板-设置地图**

![image-20240725172449485](docs/image-20240725172449485.png)

**建图**

![image-20240725172453547](docs/image-20240725172453547.png)

**定位**

![image-20240725172456621](docs/image-20240725172456621.png)

**导航规划**

![image-20240725172459140](docs/image-20240725172459140.png)

**地图点保存、逐点导航**

![image-20240725172502102](docs/image-20240725172502102.png)



### planning_rviz_plugin

导航规划调试面板，包含SL图、路径SV图、ST图、速度和反馈图等可视化图表。

![image-20240725172629862](docs/image-20240725172629862.png)

### perception_rviz_plugin

感知信息的可视化插件，主要负责显示 **custom_msgs::msg::PerceptionObjects** 类型的Topic

![image-20240725172635468](docs/image-20240725172635468.png)

### monitor_rviz_plugin/new_monitor_rviz_plugin

系统占用可视化面板

### tier4_vehicle_rviz_plugin

方向盘显示插件，可显示当前车速和转向角

### simulation_tool_plugin

webots 仿真添加和修改障碍物位置、大小、速度的工具，为**Pose Tool**，点击地图上某位置，即在该位置生成(修改)虚拟的障碍物，webots上会同步出现红色障碍块。其id、位置、大小、速度在**Tool Properties** 面板进行修改。

* id<0 新增障碍物
* id>0 修改对应id的障碍物位置、大小和速度

![image-20240725172641998](docs/image-20240725172641998.png)



## Rviz2 工具开发

### rviz2 Panel 类型

* 继承 **rviz_common::Panel**
* 可在 QT Design 编辑 UI 文件，再转换为 h 文件导入 c++
* 图表类型的需要qt 的 qwt 组件，如果没有安装，请安装  `libqwt-qt5-dev`
* ui 文件 转为 h:  `uic xxx.ui -o xxx.h`
* ui 中可使用各种横向、竖向、表格布局，使按钮能够自动对齐并适应大小。
  * 布局是外部布局，重载 `resizeEvent` 函数，在捕捉窗口大小变化是，修改 Layout 大小。（参考 task_rviz_plugin_panel.cpp）
  * 布局写在Tab中，代码中需要在初始化时将teb内部的布局和 widget 绑定，使用` setLayout `绑定。（参考 task_rviz_plugin_panel.cpp）

### rviz2 Tools 类型

* 最底层为 **rviz_common::Tool**，根据功能不同，可基于现有的各种Tool开发，如**rviz_default_plugins::tools::PoseTool** 等，可在rviz2 顶部工具栏添加和删除
* 通过 rviz_common::properties::StringProperty 等创建设置参数，可在**Tool Properties** 进行配置。
* 功能通过重载鼠标事件或者内部回调函数即可

### rviz2 Topic 类型

* 继承 **rviz_common::RosTopicDisplay\<msg\>**, 负责某Topic的可视化
* 通过OpenGL 实现各种绘制

