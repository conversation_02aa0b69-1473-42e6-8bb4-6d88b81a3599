Panels:
  - Class: rviz_common/Displays
    Help Height: 78
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /perception1
        - /perception1/bev_perceptionObjects1/Status1
        - /routing1/had_map1
        - /planning1
        - /planning1/pnc_map_perferred_lane1/Namespaces1
        - /planning1/ReferenceLine Path1/Offset1
      Splitter Ratio: 0.5
    Tree Height: 410
  - Class: rviz_common/Selection
    Name: Selection
  - Class: rviz_common/Tool Properties
    Expanded:
      - /2D Pose Estimate1
    Name: Tool Properties
    Splitter Ratio: 0.****************
  - Class: rviz_common/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
  - Class: task_rviz_plugin/TaskRvizPanel
    Name: TaskRvizPanel
  - Class: planning_rviz_plugin/PlanningRvizPanel
    Name: PlanningRvizPanel
Visualization Manager:
  Class: ""
  Displays:
    - Alpha: 0.5
      Cell Size: 10
      Class: rviz_default_plugins/Grid
      Color: 160; 160; 164
      Enabled: true
      Line Style:
        Line Width: 0.029999999329447746
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 100
      Reference Frame: <Fixed Frame>
      Value: true
    - Class: rviz_common/Group
      Displays:
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz_default_plugins/PointCloud2
          Color: 255; 0; 0
          Color Transformer: FlatColor
          Decay Time: 0
          Enabled: false
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Max Intensity: 100
          Min Color: 0; 0; 0
          Min Intensity: 100
          Name: top_lidar
          Position Transformer: XYZ
          Selectable: true
          Size (Pixels): 3
          Size (m): 0.10000000149011612
          Style: Flat Squares
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Best Effort
            Value: /sensor/lslidar_point_cloud/top/raw
          Use Fixed Frame: true
          Use rainbow: true
          Value: false
        - Class: rviz_plugins/AckermannOdom
          Enabled: true
          Left: 30
          Length: 60
          Name: AckermannOdom
          Scale: 3
          Text Color: 25; 255; 240
          Top: 30
          Topic:
            Depth: 5
            Durability Policy: Volatile
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /ackermann/odom
          Value: true
          Value Scale: 0.25
          Value height offset: 10
        - Class: rviz_default_plugins/TF
          Enabled: false
          Frame Timeout: 15
          Frames:
            All Enabled: true
          Marker Scale: 1
          Name: vehicle_tf
          Show Arrows: true
          Show Axes: true
          Show Names: false
          Tree:
            {}
          Update Interval: 0
          Value: false
        - Class: rviz_default_plugins/Axes
          Enabled: true
          Length: 3
          Name: map
          Radius: 0.5
          Reference Frame: map
          Value: true
      Enabled: true
      Name: sensor
    - Class: rviz_common/Group
      Displays:
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz_default_plugins/PointCloud2
          Color: 255; 255; 255
          Color Transformer: Intensity
          Decay Time: 0
          Enabled: false
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Max Intensity: 100
          Min Color: 0; 0; 0
          Min Intensity: 100
          Name: cloud_slice
          Position Transformer: XYZ
          Selectable: true
          Size (Pixels): 3
          Size (m): 0.009999999776482582
          Style: Flat Squares
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /corrected_cloud
          Use Fixed Frame: true
          Use rainbow: true
          Value: false
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz_default_plugins/PointCloud2
          Color: 255; 255; 255
          Color Transformer: Intensity
          Decay Time: 0
          Enabled: false
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Max Intensity: 0
          Min Color: 0; 0; 0
          Min Intensity: 0
          Name: corrected_cloud
          Position Transformer: XYZ
          Selectable: true
          Size (Pixels): 3
          Size (m): 0.009999999776482582
          Style: Flat Squares
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /corrected_cloud
          Use Fixed Frame: true
          Use rainbow: true
          Value: false
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz_default_plugins/PointCloud2
          Color: 255; 255; 255
          Color Transformer: FlatColor
          Decay Time: 0
          Enabled: false
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Max Intensity: 0
          Min Color: 0; 0; 0
          Min Intensity: 0
          Name: global_map_locate
          Position Transformer: XYZ
          Selectable: true
          Size (Pixels): 3
          Size (m): 0.05000000074505806
          Style: Flat Squares
          Topic:
            Depth: 5
            Durability Policy: Transient Local
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /global_map_locate
          Use Fixed Frame: true
          Use rainbow: true
          Value: false
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz_default_plugins/PointCloud2
          Color: 255; 255; 255
          Color Transformer: ""
          Decay Time: 0
          Enabled: false
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Max Intensity: 4096
          Min Color: 0; 0; 0
          Min Intensity: 0
          Name: map_slice
          Position Transformer: ""
          Selectable: true
          Size (Pixels): 3
          Size (m): 0.009999999776482582
          Style: Flat Squares
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /map_slice
          Use Fixed Frame: true
          Use rainbow: true
          Value: false
        - Class: rviz_default_plugins/Axes
          Enabled: true
          Length: 3
          Name: base_link
          Radius: 0.5
          Reference Frame: base_link
          Value: true
      Enabled: true
      Name: localization
    - Class: rviz_common/Group
      Displays:
        - Agv Color: 255; 51; 204
          Alpha: 0.***************
          Bicycle Color: 255; 165; 0
          Bus Color: 255; 255; 0
          Car Color: 255; 255; 0
          Class: perception_rviz_plugin/PerceptionObjects
          Enabled: true
          Forklift Color: 51; 102; 255
          Goods Color: 255; 204; 255
          Motorcycle Color: 0; 255; 0
          Name: bev_perceptionObjects
          Namespaces:
            bounding_box: true
            id_text: true
          Other Color: 0; 0; 0
          Pedestrian Color: 255; 0; 0
          Shelves Color: 0; 255; 0
          Style: Bounding Box
          Topic:
            Depth: 5
            Durability Policy: Volatile
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /uslam/perception/perception_objects
          Traffic light Color: 0; 255; 0
          Trailer Color: 255; 255; 0
          Truck Color: 255; 255; 0
          Unknown Color: 255; 255; 255
          Value: true
          show chitu car: false
          show confidence: false
          show id: true
          show obs vel: false
          show predicted path: false
        - Class: rviz_default_plugins/MarkerArray
          Enabled: true
          Name: post_perceptuon_objects
          Namespaces:
            bev: true
            cluster: true
            drivable: true
            low_cluster_obj: true
            text_height_marker: true
            text_id: true
            text_speed_marker: true
          Topic:
            Depth: 5
            Durability Policy: Volatile
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /debug/cluster_marker
          Value: true
      Enabled: true
      Name: perception
    - Class: rviz_common/Group
      Displays:
        - Class: rviz_default_plugins/MarkerArray
          Enabled: true
          Name: routing_global_path
          Namespaces:
            goal: true
            lane_prefer_triangles: true
            lane_triangles: true
            start: true
          Topic:
            Depth: 5
            Durability Policy: Volatile
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /uslam/routing/full_route_markerarray
          Value: true
        - Class: rviz_default_plugins/MarkerArray
          Enabled: true
          Name: had_map
          Namespaces:
            center_lane_line: true
            lanelet_direction: true
            lanelet_triangles: true
            lanelet_triangles_id: true
            left_lane_bound: true
            right_lane_bound: true
          Topic:
            Depth: 1
            Durability Policy: Transient Local
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /uslam/map/viz_had_map
          Value: true
      Enabled: true
      Name: routing
    - Class: rviz_common/Group
      Displays:
        - Alpha: 0.***************
          Class: rviz_default_plugins/Map
          Color Scheme: map
          Draw Behind: false
          Enabled: false
          Name: freespace_cost_map
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /debug/freespace_planner/costmap
          Update Topic:
            Depth: 5
            Durability Policy: Volatile
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /debug/freespace_planner/costmap_updates
          Use Timestamp: false
          Value: false
        - Alpha: 1
          Arrow Length: 0.*****************
          Axes Length: 0.*****************
          Axes Radius: 0.009999999776482582
          Class: rviz_default_plugins/PoseArray
          Color: 255; 25; 0
          Enabled: false
          Head Length: 0.07000000029802322
          Head Radius: 0.029999999329447746
          Name: freespace_planner_trajectory
          Shaft Length: 0.23000000417232513
          Shaft Radius: 0.009999999776482582
          Shape: Arrow (Flat)
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /debug/freespace_planner/traj
          Value: false
        - Alpha: 1
          Buffer Length: 1
          Class: rviz_default_plugins/Path
          Color: 255; 255; 0
          Enabled: false
          Head Diameter: 0.*****************
          Head Length: 0.20000000298023224
          Length: 0.*****************
          Line Style: Billboards
          Line Width: 0.05000000074505806
          Name: unsmooted_base_refer_line
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: None
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /base_ref_line
          Value: false
        - Alpha: 1
          Buffer Length: 1
          Class: rviz_default_plugins/Path
          Color: 85; 255; 255
          Enabled: false
          Head Diameter: 0.*****************
          Head Length: 0.20000000298023224
          Length: 0.*****************
          Line Style: Billboards
          Line Width: 0.05000000074505806
          Name: smoothed_full_refer_line
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: None
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /debug/reference_line/full_path
          Value: false
        - Alpha: 1
          Buffer Length: 1
          Class: rviz_default_plugins/Path
          Color: 0; 0; 255
          Enabled: false
          Head Diameter: 0.*****************
          Head Length: 0.20000000298023224
          Length: 0.*****************
          Line Style: Billboards
          Line Width: 0.10000000149011612
          Name: opt_trajectory
          Offset:
            X: 0
            Y: 0
            Z: 10
          Pose Color: 255; 85; 255
          Pose Style: None
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Best Effort
            Value: /debug/opt_trajectory
          Value: false
        - Alpha: 1
          Buffer Length: 1
          Class: rviz_default_plugins/Path
          Color: 25; 255; 0
          Enabled: true
          Head Diameter: 0.*****************
          Head Length: 0.20000000298023224
          Length: 0.*****************
          Line Style: Billboards
          Line Width: 0.5
          Name: final_planning_trajectory
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: None
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic:
            Depth: 5
            Durability Policy: System Default
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: System Default
            Value: /debug/planning/trajectory
          Value: true
        - Alpha: 1
          Arrow Length: 0.*****************
          Axes Length: 0.*****************
          Axes Radius: 0.009999999776482582
          Class: rviz_default_plugins/PoseArray
          Color: 255; 25; 0
          Enabled: false
          Head Length: 0.07000000029802322
          Head Radius: 0.029999999329447746
          Name: smoothed_refer_line_pose_array
          Shaft Length: 0.23000000417232513
          Shaft Radius: 0.009999999776482582
          Shape: Arrow (Flat)
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /debug/reference_line/base_ref_line_points
          Value: false
        - Class: rviz_default_plugins/MarkerArray
          Enabled: true
          Name: pnc_map_perferred_lane
          Namespaces:
            preferred_lane: true
            vehicle: true
          Topic:
            Depth: 5
            Durability Policy: Volatile
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /pnc_map/preferred_lane_marker
          Value: true
        - Class: rviz_default_plugins/MarkerArray
          Enabled: true
          Name: behavior_decision_marker
          Namespaces:
            include_lanes: true
            ref_line: true
          Topic:
            Depth: 5
            Durability Policy: Volatile
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /planning/behavior_decision_marker
          Value: true
        - Class: rviz_default_plugins/MarkerArray
          Enabled: true
          Name: velocity_planner_stop_reason
          Namespaces:
            {}
          Topic:
            Depth: 5
            Durability Policy: Volatile
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /debug/velocity_planner/marker
          Value: true
        - Alpha: 1
          Buffer Length: 1
          Class: rviz_default_plugins/Path
          Color: 255; 255; 127
          Enabled: false
          Head Diameter: 0.*****************
          Head Length: 0.20000000298023224
          Length: 0.*****************
          Line Style: Billboards
          Line Width: 0.20000000298023224
          Name: ReferenceLine Bound
          Offset:
            X: 0
            Y: 0
            Z: 1
          Pose Color: 255; 85; 255
          Pose Style: None
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /debug/reference_line/bounds
          Value: false
        - Alpha: 1
          Buffer Length: 1
          Class: rviz_default_plugins/Path
          Color: 255; 170; 127
          Enabled: false
          Head Diameter: 0.*****************
          Head Length: 0.20000000298023224
          Length: 0.*****************
          Line Style: Billboards
          Line Width: 0.10000000149011612
          Name: ReferenceLine Path
          Offset:
            X: 0
            Y: 0
            Z: 1
          Pose Color: 255; 85; 255
          Pose Style: None
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /debug/reference_line/current_path
          Value: false
      Enabled: true
      Name: planning
  Enabled: true
  Global Options:
    Background Color: 48; 48; 48
    Fixed Frame: map
    Frame Rate: 30
  Name: root
  Tools:
    - Class: rviz_default_plugins/Interact
      Hide Inactive Objects: true
    - Class: rviz_default_plugins/MoveCamera
    - Class: rviz_default_plugins/Select
    - Class: rviz_default_plugins/FocusCamera
    - Class: rviz_default_plugins/Measure
      Line color: 128; 128; 0
    - Class: rviz_default_plugins/SetInitialPose
      Covariance x: 0.25
      Covariance y: 0.25
      Covariance yaw: 0.06853891909122467
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /uslam/hmi/initial_pose
    - Class: rviz_default_plugins/SetGoal
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /uslam/hmi/goal_pose
    - Class: rviz_default_plugins/PublishPoint
      Single click: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /clicked_point
    - Class: simulation_tool_plugin/ObjectTool
      H vehicle height: 3
      ID: -1
      L vehicle length: 5
      Pose Topic: /wb_simulated_obstacle
      Target Frame: base_link
      Velocity: 0
      W vehicle width: 3
  Transformation:
    Current:
      Class: rviz_default_plugins/TF
  Value: true
  Views:
    Current:
      Angle: 30.464242935180664
      Class: rviz_default_plugins/TopDownOrtho
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.05999999865889549
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.009999999776482582
      Scale: 15.221105575561523
      Target Frame: base_link
      Value: TopDownOrtho (rviz_default_plugins)
      X: 13.604272842407227
      Y: -1.739227294921875
    Saved: ~
Window Geometry:
  Displays:
    collapsed: false
  Height: 1043
  Hide Left Dock: false
  Hide Right Dock: true
  PlanningRvizPanel:
    collapsed: false
  QMainWindow State: 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
  Selection:
    collapsed: false
  TaskRvizPanel:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: true
  Width: 1920
  X: 1920
  Y: 0
