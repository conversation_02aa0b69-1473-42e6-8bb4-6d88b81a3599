---
include:
  - project: "pub/ci_templates"
    file: "/cpp.lint.gitlab-ci.yml"
  - project: "pub/ci_templates"
    file: "/docker.build/define.gitlab-ci.yml"


stages:
  - lint
  - build

build:ubuntu22_merge_requests:
  variables:
    PLATFORM: linux/amd64,linux/arm64
    BASE_IMAGE_NAME: ${UA_REGISTRY}/pub/docker/ros2:ubuntu22.04
    TAG: humble
  extends:
    - .merge_requests_job

build:ubuntu22_main:
  variables:
    PLATFORM: linux/amd64,linux/arm64
    BASE_IMAGE_NAME: ${UA_REGISTRY}/pub/docker/ros2:ubuntu22.04
    TAG: humble
  extends:
    - .main_job

