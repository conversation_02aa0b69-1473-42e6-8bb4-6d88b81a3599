#!/usr/bin/env bash
set -e

export INSTALL_PATH=/opt/uautopilot/uautopilot_ros2_ws

sudo mkdir -p $INSTALL_PATH
sudo chown -R 1000:1000 $INSTALL_PATH

. /opt/ros/humble/local_setup.bash

if [ "$TARGETPLATFORM" = "linux/amd64" ]; then
    colcon build --merge-install \
    --install-base $INSTALL_PATH
elif [ "$TARGETPLATFORM" = "linux/arm64" ]; then
    colcon build --merge-install \
    --install-base $INSTALL_PATH --packages-skip velodyne_gazebo_plugins sim_perception_pub velodyne_simulator
else
    exit -1
fi



