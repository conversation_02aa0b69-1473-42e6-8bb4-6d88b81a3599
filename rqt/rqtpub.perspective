{"keys": {}, "groups": {"mainwindow": {"keys": {"geometry": {"repr(QByteArray.hex)": "QtCore.QByteArray(b'01d9d0cb000300000000083d000002cc00000d05000004c80000083d000002ea00000d05000004c8000000010000000005a00000083d000002ea00000d05000004c8')", "type": "repr(QByteArray.hex)", "pretty-print": "     =       =            =      "}, "state": {"repr(QByteArray.hex)": "QtCore.QByteArray(b'000000ff00000000fd0000000100000003000004c9000001b5fc0100000001fb00000058007200710074005f007000750062006c00690073006800650072005f005f005000750062006c00690073006800650072005f005f0031005f005f005000750062006c006900730068006500720057006900640067006500740100000000000004c90000022b00ffffff000004c90000000000000004000000040000000800000008fc00000001000000030000000100000036004d0069006e0069006d0069007a006500640044006f0063006b00570069006400670065007400730054006f006f006c0062006100720000000000ffffffff0000000000000000')", "type": "repr(QByteArray.hex)", "pretty-print": "                 Xrqt_publisher__Publisher__1__PublisherWidget                            6MinimizedDockWidgetsToolbar        "}}, "groups": {"toolbar_areas": {"keys": {"MinimizedDockWidgetsToolbar": {"repr": "8", "type": "repr"}}, "groups": {}}}}, "pluginmanager": {"keys": {"running-plugins": {"repr": "{'rqt_publisher/Publisher': [1]}", "type": "repr"}}, "groups": {"plugin__rqt_publisher__Publisher__1": {"keys": {}, "groups": {"dock_widget__PublisherWidget": {"keys": {"dock_widget_title": {"repr": "'Message Publisher'", "type": "repr"}, "dockable": {"repr": "True", "type": "repr"}, "parent": {"repr": "None", "type": "repr"}}, "groups": {}}, "plugin": {"keys": {"publishers": {"repr": "'[{\\'topic_name\\': \\'/uslam/task/mapping\\', \\'type_name\\': \\'std_msgs/msg/String\\', \\'rate\\': 1.0, \\'enabled\\': False, \\'publisher_id\\': 0, \\'counter\\': 8, \\'expressions\\': {\\'/uslam/task/mapping/data\\': \\'\\\\\\'{\"title\":\"save\"}\\\\\\'\\'}}, {\\'topic_name\\': \\'/uslam/task/mapping\\', \\'type_name\\': \\'std_msgs/msg/String\\', \\'rate\\': 1.0, \\'enabled\\': False, \\'publisher_id\\': 1, \\'counter\\': 6, \\'expressions\\': {\\'/uslam/task/mapping/data\\': \\'\\\\\\'{\"map_folder_name\":\"test2\",\"title\":\"start\"}\\\\\\'\\'}}]'", "type": "repr"}}, "groups": {}}}}}}}}