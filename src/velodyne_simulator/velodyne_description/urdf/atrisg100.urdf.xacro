<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="example">
  <xacro:arg name="gpu" default="false"/>
  <xacro:property name="gpu" value="$(arg gpu)" />
  <xacro:arg name="organize_cloud" default="false"/>
  <xacro:property name="organize_cloud" value="$(arg organize_cloud)" />

  <!-- Base Footprint -->
  <!-- <link name="base_footprint" /> -->

  <!-- Base Link -->
  <!-- <joint name="footprint" type="fixed" >
   <parent link="base_footprint" />
    <child link="base_link" />
    <origin xyz="0 0 0.05" rpy="0 0 0" />
  </joint> -->
  
  <link name="base_link" >
    <visual>
      <geometry>
        <box size="0.5 0.5 0.1" />
      </geometry>
    </visual>
    <collision>
      <geometry>
        <box size="0.5 0.5 0.1" />
      </geometry>
    </collision>
    <inertial>
      <origin xyz="0 0 0"/>
      <mass value="10"/>
      <inertia ixx="3.0" ixy="0.0" ixz="0.0"
               iyy="3.0" iyz="0.0" 
               izz="3.0" />
    </inertial>
  </link>

  <xacro:include filename="$(find velodyne_description)/urdf/VLP-16.urdf.xacro"/>
  <xacro:VLP-16 parent="base_link" name="velodyne" topic="/velodyne_points" organize_cloud="${organize_cloud}" hz="10" samples="440" gpu="${gpu}">
    <origin xyz="-0.075 0 0.56" rpy="3.1415926 0 0" />
  </xacro:VLP-16>

  <!-- <xacro:include filename="$(find velodyne_description)/urdf/HDL-32E.urdf.xacro"/>
  <xacro:HDL-32E parent="base_link" name="velodyne2" topic="/velodyne_points2" organize_cloud="${organize_cloud}" hz="10" samples="220" gpu="${gpu}">
    <origin xyz="0 0 0.6" rpy="0 0 0" />
  </xacro:HDL-32E> -->

  <joint name="imu_joint" type="fixed">
    <parent link="base_link"/>
    <child link="imu_link"/>
    <origin xyz="0 0 0.2"/>
  </joint>

  <link name="imu_link">
    <visual>
      <geometry>
        <box size="0.1 0.1 0.1"/>
      </geometry>
    </visual>

    <collision>
      <geometry>
        <box size="0.1 0.1 0.1"/>
      </geometry>
    </collision>

    <inertial>
      <origin xyz="0 0 0"/>
      <mass value="2"/>
      <inertia ixx="3.0" ixy="0.0" ixz="0.0"
               iyy="3.0" iyz="0.0" 
               izz="3.0" />
    </inertial>
  </link>



  <gazebo reference="imu_link">
    <sensor name="imu_sensor" type="imu">
      <plugin filename="libgazebo_ros_imu_sensor.so" name="imu_plugin">
        <ros>
          <namespace>/</namespace>
          <remapping>~/out:=ros_imu</remapping>
        </ros>
        <initial_orientation_as_reference>false</initial_orientation_as_reference>
      </plugin>
      <always_on>true</always_on>
      <update_rate>100</update_rate>
      <visualize>true</visualize>
      <imu>
        <angular_velocity>
          <x>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>2e-4</stddev>
              <bias_mean>0.0000075</bias_mean>
              <bias_stddev>0.0000008</bias_stddev>
            </noise>
          </x>
          <y>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>2e-4</stddev>
              <bias_mean>0.0000075</bias_mean>
              <bias_stddev>0.0000008</bias_stddev>
            </noise>
          </y>
          <z>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>2e-4</stddev>
              <bias_mean>0.0000075</bias_mean>
              <bias_stddev>0.0000008</bias_stddev>
            </noise>
          </z>
        </angular_velocity>
        <linear_acceleration>
          <x>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>1.7e-2</stddev>
              <bias_mean>0.1</bias_mean>
              <bias_stddev>0.001</bias_stddev>
            </noise>
          </x>
          <y>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>1.7e-2</stddev>
              <bias_mean>0.1</bias_mean>
              <bias_stddev>0.001</bias_stddev>
            </noise>
          </y>
          <z>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>1.7e-2</stddev>
              <bias_mean>0.1</bias_mean>
              <bias_stddev>0.001</bias_stddev>
            </noise>
          </z>
        </linear_acceleration>
      </imu>
    </sensor>
  </gazebo>


  <gazebo>
    <plugin name="object_controller" filename="libgazebo_ros_planar_move.so">
      <remapping>cmd_vel:=uslam/gazebo_cmd_vel_chassis</remapping>
      <remapping>odom:=uslam/odom</remapping>
      <odometry_frame>odom</odometry_frame>
      <update_rate>100.0</update_rate>
      <publish_rate>100.0</publish_rate>
      <robot_base_frame>base_link</robot_base_frame>
    </plugin>
  </gazebo>

</robot>
