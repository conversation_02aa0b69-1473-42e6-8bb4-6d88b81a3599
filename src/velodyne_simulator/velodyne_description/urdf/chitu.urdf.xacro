<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="chitu">
  <xacro:include filename="head.xacro"/>

  <xacro:arg name="gpu" default="false"/>
  <xacro:property name="gpu" value="$(arg gpu)" />
  <xacro:arg name="organize_cloud" default="false"/>
  <xacro:property name="organize_cloud" value="$(arg organize_cloud)" />
  <xacro:property name="wheel_mass" value="0.1" />
  <xacro:property name="wheel_width" value="0.2" />
  <xacro:property name="wheel_radius" value="0.2835" />
  <xacro:property name="wheel_base" value="2.20" />
  <xacro:property name="chassis_mass" value="5.0" />
  <xacro:property name="chassis_length" value="2.5" />
  <xacro:property name="chassis_width" value="1.34" />
  <!--左右两车轮的质心距离-->
  <xacro:property name="chassis_height" value="1.1" />
  <xacro:property name="lidar_height_offset" value="0.1" />
  <xacro:property name="imu_mass" value="0.05" />
  <xacro:property name="imu_length" value="0.1" />
  <xacro:property name="imu_height" value="0.05" />
  <xacro:property name="wheel_chassis_offset" value="0" />
  <xacro:property name="shock_dis" value="0.042" />

  <gazebo reference="left_front_wheel">
    <mu1>0.9</mu1>
    <mu2>0.9</mu2>
    <minDepth>0.005</minDepth>
    <kp>1e8</kp>
  </gazebo>

  <gazebo reference="right_front_wheel">
    <mu1>0.9</mu1>
    <mu2>0.9</mu2>
    <minDepth>0.005</minDepth>
    <kp>1e8</kp>
  </gazebo>

  <gazebo reference="left_rear_wheel">
    <mu1>0.9</mu1>
    <mu2>0.9</mu2>
    <minDepth>0.005</minDepth>
    <kp>1e8</kp>
  </gazebo>

  <gazebo reference="right_rear_wheel">
    <mu1>0.9</mu1>
    <mu2>0.9</mu2>
    <minDepth>0.005</minDepth>
    <kp>1e8</kp>
  </gazebo>

  <link name="base_link">
    <visual>
      <geometry>
        <box size="0.01 0.01 0.01"/>
      </geometry>
      <material name="chassis_mat"/>
    </visual>
  </link>

  <gazebo reference="base_link">
    <material>Gazebo/Grey</material>
  </gazebo>

  <link name="chassis">
    <visual>
      <geometry>
        <box size="${chassis_length} ${chassis_width} ${chassis_height}" />
      </geometry>
    </visual>
    <collision>
      <geometry>
        <box size="${chassis_length} ${chassis_width} ${chassis_height}" />
      </geometry>
    </collision>
    <xacro:box_inertial_matrix m="${chassis_mass}" l="${chassis_length}" w="${chassis_width}" h="${chassis_height}" />
  </link>

  <joint name="base_link_to_chassis" type="fixed">
    <parent link="base_link"/>
    <child link="chassis"/>
    <origin xyz="${wheel_base/2.0} 0 ${chassis_height/2.0 + wheel_radius + 0.1}" />
  </joint>


  <!-- 左前轮 -->
  <joint name="left_front_shock" type="prismatic">
    <parent link="base_link"/>
    <child link="left_steering_link"/>
    <origin xyz="${wheel_base} ${chassis_width/2.0 + wheel_chassis_offset} 0"/>
    <axis xyz="0 0 -1"/>
    <limit effort="12.5106" lower="${-shock_dis}" upper="${shock_dis}" velocity="1000"/>
  </joint>

  <link name="left_steering_link">
    <inertial>
      <mass value="0.001"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
  </link>

  <joint name="left_steering_joint" type="revolute">
    <parent link="left_steering_link"/>
    <child link="left_front_axle_carrier"/>
    <axis xyz="0 0 1"/>
    <limit effort="0.5649" lower="-0.785398163" upper="0.785398163" velocity="4.553"/>
  </joint>

  <link name="left_front_axle_carrier">
    <inertial>
      <mass value="0.001"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
  </link>

  <joint name="left_front_axle" type="continuous">
    <parent link="left_front_axle_carrier"/>
    <child link="left_front_wheel"/>
    <origin rpy="1.57079633 0 0"/>
    <axis xyz="0 0 -1"/>
    <limit effort="5.12766" velocity="244.8696"/>
  </joint>

  <link name="left_front_wheel">
    <visual>
      <origin xyz="0 0 0"/>
      <geometry>
        <cylinder length="${wheel_width}" radius="${wheel_radius}"/>
      </geometry>
      <material name="tire_mat"/>
    </visual>
    <collision>
      <origin xyz="0 0 0"/>
      <geometry>
        <cylinder length="${wheel_width}" radius="${wheel_radius}"/>
      </geometry>
    </collision>
    <xacro:cylinder_inertial_matrix m="${wheel_mass}" r="${wheel_radius}" h="${wheel_width}" />
  </link>

  <gazebo reference="left_front_wheel">
    <material>Gazebo/Black</material>
  </gazebo>


  <!-- 右前轮 -->
  <joint name="right_front_shock" type="prismatic">
    <parent link="base_link"/>
    <child link="right_steering_link"/>
    <origin xyz="${wheel_base} ${-chassis_width/2.0 - wheel_chassis_offset} 0"/>
    <axis xyz="0 0 -1"/>
    <limit effort="12.5106" lower="${-shock_dis}" upper="${shock_dis}" velocity="1000"/>
  </joint>

  <link name="right_steering_link">
    <inertial>
      <mass value="0.001"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
  </link>

  <joint name="right_steering_joint" type="revolute">
    <parent link="right_steering_link"/>
    <child link="right_front_axle_carrier"/>
    <axis xyz="0 0 1"/>
    <limit effort="0.5649" lower="-0.785398163" upper="0.785398163" velocity="4.553"/>
  </joint>

  <link name="right_front_axle_carrier">
    <inertial>
      <mass value="0.001"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
  </link>

  <joint name="right_front_axle" type="continuous">
    <parent link="right_front_axle_carrier"/>
    <child link="right_front_wheel"/>
    <origin rpy="1.57079633 0 0"/>
    <axis xyz="0 0 -1"/>
    <limit effort="5.12766" velocity="244.8696"/>
  </joint>

  <link name="right_front_wheel">
    <visual>
      <origin xyz="0 0 0"/>
      <geometry>
        <cylinder length="${wheel_width}" radius="${wheel_radius}"/>
      </geometry>
      <material name="tire_mat"/>
    </visual>
    <collision>
      <origin xyz="0 0 0"/>
      <geometry>
        <cylinder length="${wheel_width}" radius="${wheel_radius}"/>
      </geometry>
    </collision>
    <xacro:cylinder_inertial_matrix m="${wheel_mass}" r="${wheel_radius}" h="${wheel_width}" />
  </link>

  <gazebo reference="right_front_wheel">
    <material>Gazebo/Black</material>
  </gazebo>

  <!--左后轮-->
  <joint name="left_rear_shock" type="prismatic">
    <parent link="base_link"/>
    <child link="left_rear_axle_carrier"/>
    <origin xyz="0 ${chassis_width/2.0 + wheel_chassis_offset} 0"/>
    <axis xyz="0 0 -1"/>
    <limit effort="12.5106" lower="${-shock_dis}" upper="${shock_dis}" velocity="1000"/>
  </joint>

  <link name="left_rear_axle_carrier">
    <inertial>
      <mass value="0.001"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
  </link>

  <joint name="left_rear_axle" type="continuous">
    <parent link="left_rear_axle_carrier"/>
    <child link="left_rear_wheel"/>
    <origin rpy="1.57079633 0 0"/>
    <axis xyz="0 0 -1"/>
    <limit effort="5.12766" velocity="244.8696"/>
  </joint>

  <link name="left_rear_wheel">
    <visual>
      <origin xyz="0 0 0"/>
      <geometry>
        <cylinder length="${wheel_width}" radius="${wheel_radius}"/>
      </geometry>
      <material name="tire_mat"/>
    </visual>
    <collision>
      <origin xyz="0 0 0"/>
      <geometry>
        <cylinder length="${wheel_width}" radius="${wheel_radius}"/>
      </geometry>
    </collision>
    <xacro:cylinder_inertial_matrix m="${wheel_mass}" r="${wheel_radius}" h="${wheel_width}" />
  </link>
  <gazebo reference="left_rear_wheel">
    <material>Gazebo/Black</material>
  </gazebo>

  <!--右后轮-->
  <joint name="right_rear_shock" type="prismatic">
    <parent link="base_link"/>
    <child link="right_rear_axle_carrier"/>
    <origin xyz="0 ${-chassis_width/2.0 - wheel_chassis_offset} 0"/>
    <axis xyz="0 0 -1"/>
    <limit effort="12.5106" lower="${-shock_dis}" upper="${shock_dis}" velocity="1000"/>
  </joint>

  <link name="right_rear_axle_carrier">
    <inertial>
      <mass value="0.001"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
  </link>

  <joint name="right_rear_axle" type="continuous">
    <parent link="right_rear_axle_carrier"/>
    <child link="right_rear_wheel"/>
    <origin rpy="1.57079633 0 0"/>
    <axis xyz="0 0 -1"/>
    <limit effort="5.12766" velocity="244.8696"/>
  </joint>

  <link name="right_rear_wheel">
    <visual>
      <origin xyz="0 0 0"/>
      <geometry>
        <cylinder length="${wheel_width}" radius="${wheel_radius}"/>
      </geometry>
      <material name="tire_mat"/>
    </visual>
    <collision>
      <origin xyz="0 0 0"/>
      <geometry>
        <cylinder length="${wheel_width}" radius="${wheel_radius}"/>
      </geometry>
    </collision>
    <xacro:cylinder_inertial_matrix m="${wheel_mass}" r="${wheel_radius}" h="${wheel_width}" />
  </link>
  <gazebo reference="right_rear_wheel">
    <material>Gazebo/Black</material>
  </gazebo>

  <xacro:include filename="$(find velodyne_description)/urdf/VLP-16.urdf.xacro"/>
  <xacro:VLP-16 parent="base_link" name="laser_link_top_ros2" topic="/sensor/lslidar_point_cloud/top/raw" organize_cloud="${organize_cloud}" hz="10" samples="440" gpu="${gpu}">
    <origin xyz="2.2991767 0.0021836 1.5997236" rpy="0 0 -1.57" />
  </xacro:VLP-16>

  <joint name="imu_joint" type="fixed">
    <parent link="base_link"/>
    <child link="imu_link"/>
    <origin xyz="2.24439 0.006593 1.5049198" rpy="0 0 -1.57"/>
  </joint>

  <link name="imu_link">
    <visual>
      <geometry>
        <box size="${imu_length} ${imu_length} ${imu_height}"/>
      </geometry>
    </visual>

    <collision>
      <geometry>
        <box size="${imu_length} ${imu_length} ${imu_height}"/>
      </geometry>
    </collision>

    <xacro:box_inertial_matrix m="${imu_mass}" l="${imu_length}" w="${imu_length}" h="${imu_height}" />
  </link>

  <gazebo reference="imu_link">
    <material>Gazebo/Grey</material>
    <sensor name="imu_sensor" type="imu">
      <plugin filename="libgazebo_ros_imu_sensor.so" name="imu_plugin">
        <ros>
          <namespace>/</namespace>
          <remapping>~/out:=/sensor/imu</remapping>
        </ros>
        <initial_orientation_as_reference>false</initial_orientation_as_reference>
      </plugin>
      <always_on>true</always_on>
      <update_rate>100</update_rate>
      <visualize>true</visualize>
      <imu>
        <angular_velocity>
          <x>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>2e-6</stddev>
              <bias_mean>0.0000000075</bias_mean>
              <bias_stddev>0.000000008</bias_stddev>
            </noise>
          </x>
          <y>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>2e-6</stddev>
              <bias_mean>0.0000000075</bias_mean>
              <bias_stddev>0.000000008</bias_stddev>
            </noise>
          </y>
          <z>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>2e-6</stddev>
              <bias_mean>0.0000000075</bias_mean>
              <bias_stddev>0.000000008</bias_stddev>
            </noise>
          </z>
        </angular_velocity>
        <linear_acceleration>
          <x>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>2e-6</stddev>
              <bias_mean>0.0000000075</bias_mean>
              <bias_stddev>0.000000008</bias_stddev>
            </noise>
          </x>
          <y>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>2e-6</stddev>
              <bias_mean>0.0000000075</bias_mean>
              <bias_stddev>0.000000008</bias_stddev>
            </noise>
          </y>
          <z>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>2e-6</stddev>
              <bias_mean>0.0000000075</bias_mean>
              <bias_stddev>0.000000008</bias_stddev>
            </noise>
          </z>
        </linear_acceleration>
      </imu>
    </sensor>
  </gazebo>

  <xacro:include filename="$(find velodyne_description)/urdf/chitu.ros2_control.xacro" />

  <gazebo>
    <plugin filename="libgazebo_ros2_control.so" name="gazebo_ros2_control">
      <parameters>$(find velodyne_description)/config/my_controllers.yaml</parameters>
    </plugin>
  </gazebo>


</robot>
