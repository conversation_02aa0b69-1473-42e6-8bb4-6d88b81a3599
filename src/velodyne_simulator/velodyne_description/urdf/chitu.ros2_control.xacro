<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">


    <ros2_control name="GazeboSystem" type="system">
      <hardware>
        <plugin>gazebo_ros2_control/GazeboSystem</plugin>
      </hardware>

      <joint name="left_steering_joint">
        <command_interface name="position">
            <param name="min">-1.0</param>
            <param name="max">1.0</param>
        </command_interface>
        <state_interface name="position"/>
      </joint>

      <joint name="right_steering_joint">
        <command_interface name="position">
          <param name="min">-1.0</param>
          <param name="max">1.0</param>
        </command_interface>
        <state_interface name="position"/>
      </joint>


      <joint name="left_front_axle">
        <!-- <command_interface name="velocity">
          <param name="min">-50</param>
          <param name="max">50</param>
        </command_interface> -->
        <state_interface name="velocity"/>
        <state_interface name="position"/>
      </joint>

      <joint name="right_front_axle">
        <!-- <command_interface name="velocity">
          <param name="min">-50</param>
          <param name="max">50</param>
        </command_interface> -->
        <state_interface name="velocity"/>
        <state_interface name="position"/>
      </joint>

      <joint name="left_rear_axle">
        <command_interface name="velocity">
          <param name="min">-50</param>
          <param name="max">50</param>
        </command_interface>
        <state_interface name="velocity"/>
        <state_interface name="position"/>
      </joint>

      <joint name="right_rear_axle">
        <command_interface name="velocity">
          <param name="min">-50</param>
          <param name="max">50</param>
        </command_interface>
        <state_interface name="velocity"/>
        <state_interface name="position"/>
      </joint>

    </ros2_control>

</robot>
