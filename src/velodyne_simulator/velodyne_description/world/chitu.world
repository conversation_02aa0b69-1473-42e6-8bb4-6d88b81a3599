<sdf version='1.7'>
  <world name='default'>
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose>0 0 10 0 -0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.2 0.2 0.2 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 0.1 -0.9</direction>
      <spot>
        <inner_angle>0</inner_angle>
        <outer_angle>0</outer_angle>
        <falloff>0</falloff>
      </spot>
    </light>
    <gravity>0 0 -9.8</gravity>
    <magnetic_field>6e-06 2.3e-05 -4.2e-05</magnetic_field>
    <atmosphere type='adiabatic'/>
    <physics type='ode'>
      <max_step_size>0.001</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>1000</real_time_update_rate>
    </physics>
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.7 0.7 1</background>
      <shadows>1</shadows>
    </scene>
    <wind/>
    <spherical_coordinates>
      <surface_model>EARTH_WGS84</surface_model>
      <latitude_deg>0</latitude_deg>
      <longitude_deg>0</longitude_deg>
      <elevation>0</elevation>
      <heading_deg>0</heading_deg>
    </spherical_coordinates>
    <model name='ground_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>100</mu>
                <mu2>50</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode/>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-35.434 3.83371 0 0 -0 0</pose>
    </model>
    <model name='washer'>
      <link name='link'>
        <pose>0 0 0.001 0 -0 0</pose>
        <inertial>
          <mass>0.0122</mass>
          <inertia>
            <ixx>1.08758e-06</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1.08758e-06</iyy>
            <iyz>0</iyz>
            <izz>2.16703e-06</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://washer/meshes/washer.dae</uri>
            </mesh>
          </geometry>
          <surface>
            <contact>
              <poissons_ratio>0.305</poissons_ratio>
              <elastic_modulus>2e+11</elastic_modulus>
              <ode>
                <kp>100000</kp>
                <kd>100</kd>
                <max_vel>100</max_vel>
                <min_depth>0.001</min_depth>
              </ode>
            </contact>
            <friction>
              <torsional>
                <coefficient>1</coefficient>
                <use_patch_radius>0</use_patch_radius>
                <surface_radius>0.01</surface_radius>
                <ode/>
              </torsional>
              <ode/>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://washer/meshes/washer.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-34.3528 -7.49095 0 0 -0 0</pose>
    </model>
    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
              <scale>1.5 1.5 1.5</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
              <scale>1.5 1.5 1.5</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_tangent_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-40.6485 7.70035 0 0 -0 0</pose>
    </model>
    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
              <scale>1.5 1.5 1.5</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
              <scale>1.5 1.5 1.5</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_tangent_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-40.9418 -12.7218 0 0 -0 0</pose>
    </model>
    <model name='House 3'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
              <scale>1.5 1.5 1.5</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
              <scale>1.5 1.5 1.5</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_3/materials/scripts</uri>
              <uri>model://house_3/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_3/Diffuse</name>
            </script>
            <shader type='normal_map_tangent_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-37.415 -30.6793 0 0 -0 0</pose>
    </model>
    <model name='law_office'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <pose>0 0 6.96244 0 -0 0</pose>
          <geometry>
            <box>
              <size>6.84401 5.43165 13.9249</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://law_office/meshes/law_office.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-19.8689 -31.2639 0 0 -0 0</pose>
    </model>
    <model name='post_office'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://post_office/meshes/post_office.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://post_office/meshes/post_office.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-18.7008 37.0491 0 0 -0 0</pose>
    </model>
    <model name='gazebo'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <scale>3 3 2.5</scale>
              <uri>model://gazebo/meshes/gazebo.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <scale>3 3 2.5</scale>
              <uri>model://gazebo/meshes/gazebo.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-0.977582 37.9105 0 0 -0 0</pose>
    </model>
    <model name='Fast Food'>
      <static>1</static>
      <link name='link'>
        <pose>0 0 3.15931 0 -0 0</pose>
        <collision name='collision'>
          <geometry>
            <mesh>
              <scale>3 3 2</scale>
              <uri>model://fast_food/meshes/fast_food.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <scale>3 3 2</scale>
              <uri>model://fast_food/meshes/fast_food.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://fast_food/materials/scripts</uri>
              <uri>model://fast_food/materials/textures</uri>
              <name>FastFood/Diffuse</name>
            </script>
            <shader type='normal_map_tangent_space'>
              <normal_map>FastFood_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>27.8838 37.5776 0 0 -0 0</pose>
    </model>
    <model name='apartment'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://apartment/meshes/apartment.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://apartment/meshes/apartment.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>0.908756 -1.88185 0 0 -0 0</pose>
    </model>
    <model name='pier'>
      <static>1</static>
      <link name='link'>
        <pose>0 0 7.45747 0 -0 0</pose>
        <collision name='collision_platform'>
          <pose>0 11.1915 0.479154 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.99841 22.1059 0.223137</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='collision_pillar1'>
          <pose>2.7658 0.839949 -2.6492 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.526207 0.526207 9.61655</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='collision_pillar2'>
          <pose>2.7658 5.1533 -2.6492 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.526207 0.526207 9.61655</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='collision_pillar3'>
          <pose>2.7658 10.6027 -2.6492 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.526207 0.526207 9.61655</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='collision_pillar4'>
          <pose>2.7658 16.5973 -2.6492 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.526207 0.526207 9.61655</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='collision_pillar5'>
          <pose>2.7658 21.9768 -2.6492 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.526207 0.526207 9.61655</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='collision_pillar6'>
          <pose>-2.7658 0.839949 -2.6492 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.526207 0.526207 9.61655</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='collision_pillar7'>
          <pose>-2.7658 5.1533 -2.6492 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.526207 0.526207 9.61655</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='collision_pillar8'>
          <pose>-2.7658 10.6027 -2.6492 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.526207 0.526207 9.61655</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='collision_pillar9'>
          <pose>-2.7658 16.5973 -2.6492 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.526207 0.526207 9.61655</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='collision_pillar10'>
          <pose>-2.7658 21.9768 -2.6492 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.526207 0.526207 9.61655</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='collision_beam1'>
          <pose>0 0.303862 0.079695 0 -0 0</pose>
          <geometry>
            <box>
              <size>6.28904 0.526207 0.526207</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='collision_beam2'>
          <pose>0 4.61721 0.079695 0 -0 0</pose>
          <geometry>
            <box>
              <size>6.28904 0.526207 0.526207</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='collision_beam3'>
          <pose>0 10.0666 0.079695 0 -0 0</pose>
          <geometry>
            <box>
              <size>6.28904 0.526207 0.526207</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='collision_beam4'>
          <pose>0 16.0612 0.079695 0 -0 0</pose>
          <geometry>
            <box>
              <size>6.28904 0.526207 0.526207</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='collision_beam5'>
          <pose>0 21.4407 0.079695 0 -0 0</pose>
          <geometry>
            <box>
              <size>6.28904 0.526207 0.526207</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://pier/meshes/pier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>41.4778 1.63006 0 0 -0 0</pose>
    </model>
    <model name='pine_tree'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-21.6069 -44.4628 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_0'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-17.3854 -44.5523 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-19.3059 -41.8482 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_0'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-14.5266 -40.6452 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-13.4791 -44.6111 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_3'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-7.64178 -41.2601 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_4'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-35.6573 -44.7222 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_5'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-31.4866 -44.8181 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_6'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-27.3643 -45.0333 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_7'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-5.36837 -44.7659 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_8'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-2 -45 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_9'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>1.58243 -41.817 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_10'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>4.98862 -44.8266 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_11'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>8.05006 -41.6861 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_12'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>12.4611 -44.4022 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_13'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>13.1412 -40.3312 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_14'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>17.8531 -44.6577 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_15'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>21.3145 -41.4159 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_16'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>24.1125 -44.6069 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_17'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>28.0388 -39.5844 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_18'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>30.7167 -44.5628 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_19'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>36.0211 -45.5837 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_20'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>37.0511 -40.3664 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_21'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>41.8872 -43.1708 0 0 -0 0</pose>
    </model>
    <model name='pine_tree_clone_22'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://pine_tree/meshes/pine_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://pine_tree/materials/scripts/</uri>
              <uri>model://pine_tree/materials/textures/</uri>
              <name>PineTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-40.8106 -43.1084 0 0 -0 0</pose>
    </model>
    <model name='Gas Station'>
      <static>1</static>
      <link name='link'>
        <pose>0 0 0 0 -0 0</pose>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://gas_station/meshes/gas_station.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://gas_station/meshes/gas_station.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://gas_station/materials/scripts</uri>
              <uri>model://gas_station/materials/textures</uri>
              <name>GasStation/Diffuse</name>
            </script>
            <shader type='normal_map_tangent_space'>
              <normal_map>GasStation_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>37.9229 -11.9963 0 0 -0 0</pose>
    </model>
    <state world_name='default'>
      <sim_time>1505 650000000</sim_time>
      <real_time>68 351715267</real_time>
      <wall_time>1672308444 558990416</wall_time>
      <iterations>68210</iterations>
      <model name='Fast Food'>
        <pose>27.8838 37.5776 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>27.8838 37.5776 3.15931 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='Gas Station'>
        <pose>37.8517 -12.9062 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>37.8517 -12.9062 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='House 1'>
        <pose>-37.6007 7.76616 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-37.6007 7.76616 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='House 2'>
        <pose>-38.6325 -13.4947 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-38.6325 -13.4947 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='House 3'>
        <pose>-34.7103 -31.9403 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-34.7103 -31.9403 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='apartment'>
        <pose>0.512741 -19.9915 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>0.512741 -19.9915 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='gazebo'>
        <pose>-0.977582 37.9105 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-0.977582 37.9105 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='ground_plane'>
        <pose>0 0 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>0 0 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='law_office'>
        <pose>-37.4015 29.575 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-37.4015 29.575 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pier'>
        <pose>41.4778 1.63006 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>41.4778 1.63006 7.45747 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree'>
        <pose>-21.6069 -44.4628 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-21.6069 -44.4628 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_0'>
        <pose>-17.3854 -44.5523 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-17.3854 -44.5523 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone'>
        <pose>-19.3059 -41.8482 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-19.3059 -41.8482 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_0'>
        <pose>-14.5266 -40.6452 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-14.5266 -40.6452 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_1'>
        <pose>-13.4791 -44.6111 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-13.4791 -44.6111 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_10'>
        <pose>4.98862 -44.8266 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>4.98862 -44.8266 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_11'>
        <pose>8.05006 -41.6861 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>8.05006 -41.6861 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_12'>
        <pose>12.4611 -44.4022 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>12.4611 -44.4022 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_13'>
        <pose>13.1412 -40.3312 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>13.1412 -40.3312 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_14'>
        <pose>17.8531 -44.6577 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>17.8531 -44.6577 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_15'>
        <pose>21.3145 -41.4159 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>21.3145 -41.4159 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_16'>
        <pose>24.1125 -44.6069 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>24.1125 -44.6069 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_17'>
        <pose>28.0388 -39.5844 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>28.0388 -39.5844 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_18'>
        <pose>30.7167 -44.5628 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>30.7167 -44.5628 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_19'>
        <pose>36.0211 -45.5837 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>36.0211 -45.5837 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_20'>
        <pose>37.0511 -40.3664 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>37.0511 -40.3664 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_21'>
        <pose>41.8872 -43.1708 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>41.8872 -43.1708 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_22'>
        <pose>-40.8106 -43.1084 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-40.8106 -43.1084 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_3'>
        <pose>-7.64178 -41.2601 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-7.64178 -41.2601 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_4'>
        <pose>-35.6573 -44.7222 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-35.6573 -44.7222 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_5'>
        <pose>-31.4866 -44.8181 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-31.4866 -44.8181 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_6'>
        <pose>-27.3643 -45.0333 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-27.3643 -45.0333 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_7'>
        <pose>-5.36837 -44.7659 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-5.36837 -44.7659 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_8'>
        <pose>-2 -45 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-2 -45 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='pine_tree_clone_9'>
        <pose>1.58243 -41.817 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>1.58243 -41.817 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='post_office'>
        <pose>-18.7008 37.0491 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-18.7008 37.0491 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='washer'>
        <pose>-34.3528 -7.49095 -0.001 -3e-06 -3e-06 8e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-34.3528 -7.49095 -0 -3e-06 -3e-06 8e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0.003417 -0.000936 0.004564 -2.20512 0.275347 -3.14159</acceleration>
          <wrench>-4.2e-05 -1.1e-05 5.6e-05 0 -0 0</wrench>
        </link>
      </model>
      <light name='sun'>
        <pose>0 0 10 0 -0 0</pose>
      </light>
    </state>
    <gui fullscreen='0'>
      <camera name='user_camera'>
        <pose>57.4667 9.10336 99.0202 0 0.977799 -2.93498</pose>
        <view_controller>orbit</view_controller>
        <projection_type>perspective</projection_type>
      </camera>
    </gui>
    <plugin name="gazebo_ros_state" filename="libgazebo_ros_state.so">
      <ros>
        <namespace>/gazebo</namespace>
      </ros>
      <update_rate>10.0</update_rate>
    </plugin>
  </world>
</sdf>
