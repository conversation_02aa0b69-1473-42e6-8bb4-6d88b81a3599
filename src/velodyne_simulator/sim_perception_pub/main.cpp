#include <memory>

#include "custom_msgs/msg/perception_objects.hpp"
#include "gazebo_msgs/msg/model_states.hpp"
#include "gazebo_msgs/srv/get_entity_state.hpp"
#include "gazebo_msgs/srv/get_model_list.hpp"
#include "rclcpp/rclcpp.hpp"
#include "tf2/utils.h"
// gazebo_msgs/srv/GetEntityState
rclcpp::Publisher<custom_msgs::msg::PerceptionObjects>::SharedPtr objects_publisher;

void stateCallback(const gazebo_msgs::msg::ModelStates::SharedPtr msg)
{
    int size = msg->name.size();
    std::vector<std::pair<std::string, geometry_msgs::msg::Pose>> model_states;
    geometry_msgs::msg::Pose pose_chitu;
    for (int i = 0; i < size; i++) {
        if (msg->name[i].find("unit_box") != std::string::npos) model_states.push_back({msg->name[i], msg->pose[i]});
        if (msg->name[i].find("chitu") != std::string::npos) pose_chitu = msg->pose[i];
    }

    custom_msgs::msg::PerceptionObjects pub_msg;
    for (size_t i = 0; i < model_states.size(); i++) {
        auto &model_state = model_states[i];
        tf2::Transform pose_chitu_tf;
        pose_chitu_tf.setOrigin(tf2::Vector3(pose_chitu.position.x, pose_chitu.position.y, pose_chitu.position.z));
        pose_chitu_tf.setRotation(tf2::Quaternion(pose_chitu.orientation.x, pose_chitu.orientation.y,
                                                  pose_chitu.orientation.z, pose_chitu.orientation.w));
        tf2::Transform pose_model_tf;
        pose_model_tf.setOrigin(
            tf2::Vector3(model_state.second.position.x, model_state.second.position.y, model_state.second.position.z));
        pose_model_tf.setRotation(tf2::Quaternion(model_state.second.orientation.x, model_state.second.orientation.y,
                                                  model_state.second.orientation.z, model_state.second.orientation.w));
        tf2::Transform model_chitu_tf = pose_chitu_tf.inverse() * pose_model_tf;

        custom_msgs::msg::PerceptionObject msg;

        msg.pose.position.x = model_chitu_tf.getOrigin().x();
        msg.pose.position.y = model_chitu_tf.getOrigin().y();
        msg.pose.position.z = model_chitu_tf.getOrigin().z();
        msg.pose.orientation.w = model_chitu_tf.getRotation().w();
        msg.pose.orientation.x = model_chitu_tf.getRotation().x();
        msg.pose.orientation.y = model_chitu_tf.getRotation().y();
        msg.pose.orientation.z = model_chitu_tf.getRotation().z();
        msg.size.x = 1.0;
        msg.size.y = 1.0;
        msg.size.z = 1.0;
        msg.oc.classification = 1;
        msg.id = i;
        pub_msg.objects.push_back(msg);

        auto print = [](const tf2::Transform &tf, const std::string str) {
            RCLCPP_INFO(rclcpp::get_logger("rclcpp"), "%s pose:%f %f %f q:%f %f %f %f", str.c_str(), tf.getOrigin().x(),
                        tf.getOrigin().y(), tf.getOrigin().z(), tf.getRotation().w(), tf.getRotation().x(),
                        tf.getRotation().y(), tf.getRotation().z());
        };
        // print(pose_chitu_tf, "pose_chitu_tf");
        // print(pose_model_tf, "pose_model_tf");
        print(model_chitu_tf, model_state.first);
    }
    pub_msg.header.stamp = rclcpp::Clock().now();
    pub_msg.header.frame_id = "base_link";
    objects_publisher->publish(pub_msg);
}
int main(int argc, char **argv)
{
    rclcpp::init(argc, argv);

    std::shared_ptr<rclcpp::Node> node = rclcpp::Node::make_shared("sim_perception_pub");

    rclcpp::Client<gazebo_msgs::srv::GetEntityState>::SharedPtr client =
        node->create_client<gazebo_msgs::srv::GetEntityState>("/gazebo/get_entity_state");

    rclcpp::Client<gazebo_msgs::srv::GetModelList>::SharedPtr client_get_model =
        node->create_client<gazebo_msgs::srv::GetModelList>("/get_model_list");

    objects_publisher = node->create_publisher<custom_msgs::msg::PerceptionObjects>("/objects", 10);

    auto subscription = node->create_subscription<gazebo_msgs::msg::ModelStates>(
        "gazebo/model_states", 10, std::bind(&stateCallback, std::placeholders::_1));

    RCLCPP_INFO(rclcpp::get_logger("rclcpp"), "Ready Service Client.");

    // auto request = std::make_shared<gazebo_msgs::srv::GetEntityState::Request>();
    // request->name = "unit_box";
    // request->reference_frame = "chitu::base_link";

    // while (!client->wait_for_service(std::chrono::seconds(1))) {
    //     if (!rclcpp::ok()) {
    //         RCLCPP_ERROR(rclcpp::get_logger("rclcpp"), "Interrupted while waiting for the service. Exiting.");
    //         return 0;
    //     }
    //     RCLCPP_INFO(rclcpp::get_logger("rclcpp"), "service not available, waiting again...");
    // }
    // rclcpp::Rate rate(5.0);
    // while (rclcpp::ok()) {
    //     auto result = client->async_send_request(request);

    //     if (rclcpp::spin_until_future_complete(node, result) == rclcpp::FutureReturnCode::SUCCESS &&
    //         result.get()->success == true) {
    //         RCLCPP_INFO(rclcpp::get_logger("rclcpp"), "Pose: pos:[%f,%f,%f] qua:[%f,%f,%f,%f]",
    //                     result.get()->state.pose.position.x, result.get()->state.pose.position.y,
    //                     result.get()->state.pose.position.z, result.get()->state.pose.orientation.w,
    //                     result.get()->state.pose.orientation.x, result.get()->state.pose.orientation.y,
    //                     result.get()->state.pose.orientation.z);
    //         custom_msgs::msg::PerceptionObject msg;
    //         custom_msgs::msg::PerceptionObjects pub_msg;
    //         pub_msg.header.stamp = rclcpp::Clock().now();
    //         msg.pose.position.x = result.get()->state.pose.position.x;
    //         msg.pose.position.y = result.get()->state.pose.position.y;
    //         msg.pose.orientation = result.get()->state.pose.orientation;
    //         msg.size.x = 1.0;
    //         msg.size.y = 1.0;
    //         pub_msg.objects.push_back(msg);
    //         // publisher->publish(pub_msg);
    //         rclcpp::spin_some(node);
    //     } else {
    //         RCLCPP_ERROR(rclcpp::get_logger("rclcpp"), "Failed to call service");
    //     }
    //     rate.sleep();
    // }

    rclcpp::spin(node);
    rclcpp::shutdown();
}