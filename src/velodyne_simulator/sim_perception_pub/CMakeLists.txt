cmake_minimum_required(VERSION 3.5)
project(sim_perception_pub)

# Default to C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()
if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wpedantic")
endif()

find_package(ament_cmake REQUIRED)
find_package(gazebo_msgs REQUIRED)
find_package(rclcpp REQUIRED)
find_package(custom_msgs REQUIRED)
find_package(tf2 REQUIRED)

include_directories(include)

add_executable(sim_perception_pub main.cpp)
ament_target_dependencies(sim_perception_pub
  "gazebo_msgs"
  "rclcpp"
  "custom_msgs"
  "tf2"
)

ament_export_dependencies(rclcpp)
ament_export_dependencies(gazebo_msgs)
ament_export_dependencies(custom_msgs)
ament_export_dependencies(tf2)

ament_package()

install(TARGETS
sim_perception_pub
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION lib/${PROJECT_NAME}
)
