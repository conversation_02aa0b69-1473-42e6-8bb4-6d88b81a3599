^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Changelog for package velodyne_simulator
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

2.0.2 (2021-12-27)
------------------

2.0.1 (2021-10-14)
------------------
* Remove unused ament_lint_auto that was causing build error
* Contributors: <PERSON>

2.0.0 (2021-10-12)
------------------
* Merged in ROS2 Foxy support (pull request #14)
  * Remove support for old Gazebo versions
  * Lazy subscriber using timer in the absence of publisher connection callback
  * Python launch file
  * Add env-hooks for GAZEBO_MODEL_PATH
* Contributors: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>

1.0.12 (2021-03-30)
-------------------

1.0.11 (2021-02-23)
-------------------

1.0.10 (2020-08-03)
-------------------
* Bump minimum CMake version to 3.0.2 in all CMakeLists.txt
* Contributors: Micho Radovnikovich

1.0.9 (2019-03-08)
------------------

1.0.8 (2018-09-08)
------------------

1.0.7 (2018-07-03)
------------------

1.0.6 (2017-10-17)
------------------

1.0.5 (2017-09-05)
------------------

1.0.4 (2017-04-24)
------------------
* Updated package.xml format to version 2
* Contributors: Kevin Hallenbeck

1.0.3 (2016-08-13)
------------------
* Contributors: Kevin Hallenbeck

1.0.2 (2016-02-03)
------------------
* Contributors: Kevin Hallenbeck
