cmake_minimum_required(VERSION 3.5)
project(webots_objects_msgs)

# Default to C99
if(NOT CMAKE_C_STANDARD)
  set(CMAKE_C_STANDARD 99)
endif()

# Default to C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()
# find dependencies
find_package(ament_cmake REQUIRED)
find_package(custom_msgs REQUIRED)
find_package(std_msgs REQUIRED)
find_package(rosidl_default_generators REQUIRED)

set(srv_files
"srv/UpdateObjects.srv"
)


rosidl_generate_interfaces(${PROJECT_NAME}
	${msg_files}
	${srv_files}
  DEPENDENCIES builtin_interfaces std_msgs custom_msgs
)

ament_package()
