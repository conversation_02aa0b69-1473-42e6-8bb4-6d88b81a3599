#include "tool.hpp"
#include <jsoncpp/json/value.h>
#include <jsoncpp/json/writer.h>
#include <rviz_common/display_context.hpp>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>

namespace simulation_tool_plugin {
ObjectTool::ObjectTool()
{
    property_frame_ = new rviz_common::properties::TfFrameProperty(
        "Target Frame", "base_link", "The TF frame where the topic is output.", getPropertyContainer(), nullptr, true);

    topic_property_ = new rviz_common::properties::StringProperty("Pose Topic", "/wb_simulated_obstacle",
                                                                  "The topic on which to publish dummy object info.",
                                                                  getPropertyContainer(), SLOT(updateTopic()), this);

    id_ = new rviz_common::properties::IntProperty("ID", -1, "ID for simulated object", getPropertyContainer());

    length_ = new rviz_common::properties::FloatProperty("L vehicle length", 1.5, "L length for vehicle [m]",
                                                         getPropertyContainer());
    width_ = new rviz_common::properties::FloatProperty("W vehicle width", 0.5, "W width for vehicle [m]",
                                                        getPropertyContainer());
    height_ = new rviz_common::properties::FloatProperty("H vehicle height", 1.0, "H height for vehicle [m]",
                                                         getPropertyContainer());
    // position_z_ = new rviz_common::properties::FloatProperty("Z position", 0.0, "Z position for initial pose [m]",
    //                                                          getPropertyContainer());
    velocity_ = new rviz_common::properties::FloatProperty("Velocity", 0.0, "velocity [m/s]", getPropertyContainer());
    // accel_ =
    //     new rviz_common::properties::FloatProperty("Acceleration", 0.0, "acceleration [m/s^2]", getPropertyContainer());
    // max_velocity_ =
    //     new rviz_common::properties::FloatProperty("Max velocity", 33.3, "Max velocity [m/s]", getPropertyContainer());
    // min_velocity_ =
    //     new rviz_common::properties::FloatProperty("Min velocity", -33.3, "Min velocity [m/s]", getPropertyContainer());

    width_->setMin(0);
    length_->setMin(0);
    height_->setMin(0);
}
void ObjectTool::onInitialize()
{
    PoseTool::onInitialize();
    setName("Simulated Object");
    updateTopic();
}
std::string ObjectTool::getFixedFrame() const
{
    return context_->getFrameManager()->getFixedFrame().c_str();
}

void ObjectTool::updateTopic()
{
    rclcpp::Node::SharedPtr raw_node = context_->getRosNodeAbstraction().lock()->get_raw_node();
    //   dummy_object_info_pub_ = raw_node->create_publisher<Object>(topic_property_->getStdString(), 1);
    clock_ = raw_node->get_clock();
    move_tool_.initialize(context_);
    property_frame_->setFrameManager(context_->getFrameManager());

    obstacle_pub_ = raw_node->create_publisher<std_msgs::msg::String>(topic_property_->getStdString(), 10);
    // tf_listener_ = std::make_shared<tf2_ros::TransformListener>(*buffer_, raw_node, false);
}

void ObjectTool::onPoseSet(double x, double y, double theta)
{
    // if (enable_interactive_property_->getBool()) {
    //     return;
    // }
    std::cout << "\033[1;33m ObjectTool: click x:" << x << " y:" << y << " theta:" << theta << " size_x"
              << length_->getFloat() << " size_y:" << width_->getFloat() << " id:" << id_->getInt() << "\033[0m"
              << std::endl;

    geometry_msgs::msg::PoseStamped frame_pos;
    frame_pos.header.frame_id = getFixedFrame();
    frame_pos.pose.position.x = x;
    frame_pos.pose.position.y = y;
    frame_pos.pose.position.z = 0.0;
    tf2::Quaternion orientation_tf;
    orientation_tf.setRPY(0.0, 0.0, theta);
    frame_pos.pose.orientation = tf2::toMsg(orientation_tf);

    geometry_msgs::msg::PoseStamped target_pos;
    if (frame_pos.header.frame_id == property_frame_->getFrameStd())
        target_pos = frame_pos;
    else {
        try {
            target_pos = property_frame_->getFrameManager()->getTransformer()->transform(
                frame_pos, property_frame_->getFrameStd());
            // std::cout << "frame_pos:" << frame_pos.pose.position.x << " " << frame_pos.pose.position.y << std::endl;
            // std::cout << "target_pos:" << target_pos.pose.position.x << " " << target_pos.pose.position.y << std::endl;
        } catch (tf2::TransformException &exception) {
            std::cout << "ObjectTool transform error:" << exception.what() << std::endl;
            return;
        }
    };

    Json::Value root;
    // root["title"] = "add_obstacle";
    root["id"] = id_->getInt();
    root["size"]["x"] = length_->getFloat();
    root["size"]["y"] = width_->getFloat();
    root["size"]["z"] = height_->getFloat();
    root["position"]["x"] = target_pos.pose.position.x;
    root["position"]["y"] = target_pos.pose.position.y;

    tf2::Quaternion target_oritation_tf;
    tf2::fromMsg(target_pos.pose.orientation, target_oritation_tf);
    tf2::Matrix3x3 m(target_oritation_tf);
    double roll, pitch, yaw;
    m.getRPY(roll, pitch, yaw);
    root["position"]["theta"] = yaw;
    root["velocity"] = velocity_->getFloat();

    Json::FastWriter writer;
    std_msgs::msg::String msg;
    msg.data = writer.write(root);
    obstacle_pub_->publish(msg);
}

}  // namespace simulation_tool_plugin

#include <pluginlib/class_list_macros.hpp>
PLUGINLIB_EXPORT_CLASS(simulation_tool_plugin::ObjectTool, rviz_common::Tool)