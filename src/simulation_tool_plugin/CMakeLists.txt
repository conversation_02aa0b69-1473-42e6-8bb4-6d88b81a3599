cmake_minimum_required(VERSION 3.14)
project(simulation_tool_plugin)

find_package(ament_cmake_auto REQUIRED)
find_package(ament_cmake REQUIRED)
find_package(PkgConfig REQUIRED)
pkg_check_modules(<PERSON><PERSON><PERSON><PERSON> jsoncpp)

ament_auto_find_build_dependencies()

find_package(Qt5 REQUIRED Core Widgets)
set(QT_LIBRARIES Qt5::Widgets)

# set(CMAKE_AUTOMOC ON)
# set(CMAKE_INCLUDE_CURRENT_DIR ON)

qt5_wrap_cpp(MOC_FILES include/tool.hpp)

## Declare a C++ library
add_library(${PROJECT_NAME} SHARED
${MOC_FILES}
  src/tool.cpp
)

target_include_directories(${PROJECT_NAME} PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include/${PROJECT_NAME}>
  ${Qt5Widgets_INCLUDE_DIRS}
  ${JSONCPP_INCLUDE_DIRS}
)


target_link_libraries(${PROJECT_NAME} PUBLIC
  ${QT_LIBRARIES}
  ${JSONCPP_LIBRARIES})


ament_target_dependencies(${PROJECT_NAME}
PUBLIC
rclcpp
rviz_common
rviz_default_plugins
)


  install(
  TARGETS ${PROJECT_NAME}
  EXPORT simulation_tool_plugin
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)


# Export the plugin to be imported by rviz2
pluginlib_export_plugin_description_file(rviz_common plugins/plugin_description.xml)

ament_auto_package(
  INSTALL_TO_SHARE
  plugins
)