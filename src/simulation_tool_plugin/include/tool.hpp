#ifndef TOOLS__INTERACTIVE_OBJECT_HPP_
#define TOOLS__INTERACTIVE_OBJECT_HPP_

// #ifndef Q_MOC_RUN  // See: https://bugreports.qt-project.org/browse/QTBUG-22829
#include <tf2_ros/buffer.h>
#include <tf2_ros/transform_listener.h>
#include <QObject>
#include <rclcpp/node.hpp>
#include <rviz_common/properties/bool_property.hpp>
#include <rviz_common/properties/enum_property.hpp>
#include <rviz_common/properties/float_property.hpp>
#include <rviz_common/properties/int_property.hpp>
#include <rviz_common/properties/string_property.hpp>
#include <rviz_common/properties/tf_frame_property.hpp>
#include <rviz_common/viewport_mouse_event.hpp>
#include <rviz_default_plugins/tools/move/move_tool.hpp>
#include <rviz_default_plugins/tools/pose/pose_tool.hpp>
#include <std_msgs/msg/string.hpp>
// #endif

namespace simulation_tool_plugin {
class ObjectTool : public rviz_default_plugins::tools::PoseTool
{
    Q_OBJECT

public:
    ObjectTool();
    void onInitialize() override;
    // int processMouseEvent(rviz_common::ViewportMouseEvent &event) override;
    // int processKeyEvent(QKeyEvent *event, rviz_common::RenderPanel *panel) override;

    //   [[nodiscard]] virtual Object createObjectMsg() const = 0;

protected Q_SLOTS:
    virtual void updateTopic();

protected:  // NOLINT for Qt
    rclcpp::Clock::SharedPtr clock_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr obstacle_pub_;

    rviz_default_plugins::tools::MoveTool move_tool_;

    rviz_common::properties::TfFrameProperty *property_frame_;
    rviz_common::properties::StringProperty *topic_property_;
    rviz_common::properties::IntProperty *id_;
    rviz_common::properties::FloatProperty *width_;
    rviz_common::properties::FloatProperty *length_;
    rviz_common::properties::FloatProperty *height_;
    rviz_common::properties::FloatProperty *position_z_;
    rviz_common::properties::FloatProperty *velocity_;
    // rviz_common::properties::FloatProperty *accel_;
    // rviz_common::properties::FloatProperty *max_velocity_;
    // rviz_common::properties::FloatProperty *min_velocity_;

private:
    //   void publishObjectMsg(const std::array<uint8_t, 16> & uuid, const uint32_t action);
    void onPoseSet(double x, double y, double theta) override;

    std::string getFixedFrame() const;
};
}  // namespace simulation_tool_plugin
#endif  // TOOLS__INTERACTIVE_OBJECT_HPP_
