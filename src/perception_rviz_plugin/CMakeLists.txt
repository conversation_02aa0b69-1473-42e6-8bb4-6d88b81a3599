cmake_minimum_required(VERSION 3.5)
project(perception_rviz_plugin)

#dependencies
find_package(ament_cmake_auto REQUIRED)
find_package(Qt5 REQUIRED COMPONENTS Widgets)
ament_auto_find_build_dependencies()

set(OD_PLUGIN_LIB_SRC
  src/perception_objects_display.cpp
)

# set(OD_PLUGIN_LIB_HEADERS
#   include/visibility_control.hpp
# )
set(OD_PLUGIN_LIB_HEADERS_TO_WRAP
  #include/planning/trajectory_display.hpp
  include/perception_objects_display.hpp
  include/object_mesh_visual.hpp
  include/car_mesh_visual.hpp
  include/person_mesh_visual.hpp
  include/truck_mesh_visual.hpp
  #include/object_detection/detected_objects_display.hpp
  #include/object_detection/tracked_objects_display.hpp
)

set(COMMON_HEADERS
#   include/common/color_alpha_property.hpp
#   include/object_detection/object_polygon_detail.hpp
#   include/object_detection/object_polygon_display_base.hpp
)

set(COMMON_SRC
#   src/common/color_alpha_property.cpp
#   src/object_detection/object_polygon_detail.cpp
)

foreach(header "${OD_PLUGIN_LIB_HEADERS_TO_WRAP}")
  qt5_wrap_cpp(OD_PLUGIN_LIB_HEADERS_MOC "${header}")
endforeach()

ament_auto_add_library(${PROJECT_NAME} SHARED
#   ${COMMON_HEADERS}
#   ${COMMON_SRC}
#   ${OD_PLUGIN_LIB_HEADERS}
  ${OD_PLUGIN_LIB_HEADERS_MOC}
  ${OD_PLUGIN_LIB_SRC}
)
target_link_libraries(${PROJECT_NAME}
  rviz_common::rviz_common
  Qt5::Widgets
)
target_include_directories(${PROJECT_NAME} INTERFACE include)
#autoware_set_compile_options(${PROJECT_NAME})

# Settings to improve the build as suggested on https://github.com/ros2/rviz/blob/ros2/docs/plugin_development.md
target_compile_definitions(${PROJECT_NAME} PUBLIC "PLUGINLIB__DISABLE_BOOST_FUNCTIONS")
target_compile_definitions(${PROJECT_NAME} PRIVATE "OBJECT_DETECTION_PLUGINS_BUILDING_LIBRARY")

# Export the plugin to be imported by rviz2
pluginlib_export_plugin_description_file(rviz_common plugins_description.xml)

register_rviz_ogre_media_exports(DIRECTORIES 
 "media"
)

if(BUILD_TESTING)
  # run linters
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

# Export the icons for the plugins
ament_auto_package(INSTALL_TO_SHARE icons)
