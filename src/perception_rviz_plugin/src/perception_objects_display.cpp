/**
 *
 *   @file perception_objects_display.cpp
 *   <AUTHOR> (<EMAIL>)
 *   @brief
 *   @version 0.1
 *   @date 2023-02-07
 *
 *   (C) 2023 UBTECH.Co.Ltd. All rights reserved
 *
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF ME<PERSON>HANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include <perception_objects_display.hpp>

namespace perception_rviz_plugin {

// Constructor
PerceptionObjectsDisplay::PerceptionObjectsDisplay() :
    Node("perception_rviz_plugin"),
    rviz_common::RosTopicDisplay<PerceptionObjects>(),
    m_marker_common(std::make_unique<MarkerCommon>(this)),
    is_destroying_(false)
{
    unknown_color_property_ = new rviz_common::properties::ColorProperty("Unknown Color", QColor(255.0, 255.0, 255.0),
                                                                         "Color to draw unlabelled boundingboxes.",
                                                                         this, SLOT(updateProperty()));
    car_color_property_ = new rviz_common::properties::ColorProperty(
        "Car Color", QColor(255.0, 255.0, 0), "Color to draw car boundingboxes.", this, SLOT(updateProperty()));
    truck_color_property_ = new rviz_common::properties::ColorProperty(
        "Truck Color", QColor(255.0, 255.0, 0), "Color to draw truck boundingboxes.", this, SLOT(updateProperty()));
    trailer_color_property_ = new rviz_common::properties::ColorProperty(
        "Trailer Color", QColor(255.0, 255.0, 0), "Color to draw trailer boundingboxes.", this, SLOT(updateProperty()));
    bus_color_property_ = new rviz_common::properties::ColorProperty(
        "Bus Color", QColor(255.0, 255.0, 0), "Color to draw trailer boundingboxes.", this, SLOT(updateProperty()));
    pedestrian_color_property_ = new rviz_common::properties::ColorProperty(
        "Pedestrian Color", QColor(255.0, 0, 0), "Color to draw pedestrian boundingboxes.", this, SLOT(updateProperty()));
    bicycle_color_property_ = new rviz_common::properties::ColorProperty(
        "Bicycle Color", QColor(255.0, 165.0, 0), "Color to draw bicycle boundingboxes.", this, SLOT(updateProperty()));
    motorcycle_color_property_ = new rviz_common::properties::ColorProperty("Motorcycle Color", QColor(102, 153.0, 153),
                                                                            "Color to draw motorcycle boundingboxes.",
                                                                            this, SLOT(updateProperty()));
    shelves_color_property_ = new rviz_common::properties::ColorProperty(
        "Shelves Color", QColor(0, 255.0, 0), "Color to draw shelves boundingboxes.", this, SLOT(updateProperty()));
    goods_color_property_ = new rviz_common::properties::ColorProperty(
        "Goods Color", QColor(255, 204.0, 255), "Color to draw goods boundingboxes.", this, SLOT(updateProperty()));
    traffic_light_color_property_ = new rviz_common::properties::ColorProperty(
        "Traffic light Color", QColor(0, 255.0, 0), "Color to draw traffic light boundingboxes.", this,
        SLOT(updateProperty()));
    forklift_color_property_ = new rviz_common::properties::ColorProperty(
        "Forklift Color", QColor(51, 102.0, 255), "Color to draw fork lift boundingboxes.", this, SLOT(updateProperty()));
    agv_color_property_ = new rviz_common::properties::ColorProperty(
        "Agv Color", QColor(255, 51.0, 204), "Color to draw agv boundingboxes.", this, SLOT(updateProperty()));
    other_color_property_ = new rviz_common::properties::ColorProperty(
        "Other Color", QColor(0, 0, 0), "Color to draw other boundingboxes.", this, SLOT(updateProperty()));
    alpha_property_ = new rviz_common::properties::FloatProperty(
        "Alpha", 0.7f, "Amount of transparency to apply to the boundingbox.", this, SLOT(updateProperty()));
    alpha_property_->setMin(0);
    alpha_property_->setMax(1);

    style_property_ = new rviz_common::properties::EnumProperty(
        "Style", "Bounding Box", "Rendering mode to use, in order of computational complexity.", this,
        SLOT(updateProperty()));
    style_property_->addOption("Bounding Box", STYLE_BOUNDING_BOX);
    style_property_->addOption("Mesh", STYLE_MESH);

    show_id_property_ = new rviz_common::properties::BoolProperty("show id", false, "show the id marker text or not",
                                                                  this, SLOT(updateProperty()));
    show_confidence_property_ = new rviz_common::properties::BoolProperty(
        "show confidence", false, "show the confidence marker text or not", this, SLOT(updateProperty()));
    show_predicted_path_property_ = new rviz_common::properties::BoolProperty(
        "show predicted path", false, "show the predicted path marker or not", this, SLOT(updateProperty()));
    show_obs_vel_property_ = new rviz_common::properties::BoolProperty(
        "show obs vel", false, "show the obs vel marker or not", this, SLOT(updateProperty()));
    show_chitu_car_property_ = new rviz_common::properties::BoolProperty(
        "show chitu car", false, "show the predicted path marker or not", this, SLOT(updateProperty()));
    chitu_mesh_visual_ = NULL;
}

PerceptionObjectsDisplay::~PerceptionObjectsDisplay()
{
    is_destroying_ = true;
    if (chitu_mesh_visual_) {
        delete chitu_mesh_visual_;
    }
    fixed_frame_transform_thread_.join();
}

void PerceptionObjectsDisplay::fixedFrameTransformThread()
{
    tf_buffer_ = std::make_unique<tf2_ros::Buffer>(this->get_clock());
    tf_listener_ = std::make_shared<tf2_ros::TransformListener>(*tf_buffer_);
    geometry_msgs::msg::TransformStamped fixed_frame_to_baselink_msg;
    geometry_msgs::msg::TransformStamped fixed_frame_to_top_lidar_msg;
    tf2::Transform baselink_to_mesh;
    baselink_to_mesh.setOrigin(tf2::Vector3(1.05, 0, -0.2835));
    baselink_to_mesh.setRotation(tf2::Quaternion::getIdentity());

    bool getTransform = false;
    while (getTransform == false) {
        try {
            fixed_frame_to_baselink_msg = tf_buffer_->lookupTransform("base_link", "base_link", tf2::TimePointZero);
            fixed_frame_to_top_lidar_msg = tf_buffer_->lookupTransform("base_link", "laser_link_top", tf2::TimePointZero);
            getTransform = true;
        } catch (const tf2::TransformException &ex) {
            auto &clk = *this->get_clock();
            RCLCPP_WARN_THROTTLE(this->get_logger(), clk, 2000, "Could not transform to base_link: %s", ex.what());
        }
    }

    tf2::Stamped<tf2::Transform> transform_fixed_frame_to_baselink;
    tf2::fromMsg(fixed_frame_to_baselink_msg, transform_fixed_frame_to_baselink);
    tf2::fromMsg(fixed_frame_to_top_lidar_msg, transform_fixed_frame_to_top_lidar_);
    tf2::Transform transform_fixed_frame_to_mesh = transform_fixed_frame_to_baselink * baselink_to_mesh;
    Ogre::Vector3 fixed_frame_to_mesh_pos(transform_fixed_frame_to_mesh.getOrigin().x(),
                                          transform_fixed_frame_to_mesh.getOrigin().y(),
                                          transform_fixed_frame_to_mesh.getOrigin().z());
    chitu_mesh_visual_->setPosition(fixed_frame_to_mesh_pos);
    Ogre::Quaternion fixed_frame_to_mesh_orien(
        transform_fixed_frame_to_mesh.getRotation().w(), transform_fixed_frame_to_mesh.getRotation().x(),
        transform_fixed_frame_to_mesh.getRotation().y(), transform_fixed_frame_to_mesh.getRotation().z());
    chitu_mesh_visual_->setOrientation(fixed_frame_to_mesh_orien);

    // rclcpp::WallRate loop_rate(30);
    // while(false == is_destroying_){
    //     std::string fixed_frame = fixed_frame_.toStdString();
    //     if(fixed_frame.empty()){
    //         continue;
    //     }
    //     try {
    //         fixed_frame_to_baselink_msg = tf_buffer_->lookupTransform(fixed_frame, "base_link", tf2::TimePointZero);
    //         fixed_frame_to_top_lidar_msg = tf_buffer_->lookupTransform(fixed_frame, "laser_link_top", tf2::TimePointZero);
    //         tf2::Stamped<tf2::Transform> transform_fixed_frame_to_baselink;
    //         tf2::fromMsg(fixed_frame_to_baselink_msg, transform_fixed_frame_to_baselink);
    //         tf2::fromMsg(fixed_frame_to_top_lidar_msg, transform_fixed_frame_to_top_lidar_);
    //         tf2::Transform transform_fixed_frame_to_mesh = transform_fixed_frame_to_baselink * baselink_to_mesh;
    //         Ogre::Vector3 fixed_frame_to_mesh_pos(transform_fixed_frame_to_mesh.getOrigin().x(), transform_fixed_frame_to_mesh.getOrigin().y(), transform_fixed_frame_to_mesh.getOrigin().z());
    //         chitu_mesh_visual_->setPosition(fixed_frame_to_mesh_pos);
    //         Ogre::Quaternion fixed_frame_to_mesh_orien(transform_fixed_frame_to_mesh.getRotation().w(), transform_fixed_frame_to_mesh.getRotation().x(),
    //         transform_fixed_frame_to_mesh.getRotation().y(), transform_fixed_frame_to_mesh.getRotation().z());
    //         chitu_mesh_visual_->setOrientation(fixed_frame_to_mesh_orien);
    //     } catch (const tf2::TransformException & ex) {
    //         auto& clk = *this->get_clock();
    //         RCLCPP_WARN_THROTTLE(this->get_logger(), clk, 2000, "Could not transform %s to base_link: %s", fixed_frame.c_str(), ex.what());
    //     }
    //     loop_rate.sleep();
    // }
}

void PerceptionObjectsDisplay::onInitialize()
{
    RTDClass::onInitialize();
    m_marker_common->initialize(context_, scene_node_);

    topic_property_->setValue("/uslam/module/percetion/perception_objects");
    topic_property_->setDescription("PerceptionObjects topic to subscribe to.");
}

void PerceptionObjectsDisplay::load(const rviz_common::Config &config)
{
    Display::load(config);
    m_marker_common->load(config);
}

void PerceptionObjectsDisplay::updateProperty()
{
    // auto logger = rclcpp::get_logger("rviz2");
    // RCLCPP_INFO(logger, "updateProperty!!");

    if (style_property_->getOptionInt() != STYLE_MESH) {
        for (std::map<uint32_t, ObjectMeshVisual *>::iterator iter = object_track_cached_.begin();
             iter != object_track_cached_.end(); iter++) {
            iter->second->setVisible(false);
        }
    }

    if (style_property_->getOptionInt() == STYLE_MESH) {
        for (std::map<uint32_t, ObjectMeshVisual *>::iterator iter = object_track_cached_.begin();
             iter != object_track_cached_.end(); iter++) {
            iter->second->setVisible(true);
        }
    }

    if (style_property_->getOptionInt() != STYLE_BOUNDING_BOX) {
        visualization_msgs::msg::Marker::SharedPtr marker_tmp = std::make_shared<Marker>();
        marker_tmp->action = Marker::DELETEALL;
        marker_tmp->ns = "bounding_box";
        // marker_ptr->header = msg->header;
        // marker_ptr->id = static_cast<int>(idx);
        m_marker_common->addMessage(marker_tmp);
    }

    if (show_chitu_car_property_->getBool()) {
        if (chitu_mesh_visual_) {
            chitu_mesh_visual_->setVisible(true);
        } else {
            chitu_mesh_visual_ = new ChituMeshVisual(scene_manager_, scene_node_, std::string("chitu_car"));
            fixed_frame_transform_thread_ = std::thread(&PerceptionObjectsDisplay::fixedFrameTransformThread, this);
        }
    } else {
        if (chitu_mesh_visual_) {
            chitu_mesh_visual_->setVisible(false);
        }
    }

    if (msg_cache_ != nullptr) {
        processMessage(msg_cache_);
    }
}

QColor PerceptionObjectsDisplay::getColor(int classification) const
{
    QColor color;
    switch (classification) {
        case ObjectClassification::UNKNOWN:  // white: unknown
            color = unknown_color_property_->getColor();
            break;
        case ObjectClassification::CAR:  // yellow: car
            color = car_color_property_->getColor();
            break;
        case ObjectClassification::TRUCK:  // yellow: car
            color = truck_color_property_->getColor();
            break;

        case ObjectClassification::BUS:  // yellow: car
            color = bus_color_property_->getColor();
            break;

        case ObjectClassification::TRAILER:  // yellow: car
            color = trailer_color_property_->getColor();
            break;

        case ObjectClassification::MOTORCYCLE:  // green: motorcycle
            color = motorcycle_color_property_->getColor();

        case ObjectClassification::BICYCLE:  // orange: bicycle
            color = bicycle_color_property_->getColor();
            break;

        case ObjectClassification::PEDESTRIAN:  // blue: pedestrian
            color = pedestrian_color_property_->getColor();
            break;

        default:  // black: other labels
            color = other_color_property_->getColor();
            break;
    }
    return color;
}

visualization_msgs::msg::Marker::SharedPtr PerceptionObjectsDisplay::getPredictedPathMarker(const PerceptionObject &object) const
{
    auto marker = std::make_shared<Marker>();
    marker->type = Marker::LINE_STRIP;
    marker->action = Marker::ADD;
    marker->color.a = alpha_property_->getFloat();
    QColor color = getColor(object.oc.classification);
    marker->color.r = static_cast<float>(color.redF());
    marker->color.g = static_cast<float>(color.greenF());
    marker->color.b = static_cast<float>(color.blueF());
    marker->scale.x = object.size.y / 3.0;
    printf("%s:%d\n", __FUNCTION__, __LINE__);

    if (object.path.size() <= 0) {
        return marker;
    }

    printf("%s:%d\n", __FUNCTION__, __LINE__);
    for (int i = 0; i < object.path[0].path.size(); i++) {
        geometry_msgs::msg::Point p;
        p.x = object.path[0].path[i].pose.position.x;
        p.y = object.path[0].path[i].pose.position.y;
        p.z = object.path[0].path[i].pose.position.z;
        marker->points.push_back(p);
    }
    return marker;
}

visualization_msgs::msg::Marker::SharedPtr PerceptionObjectsDisplay::getObsVelMarker(const PerceptionObject &object) const
{
    auto marker = std::make_shared<Marker>();
    marker->type = Marker::TEXT_VIEW_FACING;
    marker->action = Marker::ADD;
    marker->color.a = alpha_property_->getFloat();
    QColor color = getColor(object.oc.classification);
    marker->color.r = 1.0;
    marker->color.g = 0.0;
    marker->color.b = 0.0;
    marker->scale.x = 1.0;
    marker->scale.y = 1.0;
    marker->scale.z = 1.0;

    double speed_x = object.twist.linear.x;
    double speed_y = object.twist.linear.y;
    double angular_z = object.twist.angular.z;
    double speed = std::sqrt(speed_x * speed_x + speed_y * speed_y);

    marker->pose.position = object.pose.position;
    tf2::Quaternion q;
    q.setRPY(0, 0, angular_z);
    marker->pose.orientation.w = q.w();
    marker->pose.orientation.x = q.x();
    marker->pose.orientation.y = q.y();
    marker->pose.orientation.z = q.z();
    std::stringstream ss;
    ss << std::fixed << std::setprecision(1) << speed;
    marker->text = ss.str();

    if (speed < 1e-3) {
        // 速度太小不显示
        marker->scale.x = 0;
        marker->scale.y = 0;
        marker->scale.z = 0;
        return marker;
    }

    return marker;
}

visualization_msgs::msg::Marker::SharedPtr PerceptionObjectsDisplay::getBoundingBoxMarker(const PerceptionObject &object) const
{
    auto marker = std::make_shared<Marker>();

    marker->type = Marker::CUBE;
    marker->action = Marker::ADD;
    marker->color.a = alpha_property_->getFloat();

    QColor color = getColor(object.oc.classification);

    marker->color.r = static_cast<float>(color.redF());
    marker->color.g = static_cast<float>(color.greenF());
    marker->color.b = static_cast<float>(color.blueF());
    marker->pose.position.x = static_cast<double>(object.pose.position.x);
    marker->pose.position.y = static_cast<double>(object.pose.position.y);
    marker->pose.position.z = static_cast<double>(object.pose.position.z);
    marker->pose.orientation.x = static_cast<double>(object.pose.orientation.x);
    marker->pose.orientation.y = static_cast<double>(object.pose.orientation.y);
    marker->pose.orientation.z = static_cast<double>(object.pose.orientation.z);
    marker->pose.orientation.w = static_cast<double>(object.pose.orientation.w);
    marker->scale.x = static_cast<double>(object.size.x);
    marker->scale.y = static_cast<double>(object.size.y);
    marker->scale.z = static_cast<double>(object.size.z);

    return marker;
}

visualization_msgs::msg::Marker::SharedPtr PerceptionObjectsDisplay::getIdTextMarker(const PerceptionObject &object) const
{
    auto marker = std::make_shared<visualization_msgs::msg::Marker>();

    marker->type = Marker::TEXT_VIEW_FACING;
    marker->action = Marker::ADD;
    marker->color.a = 1.0;
    marker->color.r = 1.0;
    marker->color.g = 0.0;
    marker->color.b = 0.0;
    marker->pose.position.x = static_cast<double>(object.pose.position.x);
    marker->pose.position.y = static_cast<double>(object.pose.position.y);
    marker->pose.position.z = static_cast<double>(object.pose.position.z + object.size.z + 1);
    marker->pose.orientation.z = 0;
    marker->pose.orientation.w = 1;
    marker->scale.x = 1.5;
    marker->scale.y = 1.5;
    marker->scale.z = 1.5;
    marker->text = std::to_string(object.id);
    return marker;
}

visualization_msgs::msg::Marker::SharedPtr PerceptionObjectsDisplay::getConfidenceTextMarker(const PerceptionObject &object) const
{
    auto marker = std::make_shared<visualization_msgs::msg::Marker>();

    marker->type = Marker::TEXT_VIEW_FACING;
    marker->action = Marker::ADD;
    marker->color.a = 1.0;
    marker->color.r = 1.0;
    marker->color.g = 0.0;
    marker->color.b = 1.0;
    marker->pose.position.x = static_cast<double>(object.pose.position.x);
    marker->pose.position.y = static_cast<double>(object.pose.position.y);
    marker->pose.position.z = static_cast<double>(object.pose.position.z + object.size.z);
    marker->pose.orientation.z = 0;
    marker->pose.orientation.w = 1;
    marker->scale.x = 1.5;
    marker->scale.y = 1.5;
    marker->scale.z = 1.5;
    marker->text = std::to_string(object.oc.probability).substr(0, 4);
    return marker;
}

void PerceptionObjectsDisplay::processObject(const PerceptionObject &obj)
{
    uint32_t id = obj.id;
    if (object_track_cached_.find(id) == object_track_cached_.end()) {
        //新发现的
        ObjectFactory objectFactory;
        ObjectMeshVisual *object_mesh_visual =
            objectFactory.createObject(obj, scene_manager_, scene_node_, transform_fixed_frame_to_top_lidar_);
        if (object_mesh_visual == NULL) {
            return;
        }
        object_track_cached_.insert(std::make_pair(id, object_mesh_visual));
    } else {
        //已经跟踪的
        ObjectMeshVisual *object_mesh_visual = object_track_cached_[id];

        tf2::Transform perception_frame_to_object;
        perception_frame_to_object.setOrigin(tf2::Vector3(obj.pose.position.x, obj.pose.position.y, obj.pose.position.z));
        perception_frame_to_object.setRotation(tf2::Quaternion(obj.pose.orientation.x, obj.pose.orientation.y,
                                                               obj.pose.orientation.z, obj.pose.orientation.w));

        tf2::Transform fixed_frame_to_object = transform_fixed_frame_to_top_lidar_ * perception_frame_to_object;

        Ogre::Vector3 position(fixed_frame_to_object.getOrigin().x(), fixed_frame_to_object.getOrigin().y(),
                               fixed_frame_to_object.getOrigin().z() - obj.size.z / 2.0);
        object_mesh_visual->setPosition(position);
        std::cout << "tracked object, x, y, z: " << position.x << " " << position.y << " " << position.z << std::endl;
        Ogre::Quaternion orientation(fixed_frame_to_object.getRotation().w(), fixed_frame_to_object.getRotation().x(),
                                     fixed_frame_to_object.getRotation().y(), fixed_frame_to_object.getRotation().z());
        object_mesh_visual->setOrientation(orientation);
    }
}

void PerceptionObjectsDisplay::processMessage(PerceptionObjects::ConstSharedPtr msg)
{
    msg_cache_ = msg;

    for (auto idx = 0U; idx < msg->objects.size(); idx++) {
        switch (style_property_->getOptionInt()) {
            case STYLE_BOUNDING_BOX: {
                m_marker_common->clearMarkers();
                const auto marker_ptr = getBoundingBoxMarker(msg->objects[idx]);
                marker_ptr->ns = "bounding_box";
                marker_ptr->header = msg->header;
                marker_ptr->id = static_cast<int>(idx);
                m_marker_common->addMessage(marker_ptr);

            } break;
            case STYLE_MESH: {
                m_marker_common->clearMarkers();
                processObject(msg->objects[idx]);
            } break;
            default:
                break;
        }

        if (show_obs_vel_property_->getBool()) {
            const auto marker_ptr = getObsVelMarker(msg->objects[idx]);
            marker_ptr->ns = "obs_vel";
            marker_ptr->header = msg->header;
            marker_ptr->id = static_cast<int>(idx);
            m_marker_common->addMessage(marker_ptr);
        }

        if (show_predicted_path_property_->getBool()) {
            const auto marker_ptr = getPredictedPathMarker(msg->objects[idx]);
            marker_ptr->ns = "predicted_path";
            marker_ptr->header = msg->header;
            marker_ptr->id = static_cast<int>(idx);
            m_marker_common->addMessage(marker_ptr);
        }

        if (show_id_property_->getBool()) {
            const auto marker_ptr = getIdTextMarker(msg->objects[idx]);
            marker_ptr->ns = "id_text";
            marker_ptr->header = msg->header;
            marker_ptr->id = static_cast<int>(idx);
            m_marker_common->addMessage(marker_ptr);
        }

        if (show_confidence_property_->getBool()) {
            const auto marker_ptr = getConfidenceTextMarker(msg->objects[idx]);
            marker_ptr->ns = "confidence_text";
            marker_ptr->header = msg->header;
            marker_ptr->id = static_cast<int>(idx);
            m_marker_common->addMessage(marker_ptr);
        }
    }
}

void PerceptionObjectsDisplay::update(float wall_dt, float ros_dt)
{
    m_marker_common->update(wall_dt, ros_dt);

    // std::cout << "object_track_cached_ size: " << object_track_cached_.size() << std::endl;
    for (std::map<uint32_t, ObjectMeshVisual *>::iterator iter = object_track_cached_.begin();
         iter != object_track_cached_.end();) {
        // std::cout << "object id: " << iter->first << ", " << iter->second->description() << std::endl;
        ObjectMeshVisual *object_mesh_visual = iter->second;
        bool timeout = object_mesh_visual->time_out();
        object_mesh_visual->update();
        if (object_mesh_visual->time_out()) {
            std::cout << "description: " << object_mesh_visual->description() << ", time out" << std::endl;
            delete object_mesh_visual;
            iter = object_track_cached_.erase(iter);
        } else {
            iter++;
        }
    }
}

void PerceptionObjectsDisplay::reset()
{
    RosTopicDisplay::reset();
    m_marker_common->clearMarkers();
}

}  // namespace perception_rviz_plugin

// Export the plugin
#include <pluginlib/class_list_macros.hpp>  // NOLINT
PLUGINLIB_EXPORT_CLASS(perception_rviz_plugin::PerceptionObjectsDisplay, rviz_common::Display)
