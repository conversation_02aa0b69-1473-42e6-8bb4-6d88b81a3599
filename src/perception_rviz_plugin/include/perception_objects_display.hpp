/**
 *
 *   @file perception_objects_display.hpp
 *   <AUTHOR> (<EMAIL>)
 *   @brief
 *   @version 0.1
 *   @date 2023-02-07
 *
 *   (C) 2023 UBTECH.Co.Ltd. All rights reserved
 *
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF ME<PERSON>HANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef PERCEPTION_RVIZ_PLUGIN_PERCEPTION_OBJECTS_DISPLAY_HPP_
#define PERCEPTION_RVIZ_PLUGIN_PERCEPTION_OBJECTS_DISPLAY_HPP_

#include <rviz_common/display.hpp>
#include <rviz_common/properties/color_property.hpp>
#include <rviz_common/properties/float_property.hpp>
#include <rviz_common/properties/enum_property.hpp>
#include <rviz_default_plugins/displays/marker/marker_common.hpp>
#include <rviz_default_plugins/displays/marker_array/marker_array_display.hpp>
#include <custom_msgs/msg/perception_objects.hpp>
#include <object_factory.hpp>
#include <unordered_map>

#include "tf2_ros/transform_listener.h"
#include "tf2_ros/buffer.h"
#include "tf2_geometry_msgs/tf2_geometry_msgs.h"

namespace perception_rviz_plugin {

enum DisplayStyle { STYLE_BOUNDING_BOX, STYLE_MESH };

class PerceptionObjectsDisplay : public rviz_common::RosTopicDisplay<custom_msgs::msg::PerceptionObjects>, rclcpp::Node
{
    Q_OBJECT

public:
    using MarkerCommon = rviz_default_plugins::displays::MarkerCommon;
    using Marker = visualization_msgs::msg::Marker;
    using PerceptionObject = custom_msgs::msg::PerceptionObject;
    using PerceptionObjects = custom_msgs::msg::PerceptionObjects;
    using ObjectClassification = custom_msgs::msg::ObjectClassification;

    PerceptionObjectsDisplay();
    ~PerceptionObjectsDisplay();
    void onInitialize() override;
    void load(const rviz_common::Config &config) override;
    void update(float wall_dt, float ros_dt) override;
    void reset() override;

private Q_SLOTS:
    void updateProperty();

private:
    // Convert boxes into markers, push them to the display queue
    void processMessage(PerceptionObjects::ConstSharedPtr objects) override;

    void processObject(const PerceptionObject &obj);

    Marker::SharedPtr getObsVelMarker(const PerceptionObject &object) const;

    Marker::SharedPtr getBoundingBoxMarker(const PerceptionObject &object) const;

    Marker::SharedPtr getPredictedPathMarker(const PerceptionObject &object) const;

    Marker::SharedPtr getIdTextMarker(const PerceptionObject &object) const;

    Marker::SharedPtr getConfidenceTextMarker(const PerceptionObject &object) const;

    QColor getColor(int classification) const;

    void fixedFrameTransformThread();

    std::unique_ptr<MarkerCommon> m_marker_common;
    PerceptionObjects::ConstSharedPtr msg_cache_{};
    rviz_common::properties::ColorProperty *unknown_color_property_;
    rviz_common::properties::ColorProperty *car_color_property_;
    rviz_common::properties::ColorProperty *bus_color_property_;
    rviz_common::properties::ColorProperty *truck_color_property_;
    rviz_common::properties::ColorProperty *trailer_color_property_;
    rviz_common::properties::ColorProperty *pedestrian_color_property_;
    rviz_common::properties::ColorProperty *bicycle_color_property_;
    rviz_common::properties::ColorProperty *motorcycle_color_property_;
    rviz_common::properties::ColorProperty *shelves_color_property_;
    rviz_common::properties::ColorProperty *goods_color_property_;
    rviz_common::properties::ColorProperty *traffic_light_color_property_;
    rviz_common::properties::ColorProperty *forklift_color_property_;
    rviz_common::properties::ColorProperty *agv_color_property_;
    rviz_common::properties::ColorProperty *other_color_property_;
    rviz_common::properties::FloatProperty *alpha_property_;
    rviz_common::properties::EnumProperty *style_property_;
    rviz_common::properties::BoolProperty *show_id_property_;
    rviz_common::properties::BoolProperty *show_obs_vel_property_;
    rviz_common::properties::BoolProperty *show_confidence_property_;
    rviz_common::properties::BoolProperty *show_predicted_path_property_;
    rviz_common::properties::BoolProperty *show_chitu_car_property_;

    // std::map<boost::uuids::uuid, CarMeshVisual> car_track_cached_;
    // std::map<boost::uuids::uuid, PersonMeshVisual> person_track_cached_;
    std::map<uint32_t, ObjectMeshVisual *> object_track_cached_;
    ObjectMeshVisual *chitu_mesh_visual_;
    std::shared_ptr<tf2_ros::TransformListener> tf_listener_{nullptr};
    std::unique_ptr<tf2_ros::Buffer> tf_buffer_;
    std::thread fixed_frame_transform_thread_;
    tf2::Stamped<tf2::Transform> transform_fixed_frame_to_top_lidar_;
    bool is_destroying_;
};

}  // namespace perception_rviz_plugin

#endif  // PERCEPTION_RVIZ_PLUGIN_PERCEPTION_OBJECTS_DISPLAY_HPP_