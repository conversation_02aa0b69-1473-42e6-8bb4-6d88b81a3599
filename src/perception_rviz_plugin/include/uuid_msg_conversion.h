/**
 *
 *   @file uuid_msg_conversion.h
 *   <AUTHOR> (<EMAIL>)
 *   @brief
 *   @version 0.1
 *   @date 2023-02-15
 *
 *   (C) 2023 UBTECH.Co.Ltd. All rights reserved
 *
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF MERC<PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef PERCEPTION_RVIZ_PLUGIN_UUID_MSG_CONVERSION_H_
#define PERCEPTION_RVIZ_PLUGIN_UUID_MSG_CONVERSION_H_

#include <boost/uuid/uuid.hpp>
#include <boost/uuid/uuid_generators.hpp>
#include <boost/uuid/uuid_io.hpp>
#include <unique_identifier_msgs/msg/uuid.hpp>

namespace perception_rviz_plugin {

static inline boost::uuids::uuid fromMsg(const unique_identifier_msgs::msg::UUID &msg)
{
    boost::uuids::uuid uu{};
    std::copy(msg.uuid.begin(), msg.uuid.end(), uu.begin());
    return uu;
}

static inline boost::uuids::uuid generateUUID()
{
    boost::uuids::uuid u = boost::uuids::random_generator()();
    return u;
}

static inline unique_identifier_msgs::msg::UUID toMsg(boost::uuids::uuid const &uu)
{
    unique_identifier_msgs::msg::UUID msg;
    std::copy(uu.begin(), uu.end(), msg.uuid.begin());
    return msg;
}

}  // namespace perception_rviz_plugin

#endif  // PERCEPTION_RVIZ_PLUGIN_UUID_MSG_CONVERSION_H_