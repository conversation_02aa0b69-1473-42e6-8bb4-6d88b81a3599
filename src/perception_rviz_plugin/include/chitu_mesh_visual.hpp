/**
 *
 *   @file chitu_mesh_visual.hpp
 *   <AUTHOR> (<EMAIL>)
 *   @brief
 *   @version 0.1
 *   @date 2023-06-13
 *
 *   (C) 2023 UBTECH.Co.Ltd. All rights reserved
 *
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *
 *   THIS SOFTWARE IS PROVIDED BY THE  AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SU<PERSON><PERSON><PERSON>TE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR B<PERSON>INESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef PERCEPTION_RVIZ_PLUGIN_CHITU_MESH_VISUAL_HPP_
#define PERCEPTION_RVIZ_PLUGIN_CHITU_MESH_VISUAL_HPP_

#include <ament_index_cpp/get_package_share_directory.hpp>
#include <object_mesh_visual.hpp>

namespace perception_rviz_plugin {

class ChituMeshVisual : public ObjectMeshVisual
{
public:
    ChituMeshVisual() {}

    ~ChituMeshVisual() { std::cout << "~ChituMeshVisual()" << std::endl; }

    ChituMeshVisual(Ogre::SceneManager *scene_manager, Ogre::SceneNode *parent_scene,
                    const std::string &child_scene_node_name) :
        ObjectMeshVisual(scene_manager, parent_scene, child_scene_node_name)
    {
        setMeshFile("chitu.mesh");
        set_animated(false);
        set_description("ChituMeshVisual");
    }

    void setMeshFile(std::string mesh_resource) override
    {
        auto logger = rclcpp::get_logger("rviz2");
        RCLCPP_INFO(logger, "setMeshFile: %s", mesh_resource.c_str());

        const std::string package_path = ament_index_cpp::get_package_share_directory("perception_rviz_plugin");
        constexpr char kMaterialsDirectory[] = "/media";
        Ogre::ResourceGroupManager::getSingleton().addResourceLocation(package_path + kMaterialsDirectory, "FileSystem",
                                                                       "autoware_rviz_plugins");
        static size_t count = 0;
        std::stringstream ss;
        ss << "chitu_mesh_visual" << count++;
        std::string id = ss.str();
        entity_ = scene_manager_->createEntity(id, mesh_resource);
        child_scene_node_->attachObject(entity_);

        // set orientation
        // Ogre::Quaternion quat;
        // quat.FromAngleAxis(Ogre::Degree(-90), Ogre::Vector3(0, 0, 1));
        // setOrientationCorrection(quat);
        double scaleFactor = 0.01;
        setScalingFactor(scaleFactor);
        setVisible(true);
    }

    void update() override
    {
        time_t current_time = time(0);
        if (current_time - latest_set_pos_time_ > time_out_duration_) {
            set_time_out(true);
            std::cout << "chitu mesh visual set_time_out(true)" << std::endl;
        }
    }
};

}  // namespace perception_rviz_plugin

#endif  // PERCEPTION_RVIZ_PLUGIN_CHITU_MESH_VISUAL_HPP_