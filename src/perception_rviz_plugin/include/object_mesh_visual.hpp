/**
 *
 *   @file object_mesh_visual.hpp
 *   <AUTHOR> (<EMAIL>)
 *   @brief
 *   @version 0.1
 *   @date 2023-02-10
 *
 *   (C) 2023 UBTECH.Co.Ltd. All rights reserved
 *
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF ME<PERSON>HANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef PERCEPTION_RVIZ_PLUGIN_OBJECT_MESH_VISUAL_HPP_
#define PERCEPTION_RVIZ_PLUGIN_OBJECT_MESH_VISUAL_HPP_

#include <OgreAnimation.h>
#include <OgreSceneManager.h>
#include <OgreMaterialManager.h>
#include <OgreTechnique.h>
#include <OgreEntity.h>
#include <ctime>

namespace perception_rviz_plugin {

class ObjectMeshVisual
{
public:
    ObjectMeshVisual() {}

    ObjectMeshVisual(Ogre::SceneManager *scene_manager, Ogre::SceneNode *parent_node,
                     const std::string &child_scene_node_name) :
        scene_manager_(scene_manager), time_out_(false)
    {
        child_scene_node_ = parent_node->createChildSceneNode(child_scene_node_name);
    }

    virtual ~ObjectMeshVisual()
    {
        std::cout << "~ObjectMeshVisual()" << std::endl;
        scene_manager_->destroySceneNode(child_scene_node_->getName());
        std::cout << "~ObjectMeshVisual() end" << std::endl;
    }

    void setPosition(const Ogre::Vector3 &position)
    {
        child_scene_node_->setPosition(position);
        latest_set_pos_time_ = time(0);
    }

    const Ogre::Vector3 &getPosition() const { return child_scene_node_->getPosition(); }

    void setOrientationCorrection(const Ogre::Quaternion &orientation) { orientatin_correction_ = orientation; }

    void setOrientation(const Ogre::Quaternion &orientation)
    {
        child_scene_node_->setOrientation(orientation * orientatin_correction_);
    }

    const Ogre::Quaternion &getOrientation() const { return child_scene_node_->getOrientation(); }

    void setScalingFactor(double scalingFactor)
    {
        child_scene_node_->setScale(scalingFactor, scalingFactor, scalingFactor);
    }

    void setVisible(bool visible) { child_scene_node_->setVisible(visible, true); }

    virtual void setMeshFile(std::string mesh_resource) = 0;

    bool animated() { return animated_; }

    void set_animated(bool animated) { animated_ = animated; }

    void set_time_out_duration(double secs) { time_out_duration_ = secs; }

    void set_time_out(bool time_out) { time_out_ = time_out; }

    bool time_out() { return time_out_; }

    std::string description() { return description_; }

    void set_description(std::string description) { description_ = description; }

    virtual void update() = 0;

protected:
    Ogre::SceneManager *scene_manager_;
    Ogre::SceneNode *child_scene_node_;
    Ogre::Entity *entity_;
    Ogre::Quaternion orientatin_correction_;
    bool animated_;
    double time_out_duration_;
    time_t latest_set_pos_time_;
    bool time_out_;
    std::string description_;
};

}  // namespace perception_rviz_plugin

#endif  // PERCEPTION_RVIZ_PLUGIN_OBJECT_MESH_VISUAL_HPP_