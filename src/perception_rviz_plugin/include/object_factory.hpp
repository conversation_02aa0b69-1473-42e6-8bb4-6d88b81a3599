/**
 *
 *   @file object_factory.hpp
 *   <AUTHOR> (<EMAIL>)
 *   @brief
 *   @version 0.1
 *   @date 2023-02-16
 *
 *   (C) 2023 UBTECH.Co.Ltd. All rights reserved
 *
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF ME<PERSON>HA<PERSON><PERSON><PERSON>ITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include <bicycle_mesh_visual.hpp>
#include <car_mesh_visual.hpp>
#include <chitu_mesh_visual.hpp>
#include <truck_mesh_visual.hpp>
#include <custom_msgs/msg/perception_object.hpp>
#include <object_mesh_visual.hpp>
#include <person_mesh_visual.hpp>
#include "tf2_ros/transform_listener.h"
#include "tf2_ros/buffer.h"
#include "tf2_geometry_msgs/tf2_geometry_msgs.h"

namespace perception_rviz_plugin {
class ObjectFactory
{
public:
    using ObjectClassification = custom_msgs::msg::ObjectClassification;
    using PerceptionObject = custom_msgs::msg::PerceptionObject;
    ObjectFactory() {}
    ObjectMeshVisual *createObject(const PerceptionObject &perception_object, Ogre::SceneManager *scene_manager,
                                   Ogre::SceneNode *parent_node,
                                   tf2::Stamped<tf2::Transform> fixed_frame_to_perception_frame)
    {
        ObjectMeshVisual *object_mesh_visual = NULL;
        switch (perception_object.oc.classification) {
            case ObjectClassification::CAR:
                object_mesh_visual = new CarMeshVisual(scene_manager, parent_node, std::to_string(perception_object.id));
                break;
            case ObjectClassification::TRUCK:
                object_mesh_visual = new TruckMeshVisual(scene_manager, parent_node, std::to_string(perception_object.id));
                break;
            case ObjectClassification::BICYCLE:
                object_mesh_visual =
                    new BicycleMeshVisual(scene_manager, parent_node, std::to_string(perception_object.id));
                break;

            case ObjectClassification::PEDESTRIAN:
                object_mesh_visual =
                    new PersonMeshVisual(scene_manager, parent_node, std::to_string(perception_object.id));
                break;
            default:
                return NULL;
                break;
        }

        tf2::Transform perception_frame_to_object;
        perception_frame_to_object.setOrigin(tf2::Vector3(
            perception_object.pose.position.x, perception_object.pose.position.y, perception_object.pose.position.z));
        perception_frame_to_object.setRotation(
            tf2::Quaternion(perception_object.pose.orientation.x, perception_object.pose.orientation.y,
                            perception_object.pose.orientation.z, perception_object.pose.orientation.w));

        tf2::Transform fixed_frame_to_object = fixed_frame_to_perception_frame * perception_frame_to_object;

        Ogre::Vector3 position(fixed_frame_to_object.getOrigin().x(), fixed_frame_to_object.getOrigin().y(),
                               fixed_frame_to_object.getOrigin().z() - perception_object.size.z / 2.0);
        object_mesh_visual->setPosition(position);
        std::cout << "create new object, x, y, z: " << position.x << " " << position.y << " " << position.z << std::endl;

        Ogre::Quaternion orientation(fixed_frame_to_object.getRotation().w(), fixed_frame_to_object.getRotation().x(),
                                     fixed_frame_to_object.getRotation().y(), fixed_frame_to_object.getRotation().z());
        object_mesh_visual->setOrientation(orientation);
        object_mesh_visual->set_time_out_duration(1.0);
        return object_mesh_visual;
    }
};

}  // namespace perception_rviz_plugin