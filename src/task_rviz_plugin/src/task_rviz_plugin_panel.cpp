#include <stdio.h>
#include <sstream>
#include <QPainter>
#include <QLineEdit>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QTimer>
#include <QFileDialog>
#include <QDir>
#include <QFileInfo>
#include <QMetaObject>
#include <QMessageBox>
#include <QFile>

#include <geometry_msgs/msg/twist.hpp>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#include <tf2/utils.h>
#include <QDebug>

#include "task_rviz_plugin_panel.hpp"
#include "rclcpp/qos.hpp"
#include <iostream>
#include <fstream>
#include <thread>
#include <chrono>
#include "state_trans.hpp"
#include <yaml-cpp/yaml.h>

namespace task_rviz_plugin {
namespace horn {
typedef unsigned char uint8;
const uint8 TURN_OFF_SOUND = 0;
const uint8 POWER_LOWER_THAN_TEN = 1;
const uint8 POWER_LOWER_THAN_TWENTY = 2;
const uint8 POWER_FULL_CHAEFED = 3;
const uint8 FRONT_OBSTACLE = 4;
const uint8 LEFT_TURN = 5;
const uint8 RIGHT_TURN = 6;
const uint8 BACKWARD = 7;
const uint8 EXECUSE_ME = 8;
const uint8 START_NAVIGATION = 9;
const uint8 CANCEL_NAVIGATION = 10;
const uint8 REACH_DESTINATION = 11;
const uint8 START_MAPPING = 12;
const uint8 CANCEL_MAPPING = 13;
const uint8 START_SAVE_MAPPING = 14;
const uint8 SAVE_MAPPING_SUCCEED = 15;
const uint8 START_RELOCALIZATION = 16;
const uint8 RELOCALIZTION_SUCCEED = 17;
const uint8 RELOCALIZTION_FAILED = 18;
const uint8 LOCALIZATION_LOST = 19;
const uint8 EMERGENCY_STOPPED = 20;
}  // namespace horn

// 构造函数，初始化变量
TaskRvizPanel::TaskRvizPanel(QWidget *parent) :
    rviz_common::Panel(parent), is_goal_pose_received_(false), is_initial_pose_received_(false)
{
    ui_.setupUi(this);

    // 把各种tab的widget和内部布局进行绑定，方便大小自动调整
    ui_.tab_map->setLayout(ui_.gridLayout_map);
    ui_.tab_locate->setLayout(ui_.gridLayout_locate);
    ui_.tab_planning->setLayout(ui_.gridLayout_planning);
    ui_.tab_goal->setLayout(ui_.gridLayout_goal);
    ui_.tab_map_node->setLayout(ui_.gridLayout_map_mode);
    ui_.tab_log->setLayout(ui_.gridLayout_log);
    ui_.tab_simulation->setLayout(ui_.gridLayout_sim);

    ui_.path_planner->setLayout(ui_.gridLayout_path_subplanner);
    ui_.velocity_planner->setLayout(ui_.gridLayout_velocity_subplanner);
    ui_.traffic_device->setLayout(ui_.gridLayout_traffic_device);

    ui_.goal_pose_x_spin_box->setRange(std::numeric_limits<double>::lowest(), std::numeric_limits<double>::max());
    ui_.goal_pose_y_spin_box->setRange(std::numeric_limits<double>::lowest(), std::numeric_limits<double>::max());
    ui_.goal_pose_theta_spin_box->setRange(-M_PI, M_PI);

    location_traffic_status_label_lcd_ = {
        {TrafficDeviceLight::LOCATION_EAST, {ui_.east_light_status_label, ui_.east_light_time_lcd}},
        {TrafficDeviceLight::LOCATION_SOUTH, {ui_.south_light_status_label, ui_.south_light_time_lcd}},
        {TrafficDeviceLight::LOCATION_WEST, {ui_.west_light_status_label, ui_.west_light_time_lcd}},
        {TrafficDeviceLight::LOCATION_NORTH, {ui_.north_light_status_label, ui_.north_light_time_lcd}}};
    traffic_timer_ = new QTimer(this);
    traffic_timer_->start(300);
    connect(ui_.start_mapping_btn, SIGNAL(clicked()), this, SLOT(clickStartMapping()));
    connect(ui_.save_mapping_btn, SIGNAL(clicked()), this, SLOT(clickSaveMapping()));
    connect(ui_.cancel_mapping_btn, SIGNAL(clicked()), this, SLOT(clickCancelMapping()));

    connect(ui_.manual_relocate_btn, SIGNAL(clicked()), this, SLOT(clickManualRelocate()));
    connect(ui_.gps_relocate_btn, SIGNAL(clicked()), this, SLOT(clickGpsRelocate()));
    connect(ui_.cancel_relocate_btn, SIGNAL(clicked()), this, SLOT(clickCancelRelocate()));

    connect(ui_.routing_planning_btn, SIGNAL(clicked()), this, SLOT(clickRoutingPlanning()));

    connect(ui_.free_navigation_btn, SIGNAL(clicked()), this, SLOT(clickFreeNavigation()));
    connect(ui_.cancel_navigation_btn, SIGNAL(clicked()), this, SLOT(clickCancelNavigation()));
    connect(ui_.max_speed_doubleSpinBox, SIGNAL(valueChanged(double)), this, SLOT(clickSetPlanningSpeed()));
    connect(ui_.goal_pose_x_spin_box, SIGNAL(valueChanged(double)), this, SLOT(clickGoalSpinBox2GoalPose()));
    connect(ui_.goal_pose_y_spin_box, SIGNAL(valueChanged(double)), this, SLOT(clickGoalSpinBox2GoalPose()));
    connect(ui_.goal_pose_theta_spin_box, SIGNAL(valueChanged(double)), this, SLOT(clickGoalSpinBox2GoalPose()));

    connect(ui_.checkBox_engage, SIGNAL(clicked()), this, SLOT(clickEngageCheckBox()));
    connect(ui_.save_goal_btn, SIGNAL(clicked()), this, SLOT(clickSaveGoalBtn()));

    connect(ui_.red_light_btn, &QPushButton::clicked, this, [this]() { clickSelectTrafficLight("red"); });
    connect(ui_.green_light_btn, &QPushButton::clicked, this, [this]() { clickSelectTrafficLight("green"); });
    connect(ui_.yellow_light_btn, &QPushButton::clicked, this, [this]() { clickSelectTrafficLight("yellow"); });

    connect(ui_.turn_left_light, &QRadioButton::clicked, this, [this]() { clickSelectTrafficRule("turn_left"); });
    connect(ui_.turn_right_light, &QRadioButton::clicked, this, [this]() { clickSelectTrafficRule("turn_right"); });
    connect(ui_.straight_light, &QRadioButton::clicked, this, [this]() { clickSelectTrafficRule("straight"); });

    connect(ui_.rain_fall_box, QOverload<int>::of(&QComboBox::currentIndexChanged), this,
            [this]() { clickSelectRainFall(); });

    // traffi device
    // device light rule
    connect(ui_.east_traffic_rule_box, QOverload<int>::of(&QComboBox::currentIndexChanged), this,
            [this]() { clickSelectTrafficDeviceRule(TrafficDeviceLight::LOCATION_EAST, ui_.east_traffic_rule_box); });
    connect(ui_.south_traffic_rule_box, QOverload<int>::of(&QComboBox::currentIndexChanged), this,
            [this]() { clickSelectTrafficDeviceRule(TrafficDeviceLight::LOCATION_SOUTH, ui_.south_traffic_rule_box); });
    connect(ui_.west_traffic_rule_box, QOverload<int>::of(&QComboBox::currentIndexChanged), this,
            [this]() { clickSelectTrafficDeviceRule(TrafficDeviceLight::LOCATION_WEST, ui_.west_traffic_rule_box); });
    connect(ui_.north_traffic_rule_box, QOverload<int>::of(&QComboBox::currentIndexChanged), this,
            [this]() { clickSelectTrafficDeviceRule(TrafficDeviceLight::LOCATION_NORTH, ui_.north_traffic_rule_box); });

    // device light color
    connect(ui_.east_traffic_color_box, QOverload<int>::of(&QComboBox::currentIndexChanged), this,
            [this]() { clickSelectTrafficDeviceColor(TrafficDeviceLight::LOCATION_EAST, ui_.east_traffic_color_box); });
    connect(ui_.south_traffic_color_box, QOverload<int>::of(&QComboBox::currentIndexChanged), this,
            [this]() { clickSelectTrafficDeviceColor(TrafficDeviceLight::LOCATION_SOUTH, ui_.south_traffic_color_box); });
    connect(ui_.west_traffic_color_box, QOverload<int>::of(&QComboBox::currentIndexChanged), this,
            [this]() { clickSelectTrafficDeviceColor(TrafficDeviceLight::LOCATION_WEST, ui_.west_traffic_color_box); });
    connect(ui_.north_traffic_color_box, QOverload<int>::of(&QComboBox::currentIndexChanged), this,
            [this]() { clickSelectTrafficDeviceColor(TrafficDeviceLight::LOCATION_NORTH, ui_.north_traffic_color_box); });

    // device light time
    connect(ui_.set_traffic_time_btn, &QPushButton::clicked, this, [this]() { clickSetTrafficDeviceLightTime(); });
    // connect(traffic_timer_, &QTimer::timeout, this, [this]() { TimeLcdToggleVisibility(ui_.east_light_time_lcd); });
    // connect(traffic_timer_, &QTimer::timeout, this, [this]() { TimeLcdToggleVisibility(ui_.south_light_time_lcd); });
    // connect(traffic_timer_, &QTimer::timeout, this, [this]() { TimeLcdToggleVisibility(ui_.north_light_time_lcd); });
    // connect(traffic_timer_, &QTimer::timeout, this, [this]() { TimeLcdToggleVisibility(ui_.west_light_time_lcd); });

    //device light response
    connect(ui_.traffic_device_response_btn, &QPushButton::clicked, this, [this]() { clickSetTrafficDeviceResponse(); });

    //device sim show mode switch
    connect(ui_.deivce_sim_status_show_switch_btn, &QPushButton::clicked, this,
            [this]() { clickSwitchTrafficDevicePanelMode(); });

    // subplanner
    connect(ui_.on_trailer_planner_btn, &QRadioButton::clicked, this,
            [this]() { switchSubPlanner("trailer_planner", "on"); });
    connect(ui_.off_trailer_planner_btn, &QRadioButton::clicked, this,
            [this]() { switchSubPlanner("trailer_planner", "off"); });

    connect(ui_.on_backup_planner_btn, &QRadioButton::clicked, this,
            [this]() { switchSubPlanner("backup_generator", "on"); });
    connect(ui_.off_backup_planner_btn, &QRadioButton::clicked, this,
            [this]() { switchSubPlanner("backup_generator", "off"); });

    connect(ui_.on_trajectory_checker_btn, &QRadioButton::clicked, this,
            [this]() { switchSubPlanner("trajectory_checker", "on"); });
    connect(ui_.off_trajectory_checker_btn, &QRadioButton::clicked, this,
            [this]() { switchSubPlanner("trajectory_checker", "off"); });

    connect(ui_.on_traffic_light_manager_btn, &QRadioButton::clicked, this,
            [this]() { switchSubPlanner("traffic_light_manager", "on"); });
    connect(ui_.off_traffic_light_manager_btn, &QRadioButton::clicked, this,
            [this]() { switchSubPlanner("traffic_light_manager", "off"); });

    connect(ui_.on_speed_bump_manager_btn, &QRadioButton::clicked, this,
            [this]() { switchSubPlanner("speed_bump_manager", "on"); });
    connect(ui_.off_speed_bump_manager_btn, &QRadioButton::clicked, this,
            [this]() { switchSubPlanner("speed_bump_manager", "off"); });

    connect(ui_.on_extra_strategy_manager_btn, &QRadioButton::clicked, this,
            [this]() { switchSubPlanner("extra_strategy_manager", "on"); });
    connect(ui_.off_extra_strategy_manager_btn, &QRadioButton::clicked, this,
            [this]() { switchSubPlanner("extra_strategy_manager", "off"); });

    connect(ui_.on_drivable_space_manager_btn, &QRadioButton::clicked, this,
            [this]() { switchSubPlanner("drivable_space_manager", "on"); });
    connect(ui_.off_drivable_space_manager_btn, &QRadioButton::clicked, this,
            [this]() { switchSubPlanner("drivable_space_manager", "off"); });

    connect(ui_.goal_pose_table, SIGNAL(itemClicked(QTableWidgetItem *)), this,
            SLOT(clickGoalPoseTable(QTableWidgetItem *)));
    connect(ui_.planning_selected_goal_btn, SIGNAL(clicked()), this, SLOT(clickPlanningSelectedGoalBtn()));
    connect(ui_.delete_selected_goal_btn, SIGNAL(clicked()), this, SLOT(clickDeleteSelectedGoalBtn()));
    connect(ui_.loop_planning_goal_btn, SIGNAL(clicked()), this, SLOT(clickLoopPlanningGoalBtn()));
    connect(ui_.loop_planning_stop_btn, SIGNAL(clicked()), this, SLOT(clickLoopPlanningStopBtn()));

    connect(ui_.set_map_btn, SIGNAL(clicked()), this, SLOT(clickSetMapBtn()));

    loop_planning_worker_ = new WorkerThread();
    connect(loop_planning_worker_, &WorkerThread::sendCurStatus, this, &TaskRvizPanel::handleWorkerIndex);
    connect(loop_planning_worker_, &WorkerThread::sendPlanningIndex, this, &TaskRvizPanel::publishOneGoal);

    connect(this, &TaskRvizPanel::sendPlanningStateToLoopPlanning, loop_planning_worker_,
            &WorkerThread::handlePlanningState);
    connect(this, &TaskRvizPanel::sendStopToLoopPlanning, loop_planning_worker_, &WorkerThread::handleStop);
    connect(ui_.one_loop_checkBox, &QCheckBox::stateChanged, loop_planning_worker_, &WorkerThread::handleOneLoopBox);

    connect(ui_.sim_set_folder_btn, &QPushButton::clicked, this, &TaskRvizPanel::clickSimSetFolder);
    connect(ui_.sim_init_btn, &QPushButton::clicked, this, &TaskRvizPanel::clickSimInitSimulation);
    connect(ui_.sim_run_btn, &QPushButton::clicked, this, &TaskRvizPanel::clickSimRun);
    connect(ui_.sim_edit_btn, &QPushButton::clicked, this, &TaskRvizPanel::clickSimEditSelect);
    connect(ui_.sim_delete_btn, &QPushButton::clicked, this, &TaskRvizPanel::clickSimDeleteSelect);
    connect(ui_.sim_save_btn, &QPushButton::clicked, this, &TaskRvizPanel::clickSimSave);

    connect(ui_.sim_config_table, &QTableWidget::itemClicked, this, &TaskRvizPanel::simTableSelect);
}

void TaskRvizPanel::onInitialize()
{
    auto ros_node_abstraction = (getDisplayContext()->getRosNodeAbstraction()).lock();
    rclcpp::Node::SharedPtr node = ros_node_abstraction->get_raw_node();
    sub_odom_ = node->template create_subscription<nav_msgs::msg::Odometry>(
        "/odom", 1, std::bind(&TaskRvizPanel::odomCallback, this, std::placeholders::_1));
    sub_battrey_ = node->template create_subscription<ackermann_msgs::msg::AckermannDriveStatus>(
        "/ackermann/status", 1, std::bind(&TaskRvizPanel::batteryCallback, this, std::placeholders::_1));
    sub_joy_ = node->template create_subscription<ackermann_msgs::msg::AckermannDriveOdometry>(
        "/joy_speed", 1, std::bind(&TaskRvizPanel::joyCallback, this, std::placeholders::_1));
    sub_horn_ = node->template create_subscription<chitu_msgs::msg::Horn>(
        "/sensor/horn/set", 1, std::bind(&TaskRvizPanel::hornCallback, this, std::placeholders::_1));
    sub_goal_pose_ = node->template create_subscription<geometry_msgs::msg::PoseStamped>(
        "/uslam/hmi/goal_pose", 1, std::bind(&TaskRvizPanel::goalPoseCallback, this, std::placeholders::_1));
    sub_initial_pose_ = node->template create_subscription<geometry_msgs::msg::PoseWithCovarianceStamped>(
        "/uslam/hmi/initial_pose", 1, std::bind(&TaskRvizPanel::initialPoseCallback, this, std::placeholders::_1));
    sub_task_response_ = node->template create_subscription<std_msgs::msg::String>(
        "/uslam/task/response", 1, std::bind(&TaskRvizPanel::taskResponseCallback, this, std::placeholders::_1));
    sub_planning_max_speed_ = node->template create_subscription<std_msgs::msg::String>(
        "/uslam/task/max_velocity_response", 1,
        std::bind(&TaskRvizPanel::planningSpeedCallback, this, std::placeholders::_1));
    sub_planning_nav_progress_bar_ = node->template create_subscription<std_msgs::msg::String>(
        "/uslam/planning/nav_progress_bar", 1,
        std::bind(&TaskRvizPanel::navProgressBarCallback, this, std::placeholders::_1));
    sub_subplanner_switch_status_ = node->template create_subscription<std_msgs::msg::String>(
        "/uslam/planning/subplanner_status", 1,
        std::bind(&TaskRvizPanel::SubplannerStatusCallback, this, std::placeholders::_1));
    // sub_traffic_device_status_res_ = node->template create_subscription<std_msgs::msg::String>(
    //     "/debug/traffic_manager/traffic_device_status", 1,
    //     std::bind(&TaskRvizPanel::SubTrafficDeviceResCallback, this, std::placeholders::_1));

    rclcpp::QoS qos_profile(1);
    qos_profile.reliable().transient_local();
    sub_module_state_ = node->template create_subscription<umodule_msgs::msg::ModuleState>(
        "/uslam/module_state", qos_profile, std::bind(&TaskRvizPanel::moduleStateCallback, this, std::placeholders::_1));

    // 订阅monitor模块启动时使用的HmiConfig
    sub_hmi_config_ = node->template create_subscription<custom_msgs::msg::HmiConfig>(
        "/uslam/monitor/hmi_config", qos_profile, std::bind(&TaskRvizPanel::hmiConfigCallback, this, std::placeholders::_1));

    pub_task_request_ = node->template create_publisher<std_msgs::msg::String>("/uslam/task/request", 10);
    pub_engaged_ = node->template create_publisher<std_msgs::msg::Bool>("/uslam/hmi/engaged", qos_profile);
    pub_planning_max_speed_ = node->template create_publisher<std_msgs::msg::Float32>("/uslam/task/max_velocity_set", 1);
    pub_rain_fall_ = node->template create_publisher<std_msgs::msg::Int32>("/sensor/rainfall1", 1);
    pub_subplanner_switch_ = node->template create_publisher<std_msgs::msg::String>("/planning/subplanner_switch", 1);
    pub_initial_pose_ =
        node->template create_publisher<geometry_msgs::msg::PoseWithCovarianceStamped>("/uslam/hmi/initial_pose", 1);

    service_traffic_guide_ = node->template create_service<custom_msgs::srv::TrafficLightService>(
        "/uslam/traffic/traffic_light",
        [this](const std::shared_ptr<custom_msgs::srv::TrafficLightService::Request> request,
               const std::shared_ptr<custom_msgs::srv::TrafficLightService::Response> response) {
            Handle_service_request(request, response);
        });
    clickSelectTrafficLight("green");
    clickSelectTrafficDeviceLight("Straight", "Red");
    ui_.label_joy->setVisible(false);

    // traffic device service
    service_traffic_device_ = node->template create_service<custom_msgs::srv::TrafficDeviceService>(
        "/uslam/traffic_device_service/traffic_status",
        [this](const std::shared_ptr<custom_msgs::srv::TrafficDeviceService::Request> request,
               const std::shared_ptr<custom_msgs::srv::TrafficDeviceService::Response> response) {
            Handle_service_request(request, response);
        });

    QColor color;
    color.setRgb(255, 0, 0);
    ui_.debug_info_text_browser->setTextColor(color);

    clickEngageCheckBox();

    const char *home = getenv("HOME");  // 获取HOME环境变量的值
    if (home != nullptr) {
        goal_file_ = std::string(home) + "/goal.json";
        selected_goal_index_ = -1;
        loadGoalFile();
    } else {
        std::cerr << "can't get environment variable HOME, set goal_file_ failed" << std::endl;
    }

    sim_data_select_ = std::make_shared<SimulationTool>(node);
}

void TaskRvizPanel::odomCallback(const nav_msgs::msg::Odometry::SharedPtr msg)
{
    auto current_time = msg->header.stamp;
    struct tm tm_time;
    time_t t = (time_t)rclcpp::Time(msg->header.stamp).seconds();
    localtime_r(&t, &tm_time);
    char time_str[128];
    strftime(time_str, 128, "%Y-%m-%d %H:%M:%S", &tm_time);

    std::string output_str =
        "Time: " + std::to_string(rclcpp::Time(msg->header.stamp).seconds()) + "     " + std::string(time_str);
    ui_.label_time->setText(output_str.c_str());

    current_time_from_odom_ = rclcpp::Time(msg->header.stamp).seconds();
    if (current_time_from_odom_ - last_joy_speed_time_ > 5.0) {
        ui_.label_joy->setVisible(false);
    }
}

void TaskRvizPanel::batteryCallback(const ackermann_msgs::msg::AckermannDriveStatus::SharedPtr msg)
{
    std::string output_str = "Battery: " + std::to_string(msg->battery_soc) + "%";
    ui_.label_battery->setText(output_str.c_str());
}

void TaskRvizPanel::joyCallback(const ackermann_msgs::msg::AckermannDriveOdometry::SharedPtr msg)
{
    ui_.label_joy->setVisible(true);
    last_joy_speed_time_ = rclcpp::Time(msg->header.stamp).seconds();
    ;
}

void TaskRvizPanel::hornCallback(const chitu_msgs::msg::Horn::SharedPtr msg)
{
    std::string horn_str = HORN_STATE_DESC_MAP.at(msg->horn_code);
    last_horn_time_ = current_time_from_odom_;
    std::string output_str = "Msg: " + horn_str;
    ui_.label_horn->setText(output_str.c_str());
}

void TaskRvizPanel::goalPoseCallback(const geometry_msgs::msg::PoseStamped::SharedPtr msg)
{
    goal_pose_ = *msg;
    std::string text = "x: " + std::to_string(msg->pose.position.x) + "\n";
    text += "y: " + std::to_string(msg->pose.position.y) + "\n";
    text += "theta: " + std::to_string(tf2::getYaw(msg->pose.orientation));
    ui_.goal_text_browser->setText(text.c_str());
    setGoalPoseSpinBox(msg);
    is_goal_pose_received_ = true;
}

void TaskRvizPanel::initialPoseCallback(const geometry_msgs::msg::PoseWithCovarianceStamped::SharedPtr msg)
{
    initial_pose_ = *msg;
    std::string text = "x: " + std::to_string(msg->pose.pose.position.x) + "\n";
    text += "y: " + std::to_string(msg->pose.pose.position.y) + "\n";
    text += "theta: " + std::to_string(tf2::getYaw(msg->pose.pose.orientation));

    ui_.locate_text_browser->setText(text.c_str());
    is_initial_pose_received_ = true;
}

void TaskRvizPanel::hmiConfigCallback(const custom_msgs::msg::HmiConfig::SharedPtr msg)
{
    std::cout << "hmiConfigCallback" << std::endl;
    ui_.map_comboBox->clear();
    ui_.mode_comboBox->clear();
    for (int i = 0; i < msg->maps.size(); i++) {
        std::cout << "map_folder: " << msg->maps[i] << std::endl;
        ui_.map_comboBox->insertItem(i, QString::fromStdString(msg->maps[i]));
    }

    for (int i = 0; i < msg->modes.size(); i++) {
        std::cout << "mode: " << msg->modes[i] << std::endl;
        ui_.mode_comboBox->insertItem(i, QString::fromStdString(msg->modes[i]));
    }
}

void TaskRvizPanel::planningSpeedCallback(const std_msgs::msg::String::ConstPtr &msg)
{
    // std::cout << "planningSpeedCallback" << std::endl;
    Json::Reader reader;
    Json::Value root;
    if (reader.parse(msg->data, root)) {
        if (root.isMember("max_navigation_velocity")) {
            double max_navigation_velocity = root["max_navigation_velocity"].asFloat();
            if (fabs(max_navigation_velocity - ui_.max_speed_doubleSpinBox->maximum()) > 1e-3) {
                std::cout << "set max_navigation_velocity " << max_navigation_velocity << std::endl;
                ui_.max_speed_doubleSpinBox->setValue(max_navigation_velocity);
                ui_.max_speed_doubleSpinBox->setMinimum(0.0);
                ui_.max_speed_doubleSpinBox->setMaximum(max_navigation_velocity);
            }
        }
        if (root.isMember("rain_fall")) {
            enum class RainFall : unsigned int { Sunny, LightRain, ModerateRain, HeavyRain };
            auto rain_fall = root["rain_fall"].asInt();
            if (rain_fall == static_cast<int>(RainFall::Sunny)) {
                ui_.navigation_progressBar->setStyleSheet(
                    "background-color: qlineargradient(spread: pad, x1 : 0, y1 : 0, x2 : 1, y2 : 0, stop : 0 rgba(0, "
                    "100, "
                    "0, 100), stop : 1 rgba(0, 200, 0, 200));");
            } else if (rain_fall == static_cast<int>(RainFall::LightRain)) {
                ui_.navigation_progressBar->setStyleSheet(
                    "background-color: qlineargradient(spread: pad, x1 : 0, y1 : 0, x2 : 1, y2 : 0, stop : 0 rgba(255, "
                    "255, "
                    "0, 100), stop : 1 rgba(255, 255, 0, 200));");
            } else if (rain_fall == static_cast<int>(RainFall::ModerateRain)) {
                ui_.navigation_progressBar->setStyleSheet(
                    "background-color: qlineargradient(spread: pad, x1 : 0, y1 : 0, x2 : 1, y2 : 0, stop : 0 rgba(139, "
                    "105, "
                    "20, 100), stop : 1 rgba(139, 105, 20, 200));");
            } else if (rain_fall == static_cast<int>(RainFall::HeavyRain)) {
                ui_.navigation_progressBar->setStyleSheet(
                    "background-color: qlineargradient(spread: pad, x1 : 0, y1 : 0, x2 : 1, y2 : 0, stop : 0 rgba(100, "
                    "0, "
                    "0, 100), stop : 1 rgba(255, 0, 0, 210));");
            } else {
                ui_.navigation_progressBar->setStyleSheet(
                    "background-color: qlineargradient(spread: pad, x1 : 0, y1 : 0, x2 : 1, y2 : 0, stop : 0 rgba(0, "
                    "0, "
                    "0, 100), stop : 1 rgba(0, 0, 0, 200));");
            }
        }
    }
}

void TaskRvizPanel::navProgressBarCallback(const std_msgs::msg::String::SharedPtr msg)
{
    Json::Reader reader;
    Json::Value root;
    if (reader.parse(msg->data, root)) {
        if (root.isMember("navigation_progress") && root.isMember("navigation_s") && root.isMember("vehicle_s")) {
            auto navigation_progress_bar = root["navigation_progress"].asDouble();
            ui_.navigation_progressBar->setValue(int(std::ceil(navigation_progress_bar * 100.0)));
            auto remain_s = root["navigation_s"].asDouble() - root["vehicle_s"].asDouble();
            QString format = QString("%1 m / %2%").arg(remain_s, 0, 'f', 2).arg(ui_.navigation_progressBar->value());
            ui_.navigation_progressBar->setFormat(format);
        }
    }
}

void TaskRvizPanel::Handle_service_request(const std::shared_ptr<custom_msgs::srv::TrafficLightService_Request> &request,
                                           const std::shared_ptr<custom_msgs::srv::TrafficLightService_Response> &response)
{
    // std::cout << "traffic service " << std::endl;
    std::vector<QPushButton *> traffic_light_btn = {ui_.turn_left_light, ui_.turn_right_light, ui_.straight_light};
    auto ask = request->ask;
    auto id = request->ask_id;
    // std::cout << "ask: " << ask << "id: " << id << std::endl;
    if (ask) {
        for (auto btn : traffic_light_btn) {
            if (!btn->isEnabled()) {
                btn->setEnabled(true);
            }
        }
        if (traffic_respone_) {
            traffic_respone_ = false;
            custom_msgs::srv::TrafficLightService_Response traffic_result;
            traffic_guide_.id = id;
            traffic_guide_.light_color.id = id;
            std::cout << "traffic rule: " << int(traffic_guide_.rule)
                      << "coclor: " << int(traffic_guide_.light_color.light_color) << std::endl;
            traffic_result.traffic_guide_sequence.push_back(traffic_guide_);
            response->traffic_guide_sequence = traffic_result.traffic_guide_sequence;

            for (auto btn : traffic_light_btn) {
                if (btn->isEnabled()) {
                    btn->setEnabled(false);
                }
            }
        }
    }
}

void TaskRvizPanel::TimeLcdToggleVisibility(QLCDNumber *lcd_number)
{
    lcd_number->setVisible(!lcd_number->isVisible());
}

void TaskRvizPanel::clickSetTrafficDeviceResponse()
{
    traffic_device_response_bool_ = true;
    std::cout << "Set traffic device response" << std::endl;
}

void TaskRvizPanel::clickSwitchTrafficDevicePanelMode()
{
    traffic_device_signal_sim_mode_ = !traffic_device_signal_sim_mode_;
    if (!traffic_device_signal_sim_mode_) {
        ui_.deivce_sim_status_show_switch_btn->setText(QString::fromStdString("Siganl Sim"));

        ui_.east_traffic_rule_box->setVisible(false);
        ui_.south_traffic_rule_box->setVisible(false);
        ui_.west_traffic_rule_box->setVisible(false);
        ui_.north_traffic_rule_box->setVisible(false);

        ui_.east_traffic_color_box->setVisible(false);
        ui_.south_traffic_color_box->setVisible(false);
        ui_.west_traffic_color_box->setVisible(false);
        ui_.north_traffic_color_box->setVisible(false);

        ui_.east_light_time_spinBox->setVisible(false);
        ui_.south_light_time_spinBox->setVisible(false);
        ui_.west_light_time_spinBox->setVisible(false);
        ui_.north_light_time_spinBox->setVisible(false);

        ui_.traffic_device_response_btn->setVisible(false);
        ui_.set_traffic_time_btn->setVisible(false);

    } else {
        ui_.deivce_sim_status_show_switch_btn->setText(QString::fromStdString("Show Mode"));

        ui_.east_traffic_rule_box->setVisible(true);
        ui_.south_traffic_rule_box->setVisible(true);
        ui_.west_traffic_rule_box->setVisible(true);
        ui_.north_traffic_rule_box->setVisible(true);

        ui_.east_traffic_color_box->setVisible(true);
        ui_.south_traffic_color_box->setVisible(true);
        ui_.west_traffic_color_box->setVisible(true);
        ui_.north_traffic_color_box->setVisible(true);

        ui_.east_light_time_spinBox->setVisible(true);
        ui_.south_light_time_spinBox->setVisible(true);
        ui_.west_light_time_spinBox->setVisible(true);
        ui_.north_light_time_spinBox->setVisible(true);

        ui_.traffic_device_response_btn->setVisible(true);
        ui_.set_traffic_time_btn->setVisible(true);
    }
}

void TaskRvizPanel::Handle_service_request(const std::shared_ptr<custom_msgs::srv::TrafficDeviceService_Request> &request,
                                           const std::shared_ptr<custom_msgs::srv::TrafficDeviceService_Response> &response)
{
    auto req = request->req;
    auto device_id = request->device_id;
    auto rule = request->rule;

    if (!req) {
        traffic_time_set_ = false;
        traffic_color_set_ = false;
        traffic_rule_set_ = false;
        traffic_device_response_bool_ = false;
        if (ui_.traffic_device_response_btn->isEnabled()) {
            ui_.traffic_device_response_btn->setEnabled(false);
        }
        return;
    }
    if (!traffic_time_set_) {
        ui_.set_traffic_time_btn->setEnabled(true);
        std::cout << "traffic time don't set..." << std::endl;
        return;
    }
    // ui_.set_traffic_time_btn->setEnabled(false);
    if (!ui_.traffic_device_response_btn->isEnabled()) {
        ui_.traffic_device_response_btn->setEnabled(true);
    }

    traffic_device_response_.device_status.device_id = device_id;

    auto ExistLocaiton = [this](const u_int8_t &location) -> bool {
        return location_traffic_status_label_lcd_.find(location) != location_traffic_status_label_lcd_.end();
    };

    // status_label->setStyleSheet("background-color: rgba(0, 200, 0, 0.5);");
    auto SetTraffiStatusShow = [this](const u_int8_t &color, const int16_t &time, const bool &twinkle,
                                      QwtTextLabel *label, QLCDNumber *lcd_number) {
        if (color == TrafficDeviceLight::COLOR_RED) {
            label->setStyleSheet("background-color: rgba(200, 0, 0, 0.5);");
        } else if (color == TrafficDeviceLight::COLOR_GREEN) {
            label->setStyleSheet("background-color: rgba(0, 200, 0, 0.5);");
        } else if (color == TrafficDeviceLight::COLOR_YELLOW) {
            label->setStyleSheet("background-color: rgba(255, 255, 0, 0.5);");
        } else {
            label->setStyleSheet("background-color: rgba(156, 156, 156, 1.0);");
        }
        lcd_number->display(time);
        if (twinkle) {
            lcd_number->setStyleSheet("background-color: rgba(255, 255, 0, 0.5);");
        } else {
            lcd_number->setStyleSheet("background-color: white; color: red;");
        }
    };
    for (auto &light : traffic_device_response_.device_status.lights) {
        if (ExistLocaiton(light.location)) {
            // light.rule = rule;
            auto diff_msg_time =
                (rclcpp::Clock().now().seconds() - traffic_device_response_.device_status.header.stamp.sec);
            auto &status_label_time_lcd = location_traffic_status_label_lcd_.at(light.location);
            auto &status_label = std::get<0>(status_label_time_lcd);
            auto &time_lcd = std::get<1>(status_label_time_lcd);
            auto current_sec = std::max(light.time - int(diff_msg_time), 0);
            SetTraffiStatusShow(light.color, current_sec, light.twinkle, status_label, time_lcd);
            light.time = current_sec;
        }
    }
    traffic_device_response_.device_status.header.stamp = rclcpp::Clock().now();
    std::cout << "Handle_service_request" << std::endl;
    if (traffic_device_response_bool_) {
        (*response) = traffic_device_response_;
        std::cout << "stamp: " << (response->device_status.header.stamp.sec)
                  << " response size: " << response->device_status.lights.size() << std::endl;
        for (auto const &light : response->device_status.lights) {
            std::cout << " location: " << int(light.location) << " rule: " << int(light.rule)
                      << " color: " << int(light.color) << " time: " << light.time << std::endl;
        }
        // traffic_device_response_bool_ = false;
    }
}

void TaskRvizPanel::SubplannerStatusCallback(const std_msgs::msg::String::SharedPtr msg)
{
    // std::cout << "sub subplanner status" << std::endl;
    std::unordered_map<std::string, std::tuple<QLabel *, QPushButton *, QPushButton *>> subplanner_ui_map = {
        {"trailer_planner",
         std::make_tuple(ui_.trailer_planner_status_label, ui_.on_trailer_planner_btn, ui_.off_trailer_planner_btn)},
        {"backup_generator",
         std::make_tuple(ui_.backup_planner_status_label, ui_.on_backup_planner_btn, ui_.off_backup_planner_btn)},
        {"trajectory_checker", std::make_tuple(ui_.trajectory_checker_status_label, ui_.on_trajectory_checker_btn,
                                               ui_.off_trajectory_checker_btn)},
        {"traffic_light_manager", std::make_tuple(ui_.traffic_light_manager_status_label,
                                                  ui_.on_traffic_light_manager_btn, ui_.off_traffic_light_manager_btn)},
        {"speed_bump_manager", std::make_tuple(ui_.speed_bump_manager_status_label, ui_.on_speed_bump_manager_btn,
                                               ui_.off_speed_bump_manager_btn)},
        {"extra_strategy_manager", std::make_tuple(ui_.extra_strategy_manager_status_label,
                                                   ui_.on_extra_strategy_manager_btn, ui_.off_extra_strategy_manager_btn)},
        {"drivable_space_manager", std::make_tuple(ui_.drivable_space_manager_status_label,
                                                   ui_.on_drivable_space_manager_btn, ui_.off_drivable_space_manager_btn)}};
    const std::string subplanner_status_string = (*msg).data;
    Json::CharReaderBuilder readerBuilder;
    Json::Value root;
    std::string errs;
    std::istringstream s(subplanner_status_string);
    if (!Json::parseFromStream(readerBuilder, s, &root, &errs)) {
        std::cout << "failed to parase Json: " << errs << std::endl;
    }

    for (const auto subplanner_ui : subplanner_ui_map) {
        auto subplanner_name = subplanner_ui.first;
        auto subplanner_label_btn = subplanner_ui.second;
        auto members = root.getMemberNames();
        for (auto it = members.begin(); it != members.end(); ++it) {
            auto title = *it;
            if (title == subplanner_name) {
                auto status_label = std::get<0>(subplanner_label_btn);
                auto on_btn = std::get<1>(subplanner_label_btn);
                auto off_btn = std::get<2>(subplanner_label_btn);

                auto cur_manager = root[title];
                auto field = cur_manager.getMemberNames();
                for (auto iit = field.begin(); iit != field.end(); ++iit) {
                    if (*iit == "isSwitch") {
                        auto is_switch = *iit;
                        if (cur_manager[is_switch] == "on") {
                            status_label->setStyleSheet("background-color: rgba(0, 200, 0, 0.5);");  // 设置背景颜色
                            on_btn->setEnabled(false);
                            off_btn->setEnabled(true);
                        } else if (cur_manager[is_switch] == "off") {
                            status_label->setStyleSheet("background-color: rgba(255, 0, 0, 0.7);");  // 设置背景颜色
                            on_btn->setEnabled(true);
                            off_btn->setEnabled(false);
                        } else {
                            status_label->setStyleSheet("background-color: rgba(255, 0, 0, 0.7);");
                            on_btn->setEnabled(false);
                            off_btn->setEnabled(false);
                        }
                    }
                }
                if (subplanner_name == "trajectory_checker") {
                    on_btn->setEnabled(false);
                    off_btn->setEnabled(false);
                }
            }
        }
    }
}

void TaskRvizPanel::SubTrafficDeviceResCallback(const std_msgs::msg::String::SharedPtr msg)
{
    if (traffic_device_signal_sim_mode_) return;

    const std::string device_status_string = (*msg).data;
    Json::CharReaderBuilder readerBuilder;
    Json::Value root;
    std::string errs;
    std::istringstream s(device_status_string);
    if (!Json::parseFromStream(readerBuilder, s, &root, &errs)) {
        std::cout << "failed to parase Json: " << errs << std::endl;
    }

    auto ExistLocaiton = [this](const u_int8_t &location) -> bool {
        return location_traffic_status_label_lcd_.find(location) != location_traffic_status_label_lcd_.end();
    };

    // status_label->setStyleSheet("background-color: rgba(0, 200, 0, 0.5);");
    auto SetTraffiStatusShow = [this](const u_int8_t &color, const int16_t &time, const bool &twinkle,
                                      QwtTextLabel *label, QLCDNumber *lcd_number) {
        if (color == TrafficDeviceLight::COLOR_RED) {
            label->setStyleSheet("background-color: rgba(200, 0, 0, 0.5);");
        } else if (color == TrafficDeviceLight::COLOR_GREEN) {
            label->setStyleSheet("background-color: rgba(0, 200, 0, 0.5);");
        } else if (color == TrafficDeviceLight::COLOR_YELLOW) {
            label->setStyleSheet("background-color: rgba(255, 255, 0, 0.5);");
        } else {
            label->setStyleSheet("background-color: rgba(156, 156, 156, 1.0);");
        }
        lcd_number->display(time);
        if (twinkle) {
            lcd_number->setStyleSheet("background-color: rgba(255, 255, 0, 0.5);");
        } else {
            lcd_number->setStyleSheet("background-color: black; color: red;");
        }
    };

    if (root.isMember("traffic_device")) {
        if (!root["traffic_device"].isMember("device_id")) {
            std::cout << "traffic device status json don't exist device_id..." << std::endl;
        }
        if (root["traffic_device"].isMember("lights")) {
            auto lights_root = root["traffic_device"]["lights"];
            for (const auto &id_root : lights_root) {
                auto location = id_root["location"].asUInt();
                auto color = id_root["color"].asUInt();
                auto time = id_root["time"].asInt64();
                auto twinkle = id_root["twinkle"].asBool();
                if (ExistLocaiton(static_cast<u_int8_t>(location))) {
                    auto &status_label_time_lcd = location_traffic_status_label_lcd_.at(static_cast<u_int8_t>(location));
                    auto &status_label = std::get<0>(status_label_time_lcd);
                    auto &time_lcd = std::get<1>(status_label_time_lcd);
                    SetTraffiStatusShow(static_cast<u_int8_t>(color), static_cast<int64_t>(time), twinkle, status_label,
                                        time_lcd);
                } else {
                    std::cout << " device panel don't exist location lights panel" << static_cast<u_int8_t>(location)
                              << " ..." << std::endl;
                }
            }
        } else {
            std::cout << "traffic device status json don't exist lights..." << std::endl;
        }

    } else {
        std::cout << "traffic device status json don't exist traffic_device..." << std::endl;
    }
}

void TaskRvizPanel::clickSetMapBtn()
{
    std::cout << "clickSetMapBtn" << std::endl;
    std::string current_map = ui_.map_comboBox->currentText().toStdString();
    std::cout << "set current map: " << current_map << std::endl;

    Json::Value root;
    root["title"] = "request_set_map";
    root["map_folder_name"] = current_map;
    publishJson(root);
}

void TaskRvizPanel::clickDeleteSelectedGoalBtn()
{
    std::cout << "clickDeleteSelectedGoalBtn select_index = " << selected_goal_index_ << std::endl;
    if (selected_goal_index_ != -1) {
        goal_pose_vec_.erase(goal_pose_vec_.begin() + selected_goal_index_);
    } else {
        clearRequestResponseTextBrowser();
        ui_.debug_info_text_browser->setText(QString::fromStdString("error: must select a goal name"));
        return;
    }
    updateGoalTable();
    writeGoalFile();
}

void TaskRvizPanel::clickPlanningSelectedGoalBtn()
{
    std::cout << "clickPlanningSelectedGoalBtn" << std::endl;

    if (selected_goal_index_ != -1) {
        ui_.debug_info_text_browser->clear();
        publishOneGoal(selected_goal_index_);
    } else {
        clearRequestResponseTextBrowser();
        ui_.debug_info_text_browser->setText(QString::fromStdString("error: must select a goal name"));
        return;
    }
}

void TaskRvizPanel::clickLoopPlanningGoalBtn()
{
    std::cout << "clickLoopPlanningGoalBtn" << std::endl;
    if (!loop_planning_worker_->isRunning() && selected_goal_index_ != -1) {
        loop_planning_worker_->setGoalsAndIndex(goal_pose_vec_, selected_goal_index_);
        loop_planning_worker_->start();
    } else {
        std::cout << "loop_planning is running. Skip" << std::endl;
    }
}

void TaskRvizPanel::clickLoopPlanningStopBtn()
{
    std::cout << "clickLoopPlanningStopBtn" << std::endl;
    if (loop_planning_worker_->isRunning()) {
        std::cout << "sendStopToLoopPlanning" << std::endl;
        emit sendStopToLoopPlanning();
        clickCancelNavigation();
    }
}

void TaskRvizPanel::clickGoalPoseTable(QTableWidgetItem *item)
{
    std::cout << "clickGoalPoseTable" << std::endl;
    // QList ui_.goal_pose_table->selectedItems();
    std::cout << "item text: " << item->text().toStdString() << " row:  " << item->row() << " col:" << item->column()
              << std::endl;
    selected_goal_index_ = item->row();
}

void TaskRvizPanel::clickEngageCheckBox()
{
    bool isChecked = ui_.checkBox_engage->isChecked();
    printf("engaged checked: %s\n", isChecked ? "true" : "false");
    std_msgs::msg::Bool msg;
    msg.data = isChecked;
    pub_engaged_->publish(msg);
}

void TaskRvizPanel::clickSaveGoalBtn()
{
    std::string goal_name = ui_.goal_name_edit->text().toStdString();
    std::string goal_pose = ui_.goal_text_browser->toPlainText().toStdString();
    if (goal_name == "" || goal_pose == "") {
        clearRequestResponseTextBrowser();
        if (goal_name == "") {
            ui_.debug_info_text_browser->setText(QString::fromStdString("error: Goal Name has not been set"));
            return;
        }
        if (goal_pose == "") {
            ui_.debug_info_text_browser->setText(QString::fromStdString("error: Goal Pose has not been set"));
            return;
        }
    } else {
        ui_.debug_info_text_browser->clear();
    }

    geometry_msgs::msg::PointStamped new_goal;
    new_goal.header = goal_pose_.header;
    new_goal.point.x = goal_pose_.pose.position.x;
    new_goal.point.y = goal_pose_.pose.position.y;
    new_goal.point.z = tf2::getYaw(goal_pose_.pose.orientation);
    goal_pose_vec_.push_back({goal_name, new_goal});
    writeGoalFile();
    updateGoalTable();

    ui_.goal_name_edit->clear();
    ui_.goal_text_browser->clear();
}

void TaskRvizPanel::clickSimSetFolder()
{
    // Get the folder path from sim_folder_edit
    QString folderPath = ui_.sim_folder_edit->toPlainText();
    if (folderPath.isEmpty()) {
        std::cout << "Error: Please enter a folder path" << std::endl;
        return;
    }

    // Check if the folder exists
    QDir folder(folderPath);
    if (!folder.exists()) {
        std::cout << "Error: Folder does not exist: " << folderPath.toStdString() << std::endl;
        return;
    }

    // Clear the table
    ui_.sim_config_table->clear();
    ui_.sim_config_table->setRowCount(0);

    // Get all YAML files in the folder
    QStringList filters;
    filters << "*.yaml"
            << "*.yml";
    folder.setNameFilters(filters);
    QFileInfoList fileList = folder.entryInfoList();

    // Process each YAML file
    int row = 0;
    for (const QFileInfo &fileInfo : fileList) {
        try {
            SimulationTool sim_data(NULL);
            sim_data.load(fileInfo.absoluteFilePath().toStdString());

            ui_.sim_config_table->insertRow(row);

            QTableWidgetItem *filenameItem = new QTableWidgetItem(fileInfo.fileName());
            ui_.sim_config_table->setItem(row, 0, filenameItem);

            QTableWidgetItem *descriptionItem = new QTableWidgetItem(QString::fromStdString(sim_data.description()));
            ui_.sim_config_table->setItem(row, 1, descriptionItem);

            QTableWidgetItem *mapNameItem = new QTableWidgetItem(QString::fromStdString(sim_data.map_name()));
            ui_.sim_config_table->setItem(row, 2, mapNameItem);

            row++;
        } catch (const YAML::Exception &e) {
            std::cerr << "Error parsing YAML file " << fileInfo.fileName().toStdString() << ": " << e.what() << std::endl;
        }
    }
    ui_.sim_config_table->resizeColumnsToContents();
}

void TaskRvizPanel::clickSimInitSimulation()
{
    // 设置地图
    if (ui_.map_comboBox->findText(sim_data_select_->map_name().c_str())) {
        ui_.map_comboBox->setCurrentText(sim_data_select_->map_name().c_str());
    }
    clickSetMapBtn();

    // 设置车辆位置
    geometry_msgs::msg::PoseWithCovarianceStamped vehicle_pose = sim_data_select_->vehicle_pose();
    pub_initial_pose_->publish(vehicle_pose);

    // 删除webots objects
    sim_data_select_->callDeleteAll();
    // 设置webots objects
    sim_data_select_->callSetObjects();
}

void TaskRvizPanel::clickSimRun()
{
    std::cout << "not implemented" << std::endl;
}
void TaskRvizPanel::clickSimEditSelect()
{
    std::cout << "not implemented" << std::endl;
}
void TaskRvizPanel::clickSimDeleteSelect() {
    // 删除选择的文件

    // Get the currently selected row
    int currentRow = ui_.sim_config_table->currentRow();

    if (currentRow < 0) {
        ui_.debug_info_text_browser->setText("Error: No file selected for deletion");
        return;
    }

    // Get the filename from the selected row
    QTableWidgetItem *filenameItem = ui_.sim_config_table->item(currentRow, 0);
    if (!filenameItem) {
        ui_.debug_info_text_browser->setText("Error: Cannot get filename from selected row");
        return;
    }

    QString filename = filenameItem->text();
    QString folderPath = ui_.sim_folder_edit->toPlainText();

    if (folderPath.isEmpty()) {
        ui_.debug_info_text_browser->setText("Error: No folder path specified");
        return;
    }

    // Construct the full file path
    QString fullFilePath = QDir(folderPath).absoluteFilePath(filename);

    // Check if the file exists
    QFile file(fullFilePath);
    if (!file.exists()) {
        ui_.debug_info_text_browser->setText("Error: File does not exist: " + fullFilePath);
        return;
    }

    // Ask for confirmation before deleting
    QMessageBox::StandardButton reply = QMessageBox::question(
        this,
        "Confirm Deletion",
        "Are you sure you want to delete the file:\n" + filename + "?",
        QMessageBox::Yes | QMessageBox::No,
        QMessageBox::No
    );

    if (reply == QMessageBox::Yes) {
        // Attempt to delete the file
        if (file.remove()) {
            // Remove the row from the table
            ui_.sim_config_table->removeRow(currentRow);
            ui_.debug_info_text_browser->setText("File deleted successfully: " + filename);

            // Update the status message
            int remainingFiles = ui_.sim_config_table->rowCount();
            if (remainingFiles == 0) {
                ui_.debug_info_text_browser->setText("All files deleted. Table is now empty.");
            } else {
                ui_.debug_info_text_browser->setText("File deleted. " + QString::number(remainingFiles) + " files remaining.");
            }
        } else {
            ui_.debug_info_text_browser->setText("Error: Failed to delete file: " + filename);
        }
    } else {
        ui_.debug_info_text_browser->setText("File deletion cancelled by user");
    }
}
void TaskRvizPanel::clickSimSave() {}

void TaskRvizPanel::simTableSelect(QTableWidgetItem *item)
{
    if (!item) {
        return;
    }

    // Get the selected row
    int selectedRow = item->row();

    // Get the filename from the first column
    QTableWidgetItem *filenameItem = ui_.sim_config_table->item(selectedRow, 0);
    QTableWidgetItem *descriptionItem = ui_.sim_config_table->item(selectedRow, 1);
    QTableWidgetItem *mapNameItem = ui_.sim_config_table->item(selectedRow, 2);

    if (filenameItem) {
        QString filename = filenameItem->text();
        QString description = descriptionItem ? descriptionItem->text() : "N/A";
        QString mapName = mapNameItem ? mapNameItem->text() : "N/A";

        // Display selection information
        QString selectionInfo = QString("Selected: %1\nDescription: %2\nMap Name: %3")
                               .arg(filename)
                               .arg(description)
                               .arg(mapName);

        ui_.debug_info_text_browser->setText(selectionInfo);

        std::cout << "Selected simulation config: " << filename.toStdString()
                  << " (Row: " << selectedRow << ")" << std::endl;

        // Optionally load the YAML file for further processing
        QString folderPath = ui_.sim_folder_edit->toPlainText();
        if (!folderPath.isEmpty()) {
            QString fullFilePath = QDir(folderPath).absoluteFilePath(filename);
            std::cout << "Full file path: " << fullFilePath.toStdString() << std::endl;

            // Here you can add code to load and process the selected YAML file
            // For example: loadSimulationConfig(fullFilePath.toStdString());
        }
    }
}

void TaskRvizPanel::loadGoalFile()
{
    // json file -> goal_pose_vec_
    Json::Reader reader;
    Json::Value root;
    std::ifstream srcFile(goal_file_, std::ios::binary); /*定义一个ifstream流对象，与文件demo.json进行关联*/
    if (!srcFile.is_open()) {
        std::cout << "Fail to open " << goal_file_ << std::endl;
        return;
    }

    if (reader.parse(srcFile, root)) {
        Json::Value all_goals = root["goals"];
        std::cout << "goal size: " << all_goals.size() << std::endl;
        goal_pose_vec_.clear();
        goal_pose_vec_.reserve(all_goals.size());
        for (int i = 0; i < all_goals.size(); i++) {
            geometry_msgs::msg::PointStamped pose;
            pose.header.stamp = rclcpp::Time(all_goals[i]["stamp"].asDouble());
            pose.header.frame_id = all_goals[i]["frame_id"].asString();
            pose.point.x = all_goals[i]["x"].asDouble();
            pose.point.y = all_goals[i]["y"].asDouble();
            pose.point.z = all_goals[i]["theta"].asDouble();
            goal_pose_vec_.push_back({all_goals[i]["goal_name"].asString(), pose});
        }
    }
    updateGoalTable();
}
void TaskRvizPanel::writeGoalFile()
{
    // goal_pose_vec_ -> json file
    Json::Value all_goals;
    for (int i = 0; i < goal_pose_vec_.size(); i++) {
        Json::Value one_goal;
        one_goal["goal_name"] = goal_pose_vec_[i].first;
        one_goal["frame_id"] = goal_pose_vec_[i].second.header.frame_id;
        one_goal["stamp"] = rclcpp::Time(goal_pose_vec_[i].second.header.stamp).seconds();
        one_goal["x"] = goal_pose_vec_[i].second.point.x;
        one_goal["y"] = goal_pose_vec_[i].second.point.y;
        one_goal["theta"] = goal_pose_vec_[i].second.point.z;
        all_goals.append(one_goal);
    }
    Json::Value root;
    Json::StyledWriter sw;
    root["goals"] = all_goals;
    std::cout << sw.write(root) << std::endl;
    /*输出到JSON文件*/
    std::ofstream desFile(goal_file_, std::ios::out);
    if (!desFile.is_open()) {
        std::cout << "Fail to update " << goal_file_ << std::endl;
        return;
    }
    desFile << sw.write(root);
    desFile.close();
}

void TaskRvizPanel::updateGoalTable()
{
    ui_.goal_pose_table->setRowCount(goal_pose_vec_.size());
    for (int i = 0; i < goal_pose_vec_.size(); i++) {
        ui_.goal_pose_table->setItem(i, 0, new QTableWidgetItem(goal_pose_vec_[i].first.c_str()));
        char goal_pose_string[200];
        sprintf(goal_pose_string, "%.3f, %.3f, %.3f", goal_pose_vec_[i].second.point.x,
                goal_pose_vec_[i].second.point.y, goal_pose_vec_[i].second.point.z);
        ui_.goal_pose_table->setItem(i, 1, new QTableWidgetItem(goal_pose_string));
    }
    QHeaderView *header = ui_.goal_pose_table->horizontalHeader();
    header->setSectionResizeMode(QHeaderView::ResizeToContents);
}
void TaskRvizPanel::publishOneGoal(int index)
{
    std::cout << "publishOneGoal index: " << index << std::endl;
    Json::Value one_goal;
    one_goal["title"] = "request_routing";
    one_goal["frame_id"] = goal_pose_vec_[index].second.header.frame_id;
    one_goal["stamp"] = rclcpp::Time(goal_pose_vec_[index].second.header.stamp).seconds();
    one_goal["x"] = goal_pose_vec_[index].second.point.x;
    one_goal["y"] = goal_pose_vec_[index].second.point.y;
    one_goal["theta"] = goal_pose_vec_[index].second.point.z;
    one_goal["only_visualization"] = false;
    publishJson(one_goal);

    is_goal_pose_received_ = false;
    ui_.goal_text_browser->clear();
}
void TaskRvizPanel::clickStartMapping()
{
    Json::Value root;
    root["title"] = "request_start_mapping";
    std::string map_folder_name = ui_.map_folder_edit->text().toStdString();
    if (map_folder_name == "") {
        clearRequestResponseTextBrowser();
        ui_.debug_info_text_browser->setText(QString::fromStdString("error: Map Folder Name has not been set"));
        return;
    } else {
        ui_.debug_info_text_browser->clear();
        root["map_folder_name"] = map_folder_name;
    }
    publishJson(root);
}

void TaskRvizPanel::clickSaveMapping()
{
    Json::Value root;
    root["title"] = "request_save_mapping";
    publishJson(root);
}

void TaskRvizPanel::clickCancelMapping()
{
    Json::Value root;
    root["title"] = "request_cancel_mapping";
    publishJson(root);
}

void TaskRvizPanel::clickManualRelocate()
{
    Json::Value root;
    root["title"] = "request_manual_relocate";
    if (is_initial_pose_received_) {
        ui_.debug_info_text_browser->clear();
        root["x"] = initial_pose_.pose.pose.position.x;
        root["y"] = initial_pose_.pose.pose.position.y;
        root["theta"] = tf2::getYaw(initial_pose_.pose.pose.orientation);
        is_initial_pose_received_ = false;
        ui_.locate_text_browser->clear();
    } else {
        clearRequestResponseTextBrowser();
        ui_.debug_info_text_browser->setText(QString::fromStdString("error: initial pose is not set"));
        return;
    }

    publishJson(root);
}

void TaskRvizPanel::clickGpsRelocate()
{
    Json::Value root;
    root["title"] = "request_gps_relocate";
    publishJson(root);
}

void TaskRvizPanel::clickCancelRelocate()
{
    Json::Value root;
    root["title"] = "request_cancel_relocate";
    publishJson(root);
}

void TaskRvizPanel::clickRoutingPlanning()
{
    Json::Value root;
    root["title"] = "request_routing";
    if (is_goal_pose_received_) {
        ui_.debug_info_text_browser->clear();
        root["frame_id"] = goal_pose_.header.frame_id;
        root["stamp"] = goal_pose_.header.stamp.sec + goal_pose_.header.stamp.nanosec * 1e-9;
        root["x"] = goal_pose_.pose.position.x;
        root["y"] = goal_pose_.pose.position.y;
        root["theta"] = tf2::getYaw(goal_pose_.pose.orientation);
        root["max_speed"] = ui_.max_speed_doubleSpinBox->value();
        is_goal_pose_received_ = false;
        ui_.goal_text_browser->clear();
    } else {
        clearRequestResponseTextBrowser();
        ui_.debug_info_text_browser->setText(QString::fromStdString("error: goal pose is not set"));
        return;
    }
    root["only_visualization"] = false;
    publishJson(root);
}

void TaskRvizPanel::clickFreeNavigation()
{
    Json::Value root;
    root["title"] = "request_free_navigation";
    if (is_goal_pose_received_) {
        ui_.debug_info_text_browser->setText(QString::fromStdString(""));
        root["frame_id"] = goal_pose_.header.frame_id;
        root["x"] = goal_pose_.pose.position.x;
        root["y"] = goal_pose_.pose.position.y;
        root["theta"] = tf2::getYaw(goal_pose_.pose.orientation);
        is_goal_pose_received_ = false;
        ui_.goal_text_browser->clear();
    } else {
        clearRequestResponseTextBrowser();
        ui_.debug_info_text_browser->setText(QString::fromStdString("error: goal pose is not set"));
        return;
    }
    publishJson(root);
}

void TaskRvizPanel::clickCancelNavigation()
{
    Json::Value root;
    root["title"] = "request_cancel_navigation";
    publishJson(root);
}

void TaskRvizPanel::clickSetPlanningSpeed()
{
    auto current_max_speed = ui_.max_speed_doubleSpinBox->value();
    std_msgs::msg::Float32 max_speed_msg;
    max_speed_msg.data = current_max_speed;
    pub_planning_max_speed_->publish(max_speed_msg);
}

void TaskRvizPanel::clickSelectTrafficLight(const std::string &light_color)
{
    if (light_color == "red") {
        traffic_guide_.light_color.light_color = custom_msgs::msg::TrafficLight::RED;
    } else if (light_color == "green") {
        traffic_guide_.light_color.light_color = custom_msgs::msg::TrafficLight::GREEN;
    } else if (light_color == "yellow") {
        traffic_guide_.light_color.light_color = custom_msgs::msg::TrafficLight::YELLOW;
    } else {
        traffic_guide_.light_color.light_color = custom_msgs::msg::TrafficLight::UNKNOWN;
    }
    std::cout << "select traffic " << light_color.c_str()
              << " light color: " << int(traffic_guide_.light_color.light_color) << std::endl;
}

void TaskRvizPanel::clickSelectTrafficDeviceLight(const std::string &rule, const std::string &color)
{
    ui_.east_traffic_rule_box->setCurrentText(QString::fromStdString(rule));
    ui_.south_traffic_rule_box->setCurrentText(QString::fromStdString(rule));
    ui_.west_traffic_rule_box->setCurrentText(QString::fromStdString(rule));
    ui_.north_traffic_rule_box->setCurrentText(QString::fromStdString(rule));
    clickSelectTrafficDeviceRule(TrafficDeviceLight::LOCATION_EAST, ui_.north_traffic_rule_box);
    clickSelectTrafficDeviceRule(TrafficDeviceLight::LOCATION_SOUTH, ui_.south_traffic_rule_box);
    clickSelectTrafficDeviceRule(TrafficDeviceLight::LOCATION_WEST, ui_.west_traffic_rule_box);
    clickSelectTrafficDeviceRule(TrafficDeviceLight::LOCATION_NORTH, ui_.north_traffic_rule_box);

    ui_.east_traffic_color_box->setCurrentText(QString::fromStdString(color));
    ui_.south_traffic_color_box->setCurrentText(QString::fromStdString(color));
    ui_.west_traffic_color_box->setCurrentText(QString::fromStdString(color));
    ui_.north_traffic_color_box->setCurrentText(QString::fromStdString(color));
    clickSelectTrafficDeviceColor(TrafficDeviceLight::LOCATION_EAST, ui_.east_traffic_color_box);
    clickSelectTrafficDeviceColor(TrafficDeviceLight::LOCATION_SOUTH, ui_.south_traffic_color_box);
    clickSelectTrafficDeviceColor(TrafficDeviceLight::LOCATION_WEST, ui_.west_traffic_color_box);
    clickSelectTrafficDeviceColor(TrafficDeviceLight::LOCATION_NORTH, ui_.north_traffic_color_box);
}

void TaskRvizPanel::clickSelectTrafficRule(const std::string &rule)
{
    if (rule == "straight") {
        traffic_guide_.rule = custom_msgs::msg::TrafficGuide::STRAIGHT;
    } else if (rule == "turn_left") {
        traffic_guide_.rule = custom_msgs::msg::TrafficGuide::TURN_LEFT;
    } else if (rule == "turn_right") {
        traffic_guide_.rule = custom_msgs::msg::TrafficGuide::TURN_RIGHT;
    } else {
        traffic_guide_.rule = custom_msgs::msg::TrafficGuide::UNKNOWN;
        return;
    }
    traffic_respone_ = true;
    std::cout << "select rule " << int(traffic_guide_.rule) << std::endl;
}

void TaskRvizPanel::clickSelectTrafficDeviceRule(const u_int32_t &location, const QComboBox *comboBox_ptr)
{
    auto rule = comboBox_ptr->currentText().toStdString();
    custom_msgs::msg::TrafficDeviceLight light;
    light.location = location;
    if (rule == "Straight") {
        light.rule = custom_msgs::msg::TrafficDeviceLight::RULE_STRAIGHT;
    } else if (rule == "Left") {
        light.rule = custom_msgs::msg::TrafficDeviceLight::RULE_TURN_LEFT;
    } else if (rule == "Right") {
        light.rule = custom_msgs::msg::TrafficDeviceLight::RULE_TURN_RIGHT;
    } else {
        light.rule = custom_msgs::msg::TrafficDeviceLight::RULE_UNKNOWN;
    }

    auto const &it = std::find_if(traffic_device_response_.device_status.lights.begin(),
                                  traffic_device_response_.device_status.lights.end(),
                                  [location](const custom_msgs::msg::TrafficDeviceLight &light_member) {
                                      return light_member.location == location;
                                  });
    if (it != traffic_device_response_.device_status.lights.end()) {
        // auto &device_light = *it;
        // device_light.rule = light.rule;
        it->rule = light.rule;
    } else {
        traffic_device_response_.device_status.lights.push_back(light);
    }
    for (const auto light_status : traffic_device_response_.device_status.lights) {
        std::cout << "select " << int(light_status.location) << " traffic device rule " << int(light_status.rule)
                  << std::endl;
    }
}

void TaskRvizPanel::clickSelectTrafficDeviceColor(const u_int32_t &location, const QComboBox *comboBox_ptr)
{
    auto color = comboBox_ptr->currentText().toStdString();
    custom_msgs::msg::TrafficDeviceLight light;
    light.location = location;
    if (color == "Red") {
        light.color = custom_msgs::msg::TrafficDeviceLight::COLOR_RED;
    } else if (color == "Green") {
        light.color = custom_msgs::msg::TrafficDeviceLight::COLOR_GREEN;
    } else if (color == "Yellow") {
        light.color = custom_msgs::msg::TrafficDeviceLight::COLOR_YELLOW;
    } else {
        light.color = custom_msgs::msg::TrafficDeviceLight::COLOR_UNKNOWN;
    }

    auto const &it = std::find_if(traffic_device_response_.device_status.lights.begin(),
                                  traffic_device_response_.device_status.lights.end(),
                                  [location](const custom_msgs::msg::TrafficDeviceLight &light_member) {
                                      return light_member.location == location;
                                  });
    if (it != traffic_device_response_.device_status.lights.end()) {
        // auto &device_light = *it;
        // device_light.color = light.color;
        it->color = light.color;
    } else {
        traffic_device_response_.device_status.lights.push_back(light);
    }
    for (const auto &light_status : traffic_device_response_.device_status.lights) {
        std::cout << "select " << int(light_status.location) << " btn: " << color.c_str() << " traffic device color "
                  << int(light_status.color) << std::endl;
    }
}

void TaskRvizPanel::clickSetTrafficDeviceLightTime()
{
    auto ExistLocaiton = [](const u_int8_t &location,
                            custom_msgs::srv::TrafficDeviceService_Response &traffic_device_response) {
        auto const &it = std::find_if(traffic_device_response.device_status.lights.begin(),
                                      traffic_device_response.device_status.lights.end(),
                                      [location](const custom_msgs::msg::TrafficDeviceLight &light_member) {
                                          return light_member.location == location;
                                      });
        return it;
    };
    std::unordered_map<u_int8_t, QSpinBox *> location_time_spin_boxs = {
        {TrafficDeviceLight::LOCATION_EAST, ui_.east_light_time_spinBox},
        {TrafficDeviceLight::LOCATION_SOUTH, ui_.south_light_time_spinBox},
        {TrafficDeviceLight::LOCATION_WEST, ui_.west_light_time_spinBox},
        {TrafficDeviceLight::LOCATION_NORTH, ui_.north_light_time_spinBox}};

    for (const auto &location_time_spin_box : location_time_spin_boxs) {
        auto &location = location_time_spin_box.first;
        auto &time_box = location_time_spin_box.second;
        auto it = ExistLocaiton(location, traffic_device_response_);
        if (it != traffic_device_response_.device_status.lights.end()) {
            (*it).time = time_box->value();
        } else {
            custom_msgs::msg::TrafficDeviceLight light;
            light.location = location;
            light.time = time_box->value();
            traffic_device_response_.device_status.lights.push_back(light);
        }
    }
    traffic_device_response_.device_status.header.stamp = rclcpp::Clock().now();
    traffic_time_set_ = true;
    for (const auto &light_status : traffic_device_response_.device_status.lights) {
        std::cout << "select " << int(light_status.location) << " traffic device time " << int(light_status.time)
                  << std::endl;
    }
    // ui_.set_traffic_time_btn->setEnabled(false);
}

void TaskRvizPanel::clickSelectRainFall()
{
    enum class RainFall : unsigned int { Sunny, LightRain, ModerateRain, HeavyRain };
    std::unordered_map<std::string, RainFall> rain_fall_map = {
        {"Sunny", RainFall::Sunny},
        {"Light_Rain", RainFall::LightRain},
        {"Moderate_Rain", RainFall::ModerateRain},
        {"Heavy_Rain", RainFall::HeavyRain},
    };
    auto current_text = ui_.rain_fall_box->currentText();
    auto current_rain_fall = current_text.toStdString();
    std_msgs::msg::Int32 rain_fall_msg;
    rain_fall_msg.data = static_cast<int32_t>(rain_fall_map[current_rain_fall]);
    pub_rain_fall_->publish(rain_fall_msg);
}

void TaskRvizPanel::switchSubPlanner(const std::string &subplanner, const std::string &status)
{
    Json::Value subplanner_root;
    subplanner_root["isSwitch"] = status;
    // if (subplanner_switch_root_array_.isMember(subplanner)) {
    subplanner_switch_root_array_[subplanner] = subplanner_root;
    // } else {
    // subplanner_switch_root_array_[subplanner].append(subplanner_root);
    // }
    Json::FastWriter writer;
    std_msgs::msg::String msg;
    msg.data = writer.write(subplanner_switch_root_array_);
    pub_subplanner_switch_->publish(msg);
}

void TaskRvizPanel::taskResponseCallback(const std_msgs::msg::String::SharedPtr msg)
{
    ui_.task_response_text_browser->clear();
    ui_.task_response_text_browser->setText(QString::fromStdString(msg->data));
}

void TaskRvizPanel::moduleStateCallback(const umodule_msgs::msg::ModuleState::SharedPtr msg)
{
    if (msg->header.frame_id == "localization_manager" && msg->state != last_localization_state_) {
        std::cout << "moduleStateCallback header:" << msg->header.frame_id << " state:" << int(msg->state) << std::endl;
        ui_.label_localization->setText(
            QString::fromStdString(" Localization: " + LOCALIZATION_STATE_DESC_MAP.at(msg->state)));
        last_localization_state_ = msg->state;
    }
    if (msg->header.frame_id == "planning_manager" && msg->state != last_planning_state_) {
        std::cout << "moduleStateCallback header:" << msg->header.frame_id << " state:" << int(msg->state) << std::endl;
        ui_.label_planning->setText(QString::fromStdString(" Planning: " + PLANNING_STATE_DESC_MAP.at(msg->state)));
        last_planning_state_ = msg->state;
        emit sendPlanningStateToLoopPlanning(msg->state);

        if (msg->state == state_msgs::msg::PlanningState::PLANNING_UNINIT ||
            msg->state == state_msgs::msg::PlanningState::PLANNING_ERROR)
            ui_.label_planning->setStyleSheet("background-color: red;");
        else if (msg->state == state_msgs::msg::PlanningState::PLANNING_FINISH)
            ui_.label_planning->setStyleSheet("background-color: lightgray;");
        else if (msg->state == state_msgs::msg::PlanningState::PLANNING_CANCEL)
            ui_.label_planning->setStyleSheet("background-color: yellow;");
        else
            ui_.label_planning->setStyleSheet("background-color: rgb(115, 210, 22);");
    }
    if (msg->header.frame_id == "routing_manager" && msg->state != last_routing_state_) {
        std::cout << "moduleStateCallback header:" << msg->header.frame_id << " state:" << int(msg->state) << std::endl;
        ui_.label_routing->setText(QString::fromStdString(" Routing: " + ROUTING_STATE_DESC_MAP.at(msg->state)));
        last_routing_state_ = msg->state;
    }
}

void TaskRvizPanel::loopFunc()
{
    // 在这里执行耗时的操作
    for (int i = 0; i < 20; ++i) {
        std::cout << "############  " << i << std::endl;
        QThread::sleep(1);  // 模拟耗时操作
    }

    // std::cout << "[**LOOP PLANNING**] Enter Loop Planning from " << selected_goal_index_ << std::endl;
    // std::cout << "\033[1;33m 22QThread::currentThreadId()" << QThread::currentThreadId() << "\033[0m" << std::endl;

    // loop_index_ = selected_goal_index_;
    // while (rclcpp::ok() && loop_index_ < goal_pose_vec_.size() && loop_planning_start_) {
    //     publishOneGoal(loop_index_);
    //     rclcpp::Rate rate(5);
    //     rclcpp::Time start_publish_time = rclcpp::Clock().now();
    //     bool start_success = false;
    //     while (rclcpp::ok() && loop_planning_start_) {
    //         std::cout << "[**LOOP PLANNING**] Wait for start PLanning status: " << int(last_planning_state_) << std::endl;
    //         if (last_planning_state_ == state_msgs::msg::PlanningState::PLANNING_RUNNING_LANE) {
    //             std::cout << "PLanning " << loop_index_ << " start success" << std::endl;
    //             start_success = true;
    //             break;
    //         }
    //         if (rclcpp::Clock().now() - start_publish_time > rclcpp::Duration(10, 0)) {
    //             std::cout << "[**LOOP PLANNING**] PLanning " << loop_index_ << " start timeout" << std::endl;
    //             loop_index_++;
    //             break;
    //         }
    //         rate.sleep();
    //     }
    //     while (rclcpp::ok() && loop_planning_start_ && start_success) {
    //         std::cout << "[**LOOP PLANNING**] Wait for finish PLanning status: " << int(last_planning_state_) << std::endl;
    //         if (last_planning_state_ == state_msgs::msg::PlanningState::PLANNING_FINISH ||
    //             last_planning_state_ == state_msgs::msg::PlanningState::PLANNING_CANCEL ||
    //             last_planning_state_ == state_msgs::msg::PlanningState::PLANNING_ERROR) {
    //             std::cout << "[**LOOP PLANNING**] PLanning " << loop_index_ << " finish" << std::endl;
    //             loop_index_++;
    //             break;
    //         }
    //         rate.sleep();
    //     }
    // }
    // std::cout << "[**LOOP PLANNING**] Exit Loop Planning" << std::endl;
    // emit loopFuncFinished();
}

void TaskRvizPanel::clearRequestResponseTextBrowser()
{
    ui_.task_response_text_browser->clear();
    ui_.task_request_text_browser->clear();
}

void TaskRvizPanel::setGoalPoseSpinBox(const geometry_msgs::msg::PoseStamped::SharedPtr &msg)
{
    ui_.goal_pose_x_spin_box->setValue(msg->pose.position.x);
    ui_.goal_pose_y_spin_box->setValue(msg->pose.position.y);
    ui_.goal_pose_theta_spin_box->setValue(tf2::getYaw(msg->pose.orientation));
}

void TaskRvizPanel::clickGoalSpinBox2GoalPose()
{
    goal_pose_.pose.position.x = ui_.goal_pose_x_spin_box->value();
    goal_pose_.pose.position.y = ui_.goal_pose_y_spin_box->value();
    tf2::Quaternion q;
    q.setRPY(0, 0, ui_.goal_pose_theta_spin_box->value());
    goal_pose_.pose.orientation = tf2::toMsg(q);
    is_goal_pose_received_ = true;
}

void TaskRvizPanel::publishJson(Json::Value &value)
{
    ui_.debug_info_text_browser->clear();
    ui_.task_response_text_browser->clear();
    Json::FastWriter writer;
    std_msgs::msg::String msg;
    value["session_id"] = Json::Value(session_id_++);
    msg.data = writer.write(value);
    pub_task_request_->publish(msg);
    ui_.task_request_text_browser->setText(QString::fromStdString(msg.data));
}

void TaskRvizPanel::resizeEvent(QResizeEvent *event)
{
    // 获取窗口大小
    QSize windowSize = size();

    // 调用基类的 resizeEvent 函数
    rviz_common::Panel::resizeEvent(event);

    // 窗口大小改变时改变最外层 gridLayoutWidget 大小
    ui_.gridLayoutWidget->setGeometry(0, 0, windowSize.width(), windowSize.height());

    // 调整外层 teb_widget 大小和窗格保持一致 (tab和layout绑定之后不需要了)
    // QWidget *teb_widget = ui_.tabWidget->currentWidget();
    // QWidget *layout_widget = teb_widget->findChild<QWidget *>();
    // if (layout_widget) {
    //     layout_widget->setGeometry(0, 0, teb_widget->size().width(), teb_widget->size().height());
    // }
}

// 重载父类的功能
void TaskRvizPanel::save(rviz_common::Config config) const
{
    rviz_common::Panel::save(config);
    // config.mapSetValue("Topic", output_topic_);
}

// 重载父类的功能，加载配置数据
void TaskRvizPanel::load(const rviz_common::Config &config)
{
    rviz_common::Panel::load(config);
    // QString topic;
    // if (config.mapGetString("Topic", &topic)) {
    //     output_topic_editor_->setText(topic);
    //     updateTopic();
    // }
}

void TaskRvizPanel::handleWorkerIndex(int index)
{
    // char status_string[1000];
    std::string status_string;
    std::string running_str;
    if (loop_planning_worker_->isRunning())
        running_str = "<span style='color: green;'>Start</span>";
    else
        running_str = "<span style='color: red;'>Stop</span>";

    std::string cur_name = "None";
    if (ui_.goal_pose_table->item(index, 0)) cur_name = ui_.goal_pose_table->item(index, 0)->text().toStdString();
    status_string = "Loop Status: " + running_str + "<br>" + "<span style='color: blue;'>** Current Goal **</span><br>" +
                    "Index: " + std::to_string(index + 1) + "<br>" + "Name: " + cur_name + "<br>" +
                    "State: " + PLANNING_STATE_DESC_MAP.at(last_planning_state_).c_str();

    ui_.label_loop_planning->setText(QString::fromStdString(status_string));
}

}  // namespace task_rviz_plugin

// 声明此类是一个rviz的插件
#include <pluginlib/class_list_macros.hpp>  // NOLINT
PLUGINLIB_EXPORT_CLASS(task_rviz_plugin::TaskRvizPanel, rviz_common::Panel)
