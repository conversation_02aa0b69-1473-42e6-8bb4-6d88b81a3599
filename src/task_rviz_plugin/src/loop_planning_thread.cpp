#include "loop_planning_thread.hpp"
#include "task_rviz_plugin_panel.hpp"
#include <state_msgs/msg/planning_state.hpp>

void WorkerThread::run()
{
    loop_start_ = true;
    std::cout << "[**LOOP PLANNING**] Enter Loop Planning from " << loop_index_ << std::endl;
    while (loop_start_ && loop_index_ < goal_pose_vec_.size()) {
        emit sendPlanningIndex(loop_index_);

        rclcpp::Rate rate(5);
        rclcpp::Time start_publish_time = rclcpp::Clock().now();
        bool start_success = false;
        while (loop_start_) {
            std::cout << "[**LOOP PLANNING**] Wait for start PLanning status: " << int(planning_state_) << std::endl;
            if (planning_state_ == state_msgs::msg::PlanningState::PLANNING_RUNNING_LANE) {
                std::cout << "PLanning " << loop_index_ << " start success" << std::endl;
                start_success = true;
                break;
            }
            if (rclcpp::Clock().now() - start_publish_time > rclcpp::Duration(10, 0)) {
                std::cout << "[**LOOP PLANNING**] PLanning " << loop_index_ << " start timeout" << std::endl;
                loop_index_++;
                if (one_loop_checkbox_ == Qt::Unchecked && loop_index_ == goal_pose_vec_.size()) loop_index_ = 0;
                break;
            }
            rate.sleep();
        }
        while (loop_start_ && start_success) {
            std::cout << "[**LOOP PLANNING**] Wait for finish PLanning status: " << int(planning_state_) << std::endl;
            if (planning_state_ == state_msgs::msg::PlanningState::PLANNING_FINISH ||
                planning_state_ == state_msgs::msg::PlanningState::PLANNING_CANCEL ||
                planning_state_ == state_msgs::msg::PlanningState::PLANNING_ERROR) {
                std::cout << "[**LOOP PLANNING**] PLanning " << loop_index_ << " finish" << std::endl;
                sleep(2);
                loop_index_++;
                if (one_loop_checkbox_ == Qt::Unchecked && loop_index_ == goal_pose_vec_.size()) loop_index_ = 0;
                break;
            }
            rate.sleep();
        }
    }
    std::cout << "[**LOOP PLANNING**] Exit Loop Planning" << std::endl;
}

void WorkerThread::handlePlanningState(uint8_t state)
{
    emit sendCurStatus(loop_index_);
    if (!isRunning()) return;
    planning_state_ = state;
    std::cout << "[**LOOP PLANNING**] Get state: " << (int)planning_state_ << std::endl;
}
void WorkerThread::handleStop()
{
    if (isRunning()) {
        std::cout << "[**LOOP PLANNING**] Stop Loop Planning" << std::endl;
        loop_start_ = false;
        quit();
    }
}
void WorkerThread::handleOneLoopBox(int val)
{
    std::cout << "[**LOOP PLANNING**] Get OneLoopBox: " << val << std::endl;
    one_loop_checkbox_ = val;
}