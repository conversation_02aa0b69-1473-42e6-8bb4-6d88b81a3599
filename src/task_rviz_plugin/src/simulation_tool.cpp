#include "simulation_tool.hpp"
#include <tf2/LinearMath/Quaternion.h>
#include <tf2/utils.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#include <fstream>

SimulationTool::SimulationTool(rclcpp::Node::SharedPtr node)
{
    if (node) {
        node_ = node;
        update_objects_client_ =
            node->template create_client<webots_objects_msgs::srv::UpdateObjects>("update_objects");
    }
}

void SimulationTool::load(const std::string &file_path)
{
    std::cout << "load simulation data from file: " << file_path << std::endl;
    try {
        YAML::Node config = YAML::LoadFile(file_path);
        description_ = config["description"].as<std::string>();
        map_name_ = config["map_name"].as<std::string>();
        sumo_net_name_ = config["sumo_net_name"].as<std::string>();

        std::cout << "description: " << description_ << " map_name: " << map_name_
                  << " sumo_net_name: " << sumo_net_name_ << std::endl;

        double vehicle_pose_x = config["vehicle_pose"]["x"].as<double>();
        double vehicle_pose_y = config["vehicle_pose"]["y"].as<double>();
        double vehicle_pose_theta = config["vehicle_pose"]["theta"].as<double>();
        vehicle_pose_.pose.pose.position.x = vehicle_pose_x;
        vehicle_pose_.pose.pose.position.y = vehicle_pose_y;
        tf2::Quaternion vehicle_pose_quat;
        vehicle_pose_quat.setRPY(0.0, 0.0, vehicle_pose_theta);
        vehicle_pose_.pose.pose.orientation = tf2::toMsg(vehicle_pose_quat);

        std::cout << "vehicle_pose_x: " << vehicle_pose_x << " vehicle_pose_y: " << vehicle_pose_y
                  << " vehicle_pose_theta: " << vehicle_pose_theta << std::endl;

        double target_pose_x = config["target_pose"]["x"].as<double>();
        double target_pose_y = config["target_pose"]["y"].as<double>();
        double target_pose_theta = config["target_pose"]["theta"].as<double>();
        target_pose_.position.x = target_pose_x;
        target_pose_.position.y = target_pose_y;
        tf2::Quaternion target_pose_quat;
        target_pose_quat.setRPY(0.0, 0.0, target_pose_theta);
        target_pose_.orientation = tf2::toMsg(target_pose_quat);

        std::cout << "target_pose_x: " << target_pose_x << " target_pose_y: " << target_pose_y
                  << " target_pose_theta: " << target_pose_theta << std::endl;

        YAML::Node obstacles = config["obstacle"];
        if (obstacles.IsMap()) {
            for (const auto &obstacle_pair : obstacles) {
                std::string obstacle_name = obstacle_pair.first.as<std::string>();
                YAML::Node obstacle_data = obstacle_pair.second;

                custom_msgs::msg::PerceptionObject obstacle;
                obstacle.id = obstacle_data["id"].as<int>();
                obstacle.pose.position.x = obstacle_data["x"].as<double>();
                obstacle.pose.position.y = obstacle_data["y"].as<double>();
                obstacle.pose.position.z = obstacle_data["z"].IsDefined() ? obstacle_data["z"].as<double>() : 0.0;

                double obstacle_theta = obstacle_data["theta"].as<double>();
                tf2::Quaternion obstacle_quat;
                obstacle_quat.setRPY(0.0, 0.0, obstacle_theta);
                obstacle.pose.orientation = tf2::toMsg(obstacle_quat);

                obstacle.size.x = obstacle_data["size_x"].as<double>();
                obstacle.size.y = obstacle_data["size_y"].as<double>();
                obstacle.size.z = obstacle_data["size_z"].as<double>();

                // Add to objects list
                objects_.objects.push_back(obstacle);

                std::cout << "Loaded obstacle: " << obstacle_name << " id: " << obstacle.id << " pos: ("
                          << obstacle.pose.position.x << ", " << obstacle.pose.position.y << ", "
                          << obstacle.pose.position.z << ")"
                          << " theta: " << obstacle_theta << " size: (" << obstacle.size.x << ", " << obstacle.size.y
                          << ", " << obstacle.size.z << ")" << std::endl;
            }
        }
    } catch (const YAML::Exception &e) {
        std::cerr << "Error parsing YAML file " << file_path << ": " << e.what() << std::endl;
    }
}

void SimulationTool::save(const std::string &file_path)
{
    std::cout << "save simulation data to file: " << file_path << std::endl;
    try {
        YAML::Emitter out;
        out << YAML::BeginMap;
        out << YAML::Key << "description" << YAML::Value << description_;
        out << YAML::Key << "map_name" << YAML::Value << map_name_;
        out << YAML::Key << "sumo_net_name" << YAML::Value << sumo_net_name_;

        out << YAML::Key << "vehicle_pose" << YAML::Value << YAML::BeginMap;
        out << YAML::Key << "x" << YAML::Value << vehicle_pose_.pose.pose.position.x;
        out << YAML::Key << "y" << YAML::Value << vehicle_pose_.pose.pose.position.y;
        out << YAML::Key << "theta" << YAML::Value << tf2::getYaw(vehicle_pose_.pose.pose.orientation);
        out << YAML::EndMap;

        out << YAML::Key << "target_pose" << YAML::Value << YAML::BeginMap;
        out << YAML::Key << "x" << YAML::Value << target_pose_.position.x;
        out << YAML::Key << "y" << YAML::Value << target_pose_.position.y;
        out << YAML::Key << "theta" << YAML::Value << tf2::getYaw(target_pose_.orientation);
        out << YAML::EndMap;

        out << YAML::Key << "obstacle" << YAML::Value << YAML::BeginMap;
        for (const auto &obstacle : objects_.objects) {
            out << YAML::Key << "obs" << YAML::Value << YAML::BeginMap;
            out << YAML::Key << "id" << YAML::Value << obstacle.id;
            out << YAML::Key << "x" << YAML::Value << obstacle.pose.position.x;
            out << YAML::Key << "y" << YAML::Value << obstacle.pose.position.y;
            out << YAML::Key << "theta" << YAML::Value << tf2::getYaw(obstacle.pose.orientation);
            out << YAML::Key << "size_x" << YAML::Value << obstacle.size.x;  // todo
            out << YAML::Key << "size_y" << YAML::Value << obstacle.size.y;
            out << YAML::Key << "size_z" << YAML::Value << obstacle.size.z;
            out << YAML::Key << "velocity" << YAML::Value << 0.0;
            out << YAML::EndMap;
        }
        out << YAML::EndMap;

        out << YAML::EndMap;

        std::ofstream file_out(file_path);
        file_out << out.c_str();
    } catch (const YAML::Exception &e) {
        std::cerr << "Error writing YAML file " << file_path << ": " << e.what() << std::endl;
    }
}

webots_objects_msgs::srv::UpdateObjects::Response::SharedPtr SimulationTool::callService(
    webots_objects_msgs::srv::UpdateObjects::Request::SharedPtr &request)
{
    // Call the UpdateObjects service
    if (!update_objects_client_->wait_for_service(std::chrono::seconds(1))) {
        std::cerr << "UpdateObjects service not available" << std::endl;
        return nullptr;
    }

    auto future = update_objects_client_->async_send_request(
        request, [this](rclcpp::Client<webots_objects_msgs::srv::UpdateObjects>::SharedFuture future) {
            auto response = future.get();
            if (response) {
                RCLCPP_INFO(node_->get_logger(), "UpdateObjects service response: %s", response->response.data.c_str());
            }
        });
}

void SimulationTool::callDeleteAll()
{
    auto request = std::make_shared<webots_objects_msgs::srv::UpdateObjects::Request>();
    request->action.data = "DELETEALL";
    callService(request);
}

void SimulationTool::callSetObjects()
{
    auto request = std::make_shared<webots_objects_msgs::srv::UpdateObjects::Request>();
    request->action.data = "ADD";
    request->input_objects = objects_;
    callService(request);
}
