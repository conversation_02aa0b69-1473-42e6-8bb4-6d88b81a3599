#pragma once
#include <QThread>
#include <geometry_msgs/msg/point_stamped.hpp>
#include <rclcpp/time.hpp>

class WorkerThread : public QThread
{
    Q_OBJECT
public:
    void setGoalsAndIndex(std::vector<std::pair<std::string, geometry_msgs::msg::PointStamped>> &vec, int index)
    {
        goal_pose_vec_ = vec;
        loop_index_ = index;
    }

    void run() override;

signals:
    void sendCurStatus(int);
    void sendPlanningIndex(int);

public Q_SLOTS:
    void handlePlanningState(uint8_t);
    void handleStop();
    void handleOneLoopBox(int);

private:
    std::vector<std::pair<std::string, geometry_msgs::msg::PointStamped>> goal_pose_vec_;
    bool loop_start_ = false;
    int loop_index_ = -1;
    uint8_t planning_state_ = -1;
    int one_loop_checkbox_ = Qt::Unchecked;
};
