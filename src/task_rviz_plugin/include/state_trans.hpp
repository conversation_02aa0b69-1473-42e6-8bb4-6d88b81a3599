#pragma once
#include <state_msgs/msg/planning_state.hpp>
#include <state_msgs/msg/localization_state.hpp>
#include <state_msgs/msg/planning_state.hpp>
#include <state_msgs/msg/routing_state.hpp>
#include <chitu_msgs/msg/horn.hpp>

// 定位状态描述
const static std::unordered_map<uint8_t, std::string> LOCALIZATION_STATE_DESC_MAP = {
    {state_msgs::msg::LocalizationState::LOCATE_UNINIT, "LocateUninit"},
    {state_msgs::msg::LocalizationState::LOCATE_READY, "LocateReady"},
    {state_msgs::msg::LocalizationState::LOCATE_RUNNING, "LocateRunning"},
    {state_msgs::msg::LocalizationState::LOCATE_LOST, "LocateLost"},
    {state_msgs::msg::LocalizationState::LOCATE_RECOVERING, "LocateRecovering"},
    {state_msgs::msg::LocalizationState::RELOCATE_UNINIT, "RelocateUninit"},
    {state_msgs::msg::LocalizationState::RELOCATE_READY, "RelocateReady"},
    {state_msgs::msg::LocalizationState::RELOCATE_RUNNING, "RelocateRunning"},
    {state_msgs::msg::LocalizationState::RELOCATE_LOST, "RelocateLost"},
    {state_msgs::msg::LocalizationState::RELOCATE_SUCCESS, "RelocateSuccess"},
    {state_msgs::msg::LocalizationState::RELOCATE_ABORT, "RelocateAbort"},
    {state_msgs::msg::LocalizationState::RELOCATE_FAILURE, "RelocateFailure"},
    {state_msgs::msg::LocalizationState::MAP_NOT_INIT, "MapNotInit"},
    {state_msgs::msg::LocalizationState::MAP_INITED, "MapInited"},
    {state_msgs::msg::LocalizationState::ODOM_LOST, "OdomLost"},
    {state_msgs::msg::LocalizationState::IMU_LOST, "ImuLost"},
    {state_msgs::msg::LocalizationState::POINTCLOUD_LOST, "PointcloudLost"}};

// 规划状态描述
const static std::unordered_map<uint8_t, std::string> PLANNING_STATE_DESC_MAP = {
    {state_msgs::msg::PlanningState::PLANNING_UNINIT, "Uinit"},
    {state_msgs::msg::PlanningState::PLANNING_RUNNING_LANE, "RunningLane"},
    {state_msgs::msg::PlanningState::PLANNING_RUNNING_LANE_OBSTACLE_AVOID, "RunningLaneObstacleAvoid"},
    {state_msgs::msg::PlanningState::PLANNING_RUNNING_LANE_OBSTACLE_STOP, "RunningLaneObstacleStop"},
    {state_msgs::msg::PlanningState::PLANNING_RUNNING_LANE_SOLUTION_FAILED, "RunningLaneSolutionFailed"},
    {state_msgs::msg::PlanningState::PLANNING_RUNNING_FREE, "RunningFree"},
    {state_msgs::msg::PlanningState::PLANNING_FINISH, "Finish"},
    {state_msgs::msg::PlanningState::PLANNING_CANCEL, "Cancel"},
    {state_msgs::msg::PlanningState::PLANNING_ERROR, "Error"}};

// 路由状态描述
const static std::unordered_map<uint8_t, std::string> ROUTING_STATE_DESC_MAP = {
    {state_msgs::msg::RoutingState::ROUTING_IDLE, "Idle"},
    {state_msgs::msg::RoutingState::ROUTING_RUNNING, "Running"},
    {state_msgs::msg::RoutingState::ROUTING_FINISH, "Finish"},
    {state_msgs::msg::RoutingState::ROUTING_ERROR_NO_MAP, "No Map"},
    {state_msgs::msg::RoutingState::ROUTING_ERROR_NO_LOCALIZATION, "No Localization"},
    {state_msgs::msg::RoutingState::ROUTING_ERROR_START_LANELET_NOT_FOUND, "Start Lanelet Not Found"},
    {state_msgs::msg::RoutingState::ROUTING_ERROR_END_LANELET_NOT_FOUND, "End Lanelet Not Found"},
    {state_msgs::msg::RoutingState::ROUTING_ERROR_START_ANGLE_ERROR, "Start Angle Error"},
    {state_msgs::msg::RoutingState::ROUTING_ERROR_END_BEHIND_START_POSE, "End Behind Start Pose"},
    {state_msgs::msg::RoutingState::ROUTING_ERROR_ROUTE_NOT_FOUND, "No Route"},
    {state_msgs::msg::RoutingState::ROUTING_ERROR_UNKNOWN, "Unknown Error"}};

// 语音播报状态描述
const static std::unordered_map<uint8_t, std::string> HORN_STATE_DESC_MAP = {
    {chitu_msgs::msg::Horn::TURN_OFF_SOUND, "Turn Off Sound"},
    {chitu_msgs::msg::Horn::POWER_LOWER_THAN_TEN, "Power < 10%%"},
    {chitu_msgs::msg::Horn::POWER_LOWER_THAN_TWENTY, "Power < 20%%"},
    {chitu_msgs::msg::Horn::POWER_FULL_CHAEFED, "Power Full"},
    {chitu_msgs::msg::Horn::FRONT_OBSTACLE, "Front Obstacle"},
    {chitu_msgs::msg::Horn::LEFT_TURN, "Turn Left"},
    {chitu_msgs::msg::Horn::RIGHT_TURN, "Turn Right"},
    {chitu_msgs::msg::Horn::BACKWARD, "Backward"},
    {chitu_msgs::msg::Horn::EXECUSE_ME, "Execuse Me"},
    {chitu_msgs::msg::Horn::START_NAVIGATION, "Start Navigation"},
    {chitu_msgs::msg::Horn::CANCEL_NAVIGATION, "Cancel Navigation"},
    {chitu_msgs::msg::Horn::REACH_DESTINATION, "Reach Destination"},
    {chitu_msgs::msg::Horn::START_MAPPING, "Start Mapping"},
    {chitu_msgs::msg::Horn::CANCEL_MAPPING, "Cancel Mapping"},
    {chitu_msgs::msg::Horn::START_SAVE_MAPPING, "Start Save Mapping"},
    {chitu_msgs::msg::Horn::SAVE_MAPPING_SUCCEED, "Save Mapping Succeed"},
    {chitu_msgs::msg::Horn::START_RELOCALIZATION, "Start Relocalization"},
    {chitu_msgs::msg::Horn::RELOCALIZTION_SUCCEED, "Relocalization Succeed"},
    {chitu_msgs::msg::Horn::RELOCALIZTION_FAILED, "Relocalization Failed"},
    {chitu_msgs::msg::Horn::LOCALIZATION_LOST, "Localization Lost"},
    {chitu_msgs::msg::Horn::EMERGENCY_STOPPED, "Emergency Stopped"},
    {chitu_msgs::msg::Horn::NAVIGATION_RUNNING, "Navigation Running"},
    {chitu_msgs::msg::Horn::COLLISION_WARNING, "Collision Warnning"}};
