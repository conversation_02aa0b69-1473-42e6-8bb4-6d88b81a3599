#ifndef CONTROL_AND_DISPLAY_PANEL_H_
#define CONTROL_AND_DISPLAY_PANEL_H_

//所需要包含的头文件
#include <QCursor>  // NOLINT cpplint cannot handle the include order here
#include <QObject>  // NOLINT cpplint cannot handle the include order here
#include <QThread>

#include "rclcpp/rclcpp.hpp"
#include "rclcpp/service.hpp"
#include "rclcpp/qos.hpp"
#include "rviz_common/panel.hpp"
#include "std_msgs/msg/string.hpp"
#include "std_msgs/msg/bool.hpp"
#include "std_msgs/msg/float32.hpp"
#include "std_msgs/msg/int32.hpp"
#include "geometry_msgs/msg/twist.hpp"
#include "geometry_msgs/msg/pose_stamped.hpp"
#include "geometry_msgs/msg/point_stamped.hpp"
#include "geometry_msgs/msg/pose_with_covariance_stamped.hpp"
#include <ackermann_msgs/msg/ackermann_drive_status.hpp>
#include <ackermann_msgs/msg/ackermann_drive_odometry.hpp>
#include <chitu_msgs/msg/horn.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <custom_msgs/msg/hmi_config.hpp>
#include "custom_msgs/msg/traffic_guide.hpp"
#include "custom_msgs/msg/traffic_light.hpp"
#include "custom_msgs/srv/traffic_light_service.hpp"
#include "custom_msgs/msg/traffic_device_light.hpp"
#include "custom_msgs/msg/traffic_device.hpp"
#include "custom_msgs/srv/traffic_device_service.hpp"
#include "umodule_msgs/msg/module_state.hpp"
#include "task_rviz_plugin.h"

#include "rviz_common/display_context.hpp"
#include <json/json.h>
#include "loop_planning_thread.hpp"

class QLineEdit;

namespace task_rviz_plugin {
using TrafficDeviceLight = custom_msgs::msg::TrafficDeviceLight;
// 所有的plugin都必须是rviz::Panel的子类
class TaskRvizPanel : public rviz_common::Panel
{
    // 后边需要用到Qt的信号和槽，都是QObject的子类，所以需要声明Q_OBJECT宏
    Q_OBJECT
public:
    // 构造函数，在类中会用到QWidget的实例来实现GUI界面，这里先初始化为0即可
    TaskRvizPanel(QWidget *parent = 0);

    // 重载rviz::Panel积累中的函数，用于保存、加载配置文件中的数据，在我们这个plugin
    // 中，数据就是topic的名称
    virtual void load(const rviz_common::Config &config);
    virtual void save(rviz_common::Config config) const;

    void onInitialize() override;

    // 内部槽.
protected Q_SLOTS:
    // 在窗口大小变化时触发
    void resizeEvent(QResizeEvent *event);

    void clickStartMapping();
    void clickSaveMapping();
    void clickCancelMapping();

    void clickManualRelocate();
    void clickGpsRelocate();
    void clickCancelRelocate();

    void clickRoutingPlanning();
    void clickSelectTrafficLight(const std::string &light_color);
    void clickSelectTrafficDeviceLight(const std::string &rule, const std::string &color);
    void clickSelectTrafficRule(const std::string &rule);
    void clickSelectRainFall();
    void clickSelectTrafficDeviceRule(const u_int32_t &location, const QComboBox *comboBox_ptr);
    void clickSelectTrafficDeviceColor(const u_int32_t &location, const QComboBox *comboBox_ptr);
    void clickSetTrafficDeviceLightTime();
    void TimeLcdToggleVisibility(QLCDNumber *lcd_number);
    void clickSetTrafficDeviceResponse();
    void clickSwitchTrafficDevicePanelMode();

    void switchSubPlanner(const std::string &subplanner, const std::string &status);

    void clickFreeNavigation();
    void clickCancelNavigation();
    void clickSetPlanningSpeed();
    void clickEngageCheckBox();
    void clickSaveGoalBtn();
    void clickSetMapBtn();
    void clickGoalPoseTable(QTableWidgetItem *);
    void clickPlanningSelectedGoalBtn();
    void clickLoopPlanningGoalBtn();
    void clickLoopPlanningStopBtn();
    void clickDeleteSelectedGoalBtn();
    void clickGoalSpinBox2GoalPose();

    void clickSimSetFolder();
    void clickSimInitSimulation();
    void clickSimRun();
    void clickSimEditSelect();
    void clickSimDeleteSelect();
    void clickSimSave();

    void simTableSelect(QTableWidgetItem* item);

private:
    void goalPoseCallback(const geometry_msgs::msg::PoseStamped::SharedPtr msg);
    void initialPoseCallback(const geometry_msgs::msg::PoseWithCovarianceStamped::SharedPtr msg);
    void taskResponseCallback(const std_msgs::msg::String::SharedPtr msg);
    void hmiConfigCallback(const custom_msgs::msg::HmiConfig::SharedPtr msg);
    void planningSpeedCallback(const std_msgs::msg::String::ConstPtr &msg);
    void navProgressBarCallback(const std_msgs::msg::String::SharedPtr msg);
    void Handle_service_request(const std::shared_ptr<custom_msgs::srv::TrafficLightService_Request> &request,
                                const std::shared_ptr<custom_msgs::srv::TrafficLightService_Response> &response);

    void Handle_service_request(const std::shared_ptr<custom_msgs::srv::TrafficDeviceService_Request> &request,
                                const std::shared_ptr<custom_msgs::srv::TrafficDeviceService_Response> &response);

    void publishJson(Json::Value &value);
    void clearRequestResponseTextBrowser();
    void setGoalPoseSpinBox(const geometry_msgs::msg::PoseStamped::SharedPtr &msg);

    void odomCallback(const nav_msgs::msg::Odometry::SharedPtr msg);
    void batteryCallback(const ackermann_msgs::msg::AckermannDriveStatus::SharedPtr msg);
    void joyCallback(const ackermann_msgs::msg::AckermannDriveOdometry::SharedPtr msg);
    void hornCallback(const chitu_msgs::msg::Horn::SharedPtr msg);
    void SubplannerStatusCallback(const std_msgs::msg::String::SharedPtr msg);
    void SubTrafficDeviceResCallback(const std_msgs::msg::String::SharedPtr msg);
    void moduleStateCallback(const umodule_msgs::msg::ModuleState::SharedPtr msg);

    Ui::Form ui_;
    std::shared_ptr<rclcpp::Node> node_;

    // Publishers
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr pub_task_request_;
    rclcpp::Publisher<std_msgs::msg::Bool>::SharedPtr pub_engaged_;
    rclcpp::Publisher<std_msgs::msg::Float32>::SharedPtr pub_planning_max_speed_;
    rclcpp::Publisher<std_msgs::msg::Int32>::SharedPtr pub_rain_fall_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr pub_subplanner_switch_;

    // Subscribers
    rclcpp::Subscription<geometry_msgs::msg::PoseStamped>::SharedPtr sub_goal_pose_;
    rclcpp::Subscription<geometry_msgs::msg::PoseWithCovarianceStamped>::SharedPtr sub_initial_pose_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr sub_task_response_;
    rclcpp::Subscription<custom_msgs::msg::HmiConfig>::SharedPtr sub_hmi_config_;
    rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr sub_odom_;
    rclcpp::Subscription<ackermann_msgs::msg::AckermannDriveStatus>::SharedPtr sub_battrey_;
    rclcpp::Subscription<ackermann_msgs::msg::AckermannDriveOdometry>::SharedPtr sub_joy_;
    rclcpp::Subscription<chitu_msgs::msg::Horn>::SharedPtr sub_horn_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr sub_planning_max_speed_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr sub_planning_nav_progress_bar_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr sub_subplanner_switch_status_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr sub_traffic_device_status_res_;
    rclcpp::Subscription<umodule_msgs::msg::ModuleState>::SharedPtr sub_module_state_;

    // service
    rclcpp::Service<custom_msgs::srv::TrafficLightService>::SharedPtr service_traffic_guide_;
    rclcpp::Service<custom_msgs::srv::TrafficDeviceService>::SharedPtr service_traffic_device_;

    double last_joy_speed_time_ = 0.0;
    double last_horn_time_ = 0.0;
    double current_time_from_odom_ = 0.0;
    uint8_t last_localization_state_ = 0, last_planning_state_ = 0, last_routing_state_ = 0;

    Json::Value subplanner_switch_root_array_;

    std::unordered_map<u_int8_t, std::tuple<QwtTextLabel *, QLCDNumber *>> location_traffic_status_label_lcd_;

    bool traffic_respone_ = false;
    bool traffic_time_set_ = false;
    bool traffic_rule_set_ = false;
    bool traffic_color_set_ = false;
    bool traffic_device_response_bool_ = false;
    bool traffic_device_signal_sim_mode_ = true;
    QTimer *traffic_timer_;
    std::mutex traffic_mutex_;
    custom_msgs::msg::TrafficGuide traffic_guide_;
    custom_msgs::srv::TrafficDeviceService_Response traffic_device_response_;
    geometry_msgs::msg::PoseStamped goal_pose_;
    geometry_msgs::msg::PoseWithCovarianceStamped initial_pose_;
    bool is_goal_pose_received_, is_initial_pose_received_;
    int session_id_ = 1;
    std::string status_str[6] = {"UNKNOWN", "OK", "INFO", "WARN", "ERROR", "FATAL"};

    // for goals
    std::vector<std::pair<std::string, geometry_msgs::msg::PointStamped>> goal_pose_vec_;
    std::string goal_file_;
    int selected_goal_index_ = -1;
    void loadGoalFile();   // json file -> goal_pose_vec_
    void writeGoalFile();  // goal_pose_vec_ -> json file
    void updateGoalTable();
    void publishOneGoal(int index);

    // for loop planning
    bool loop_planning_start_ = false;
    int loop_index_ = -1;
    int loop_status_ = -1;
    WorkerThread *loop_planning_worker_;

private Q_SLOTS:
    void loopFunc();
    void handleWorkerIndex(int index);

signals:
    void sendPlanningStateToLoopPlanning(uint8_t);
    void sendStopToLoopPlanning();
};

}  // namespace task_rviz_plugin

#endif  // CONTROL_AND_DISPLAY_PANEL_H_
