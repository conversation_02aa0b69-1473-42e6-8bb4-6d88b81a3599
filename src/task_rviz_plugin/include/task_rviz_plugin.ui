<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>975</width>
    <height>836</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <weight>50</weight>
    <bold>false</bold>
   </font>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QWidget" name="gridLayoutWidget">
   <property name="geometry">
    <rect>
     <x>9</x>
     <y>9</y>
     <width>891</width>
     <height>771</height>
    </rect>
   </property>
   <layout class="QGridLayout" name="gridLayout" columnstretch="2,0">
    <property name="leftMargin">
     <number>10</number>
    </property>
    <property name="topMargin">
     <number>5</number>
    </property>
    <property name="rightMargin">
     <number>10</number>
    </property>
    <property name="bottomMargin">
     <number>5</number>
    </property>
    <property name="horizontalSpacing">
     <number>6</number>
    </property>
    <property name="verticalSpacing">
     <number>2</number>
    </property>
    <item row="3" column="1">
     <widget class="QLabel" name="label_planning">
      <property name="maximumSize">
       <size>
        <width>300</width>
        <height>20</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>14</pointsize>
        <weight>50</weight>
        <bold>false</bold>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: lightgray;</string>
      </property>
      <property name="text">
       <string>Planning</string>
      </property>
     </widget>
    </item>
    <item row="0" column="1">
     <widget class="QLabel" name="label_battery">
      <property name="maximumSize">
       <size>
        <width>********</width>
        <height>20</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>14</pointsize>
        <weight>50</weight>
        <bold>false</bold>
       </font>
      </property>
      <property name="text">
       <string>Battrey: 0%</string>
      </property>
     </widget>
    </item>
    <item row="2" column="1">
     <widget class="QLabel" name="label_routing">
      <property name="maximumSize">
       <size>
        <width>300</width>
        <height>20</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>14</pointsize>
        <weight>50</weight>
        <bold>false</bold>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: lightgray;</string>
      </property>
      <property name="text">
       <string>Routing</string>
      </property>
     </widget>
    </item>
    <item row="2" column="0">
     <widget class="QLabel" name="label_joy">
      <property name="maximumSize">
       <size>
        <width>********</width>
        <height>20</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>14</pointsize>
        <weight>50</weight>
        <bold>false</bold>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgb(115, 210, 22);</string>
      </property>
      <property name="text">
       <string>Joy Control</string>
      </property>
     </widget>
    </item>
    <item row="3" column="0">
     <widget class="QCheckBox" name="checkBox_engage">
      <property name="maximumSize">
       <size>
        <width>********</width>
        <height>50</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>16</pointsize>
        <weight>50</weight>
        <bold>false</bold>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QCheckBox::indicator 
{
width: 32px;
height: 32px;
	font: 16pt &quot;Ubuntu&quot;;
}</string>
      </property>
      <property name="text">
       <string>Engage Velocity</string>
      </property>
      <property name="iconSize">
       <size>
        <width>32</width>
        <height>32</height>
       </size>
      </property>
      <property name="tristate">
       <bool>false</bool>
      </property>
     </widget>
    </item>
    <item row="0" column="0">
     <widget class="QLabel" name="label_time">
      <property name="maximumSize">
       <size>
        <width>********</width>
        <height>20</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>14</pointsize>
        <weight>50</weight>
        <bold>false</bold>
       </font>
      </property>
      <property name="text">
       <string>Time: 0.0</string>
      </property>
     </widget>
    </item>
    <item row="1" column="1">
     <widget class="QLabel" name="label_localization">
      <property name="maximumSize">
       <size>
        <width>300</width>
        <height>20</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>14</pointsize>
        <weight>50</weight>
        <bold>false</bold>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: lightgray;</string>
      </property>
      <property name="text">
       <string>Localization</string>
      </property>
     </widget>
    </item>
    <item row="1" column="0">
     <widget class="QLabel" name="label_horn">
      <property name="maximumSize">
       <size>
        <width>********</width>
        <height>20</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>14</pointsize>
        <weight>50</weight>
        <bold>false</bold>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="text">
       <string>Msg:</string>
      </property>
     </widget>
    </item>
    <item row="4" column="0" rowspan="2" colspan="2">
     <widget class="QTabWidget" name="tabWidget">
      <property name="currentIndex">
       <number>6</number>
      </property>
      <widget class="QWidget" name="tab_map">
       <attribute name="title">
        <string>Mapping</string>
       </attribute>
       <widget class="QWidget" name="gridLayoutWidget_2">
        <property name="geometry">
         <rect>
          <x>30</x>
          <y>20</y>
          <width>381</width>
          <height>191</height>
         </rect>
        </property>
        <layout class="QGridLayout" name="gridLayout_map">
         <property name="leftMargin">
          <number>5</number>
         </property>
         <property name="topMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>5</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <item row="5" column="0">
          <widget class="QPushButton" name="start_mapping_btn">
           <property name="text">
            <string>Start Mapping</string>
           </property>
          </widget>
         </item>
         <item row="6" column="0">
          <widget class="QPushButton" name="cancel_mapping_btn">
           <property name="text">
            <string>Cancel Mapping</string>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QPushButton" name="save_mapping_btn">
           <property name="text">
            <string>Save Mapping</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLineEdit" name="map_folder_edit"/>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="label_3">
           <property name="text">
            <string>Map Folder Name</string>
           </property>
          </widget>
         </item>
         <item row="7" column="0">
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </widget>
      <widget class="QWidget" name="tab_locate">
       <attribute name="title">
        <string>Localization</string>
       </attribute>
       <widget class="QWidget" name="gridLayoutWidget_3">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>20</y>
          <width>351</width>
          <height>171</height>
         </rect>
        </property>
        <layout class="QGridLayout" name="gridLayout_locate">
         <property name="leftMargin">
          <number>5</number>
         </property>
         <property name="topMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>5</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <item row="0" column="1">
          <widget class="QLabel" name="label_5">
           <property name="text">
            <string>Initial Pose</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QPushButton" name="cancel_relocate_btn">
           <property name="text">
            <string>Cancel Relocate</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QPushButton" name="gps_relocate_btn">
           <property name="text">
            <string>GPS Relocate</string>
           </property>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QPushButton" name="manual_relocate_btn">
           <property name="text">
            <string>Manual Relocate</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1" rowspan="2">
          <widget class="QTextBrowser" name="locate_text_browser"/>
         </item>
         <item row="3" column="0">
          <spacer name="verticalSpacer_2">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </widget>
      <widget class="QWidget" name="tab_planning">
       <attribute name="title">
        <string>Planning</string>
       </attribute>
       <widget class="QWidget" name="gridLayoutWidget_4">
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>482</width>
          <height>521</height>
         </rect>
        </property>
        <layout class="QGridLayout" name="gridLayout_planning">
         <property name="leftMargin">
          <number>5</number>
         </property>
         <property name="topMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>5</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <property name="verticalSpacing">
          <number>2</number>
         </property>
         <item row="6" column="3">
          <widget class="QPushButton" name="turn_right_light">
           <property name="enabled">
            <bool>false</bool>
           </property>
           <property name="text">
            <string>Right Light</string>
           </property>
          </widget>
         </item>
         <item row="5" column="3">
          <widget class="QPushButton" name="turn_left_light">
           <property name="enabled">
            <bool>false</bool>
           </property>
           <property name="text">
            <string>Left Light</string>
           </property>
          </widget>
         </item>
         <item row="4" column="4" colspan="2">
          <widget class="QRadioButton" name="red_light_btn">
           <property name="text">
            <string>red</string>
           </property>
          </widget>
         </item>
         <item row="5" column="4" colspan="2">
          <widget class="QRadioButton" name="green_light_btn">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="text">
            <string>green</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item row="6" column="0">
          <widget class="QLabel" name="weather_label">
           <property name="text">
            <string>Weather</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="4" column="3">
          <widget class="QPushButton" name="straight_light">
           <property name="enabled">
            <bool>false</bool>
           </property>
           <property name="text">
            <string>Straight Light</string>
           </property>
          </widget>
         </item>
         <item row="0" column="0" colspan="6">
          <widget class="QProgressBar" name="navigation_progressBar">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="font">
            <font>
             <weight>75</weight>
             <italic>false</italic>
             <bold>true</bold>
             <underline>false</underline>
             <strikeout>false</strikeout>
            </font>
           </property>
           <property name="mouseTracking">
            <bool>false</bool>
           </property>
           <property name="styleSheet">
            <string notr="true">QProgressBar::chunk { background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(65, 94, 207, 100), stop:1 rgba(65, 94, 207, 255));}</string>
           </property>
           <property name="value">
            <number>90</number>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
           <property name="textVisible">
            <bool>true</bool>
           </property>
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="invertedAppearance">
            <bool>false</bool>
           </property>
           <property name="textDirection">
            <enum>QProgressBar::TopToBottom</enum>
           </property>
           <property name="format">
            <string>%p%</string>
           </property>
          </widget>
         </item>
         <item row="4" column="1">
          <widget class="QDoubleSpinBox" name="max_speed_doubleSpinBox">
           <property name="decimals">
            <number>1</number>
           </property>
           <property name="maximum">
            <double>10.000000000000000</double>
           </property>
           <property name="singleStep">
            <double>0.200000000000000</double>
           </property>
           <property name="value">
            <double>2.000000000000000</double>
           </property>
          </widget>
         </item>
         <item row="7" column="0" rowspan="5" colspan="6">
          <widget class="QTabWidget" name="tabWidget_subplanner">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>120</height>
            </size>
           </property>
           <property name="currentIndex">
            <number>2</number>
           </property>
           <widget class="QWidget" name="path_planner">
            <property name="enabled">
             <bool>true</bool>
            </property>
            <attribute name="title">
             <string>path_planner</string>
            </attribute>
            <widget class="QWidget" name="gridLayoutWidget_7">
             <property name="geometry">
              <rect>
               <x>0</x>
               <y>10</y>
               <width>461</width>
               <height>141</height>
              </rect>
             </property>
             <layout class="QGridLayout" name="gridLayout_path_subplanner" columnstretch="2,0,0">
              <item row="1" column="0">
               <widget class="QLabel" name="backup_planner_status_label">
                <property name="text">
                 <string>backup_generator</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item row="0" column="2">
               <widget class="QPushButton" name="off_trailer_planner_btn">
                <property name="text">
                 <string>OFF</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QPushButton" name="on_trailer_planner_btn">
                <property name="text">
                 <string>ON</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QPushButton" name="on_backup_planner_btn">
                <property name="text">
                 <string>ON</string>
                </property>
               </widget>
              </item>
              <item row="1" column="2">
               <widget class="QPushButton" name="off_backup_planner_btn">
                <property name="text">
                 <string>OFF</string>
                </property>
               </widget>
              </item>
              <item row="0" column="0">
               <widget class="QLabel" name="trailer_planner_status_label">
                <property name="text">
                 <string>trailer_planner</string>
                </property>
                <property name="textFormat">
                 <enum>Qt::AutoText</enum>
                </property>
                <property name="alignment">
                 <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item row="2" column="0" colspan="3">
               <spacer name="verticalSpacer_7">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </widget>
           <widget class="QWidget" name="velocity_planner">
            <attribute name="title">
             <string>velocity_planner</string>
            </attribute>
            <widget class="QWidget" name="gridLayoutWidget_8">
             <property name="geometry">
              <rect>
               <x>0</x>
               <y>0</y>
               <width>461</width>
               <height>171</height>
              </rect>
             </property>
             <layout class="QGridLayout" name="gridLayout_velocity_subplanner" columnstretch="2,0,0">
              <item row="3" column="2">
               <widget class="QPushButton" name="off_extra_strategy_manager_btn">
                <property name="text">
                 <string>OFF</string>
                </property>
               </widget>
              </item>
              <item row="0" column="2">
               <widget class="QPushButton" name="off_trajectory_checker_btn">
                <property name="text">
                 <string>OFF</string>
                </property>
               </widget>
              </item>
              <item row="2" column="0">
               <widget class="QLabel" name="speed_bump_manager_status_label">
                <property name="text">
                 <string>speed_bump_manager</string>
                </property>
               </widget>
              </item>
              <item row="2" column="1">
               <widget class="QPushButton" name="on_speed_bump_manager_btn">
                <property name="text">
                 <string>ON</string>
                </property>
               </widget>
              </item>
              <item row="0" column="0">
               <widget class="QLabel" name="trajectory_checker_status_label">
                <property name="text">
                 <string>trajectory_checker</string>
                </property>
                <property name="textFormat">
                 <enum>Qt::AutoText</enum>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QLabel" name="extra_strategy_manager_status_label">
                <property name="text">
                 <string>extra_strategy_manager</string>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="traffic_light_manager_status_label">
                <property name="text">
                 <string>traffic_light_manager</string>
                </property>
               </widget>
              </item>
              <item row="1" column="2">
               <widget class="QPushButton" name="off_traffic_light_manager_btn">
                <property name="text">
                 <string>OFF</string>
                </property>
               </widget>
              </item>
              <item row="2" column="2">
               <widget class="QPushButton" name="off_speed_bump_manager_btn">
                <property name="text">
                 <string>OFF</string>
                </property>
               </widget>
              </item>
              <item row="3" column="1">
               <widget class="QPushButton" name="on_extra_strategy_manager_btn">
                <property name="text">
                 <string>ON</string>
                </property>
               </widget>
              </item>
              <item row="5" column="0" colspan="3">
               <spacer name="verticalSpacer_6">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item row="0" column="1">
               <widget class="QPushButton" name="on_trajectory_checker_btn">
                <property name="text">
                 <string>ON</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QPushButton" name="on_traffic_light_manager_btn">
                <property name="text">
                 <string>ON</string>
                </property>
               </widget>
              </item>
              <item row="4" column="0">
               <widget class="QLabel" name="drivable_space_manager_status_label">
                <property name="text">
                 <string>drivable_space_manager</string>
                </property>
               </widget>
              </item>
              <item row="4" column="1">
               <widget class="QPushButton" name="on_drivable_space_manager_btn">
                <property name="text">
                 <string>ON</string>
                </property>
               </widget>
              </item>
              <item row="4" column="2">
               <widget class="QPushButton" name="off_drivable_space_manager_btn">
                <property name="text">
                 <string>OFF</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </widget>
           <widget class="QWidget" name="traffic_device">
            <property name="enabled">
             <bool>true</bool>
            </property>
            <property name="layoutDirection">
             <enum>Qt::LeftToRight</enum>
            </property>
            <property name="autoFillBackground">
             <bool>false</bool>
            </property>
            <attribute name="title">
             <string>traffic_device</string>
            </attribute>
            <widget class="QWidget" name="gridLayoutWidget_9">
             <property name="geometry">
              <rect>
               <x>-1</x>
               <y>9</y>
               <width>471</width>
               <height>161</height>
              </rect>
             </property>
             <layout class="QGridLayout" name="gridLayout_traffic_device" columnstretch="0,0,0,0,0,1">
              <item row="0" column="0">
               <widget class="QwtTextLabel" name="east_light_status_label">
                <property name="plainText">
                 <string>East</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QLCDNumber" name="east_light_time_lcd">
                <property name="maximumSize">
                 <size>
                  <width>70</width>
                  <height>********</height>
                 </size>
                </property>
               </widget>
              </item>
              <item row="1" column="3">
               <widget class="QComboBox" name="south_traffic_color_box">
                <property name="maximumSize">
                 <size>
                  <width>100</width>
                  <height>********</height>
                 </size>
                </property>
                <item>
                 <property name="text">
                  <string>Red</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Green</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Yellow</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Unknown</string>
                 </property>
                </item>
               </widget>
              </item>
              <item row="3" column="3">
               <widget class="QComboBox" name="north_traffic_color_box">
                <property name="maximumSize">
                 <size>
                  <width>100</width>
                  <height>********</height>
                 </size>
                </property>
                <item>
                 <property name="text">
                  <string>Red</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Green</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Yellow</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Unknown</string>
                 </property>
                </item>
               </widget>
              </item>
              <item row="3" column="2">
               <widget class="QComboBox" name="north_traffic_rule_box">
                <item>
                 <property name="text">
                  <string>Straight</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Left</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Right</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Unknown</string>
                 </property>
                </item>
               </widget>
              </item>
              <item row="0" column="3">
               <widget class="QComboBox" name="east_traffic_color_box">
                <property name="maximumSize">
                 <size>
                  <width>100</width>
                  <height>********</height>
                 </size>
                </property>
                <item>
                 <property name="text">
                  <string>Red</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Green</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Yellow</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Unknown</string>
                 </property>
                </item>
               </widget>
              </item>
              <item row="2" column="2">
               <widget class="QComboBox" name="west_traffic_rule_box">
                <item>
                 <property name="text">
                  <string>Straight</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Left</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Right</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Unknown</string>
                 </property>
                </item>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QLCDNumber" name="south_light_time_lcd">
                <property name="maximumSize">
                 <size>
                  <width>70</width>
                  <height>********</height>
                 </size>
                </property>
               </widget>
              </item>
              <item row="1" column="2">
               <widget class="QComboBox" name="south_traffic_rule_box">
                <item>
                 <property name="text">
                  <string>Straight</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Left</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Right</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Unknown</string>
                 </property>
                </item>
               </widget>
              </item>
              <item row="2" column="1">
               <widget class="QLCDNumber" name="west_light_time_lcd">
                <property name="maximumSize">
                 <size>
                  <width>70</width>
                  <height>********</height>
                 </size>
                </property>
               </widget>
              </item>
              <item row="2" column="0">
               <widget class="QwtTextLabel" name="west_light_status_label">
                <property name="plainText">
                 <string>West</string>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QwtTextLabel" name="north_light_status_label">
                <property name="plainText">
                 <string>North</string>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QwtTextLabel" name="south_light_status_label">
                <property name="plainText">
                 <string>South</string>
                </property>
               </widget>
              </item>
              <item row="0" column="2">
               <widget class="QComboBox" name="east_traffic_rule_box">
                <item>
                 <property name="text">
                  <string>Straight</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Left</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Right</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Unknown</string>
                 </property>
                </item>
               </widget>
              </item>
              <item row="2" column="3">
               <widget class="QComboBox" name="west_traffic_color_box">
                <property name="maximumSize">
                 <size>
                  <width>100</width>
                  <height>********</height>
                 </size>
                </property>
                <item>
                 <property name="text">
                  <string>Red</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Green</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Yellow</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Unknown</string>
                 </property>
                </item>
               </widget>
              </item>
              <item row="3" column="1">
               <widget class="QLCDNumber" name="north_light_time_lcd">
                <property name="maximumSize">
                 <size>
                  <width>70</width>
                  <height>********</height>
                 </size>
                </property>
               </widget>
              </item>
              <item row="0" column="4">
               <widget class="QSpinBox" name="east_light_time_spinBox">
                <property name="maximum">
                 <number>200</number>
                </property>
               </widget>
              </item>
              <item row="1" column="4">
               <widget class="QSpinBox" name="south_light_time_spinBox">
                <property name="maximum">
                 <number>200</number>
                </property>
               </widget>
              </item>
              <item row="2" column="4">
               <widget class="QSpinBox" name="west_light_time_spinBox">
                <property name="maximum">
                 <number>200</number>
                </property>
               </widget>
              </item>
              <item row="3" column="4">
               <widget class="QSpinBox" name="north_light_time_spinBox">
                <property name="maximum">
                 <number>200</number>
                </property>
               </widget>
              </item>
              <item row="0" column="5">
               <widget class="QPushButton" name="traffic_device_response_btn">
                <property name="layoutDirection">
                 <enum>Qt::LeftToRight</enum>
                </property>
                <property name="text">
                 <string>Response</string>
                </property>
               </widget>
              </item>
              <item row="4" column="0" colspan="6">
               <spacer name="verticalSpacer_3">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item row="1" column="5">
               <widget class="QPushButton" name="set_traffic_time_btn">
                <property name="text">
                 <string>Set Time</string>
                </property>
               </widget>
              </item>
              <item row="3" column="5">
               <widget class="QPushButton" name="deivce_sim_status_show_switch_btn">
                <property name="text">
                 <string>Signal Sim</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </widget>
          </widget>
         </item>
         <item row="4" column="0">
          <widget class="QLabel" name="max_speed_label_2">
           <property name="text">
            <string>Max</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="1" column="3" colspan="3">
          <widget class="QLabel" name="label_7">
           <property name="text">
            <string>Goal Pose</string>
           </property>
           <property name="textFormat">
            <enum>Qt::RichText</enum>
           </property>
          </widget>
         </item>
         <item row="6" column="1" colspan="2">
          <widget class="QComboBox" name="rain_fall_box">
           <item>
            <property name="text">
             <string>Sunny</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Light_Rain</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Moderate_Rain</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Heavy_Rain</string>
            </property>
           </item>
          </widget>
         </item>
         <item row="1" column="0" colspan="3">
          <widget class="QPushButton" name="routing_planning_btn">
           <property name="text">
            <string>Routing Planning</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0" colspan="3">
          <widget class="QPushButton" name="free_navigation_btn">
           <property name="text">
            <string>Free Navigation</string>
           </property>
          </widget>
         </item>
         <item row="6" column="4" colspan="2">
          <widget class="QRadioButton" name="yellow_light_btn">
           <property name="text">
            <string>yellow</string>
           </property>
          </widget>
         </item>
         <item row="3" column="0" colspan="3">
          <widget class="QPushButton" name="cancel_navigation_btn">
           <property name="text">
            <string>Cancel Navigation</string>
           </property>
          </widget>
         </item>
         <item row="4" column="2">
          <widget class="QLabel" name="max_speed_label">
           <property name="text">
            <string>m/s</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="3" column="3">
          <widget class="QDoubleSpinBox" name="goal_pose_x_spin_box">
           <property name="maximum">
            <double>100.989999999999995</double>
           </property>
          </widget>
         </item>
         <item row="3" column="4">
          <widget class="QDoubleSpinBox" name="goal_pose_y_spin_box"/>
         </item>
         <item row="3" column="5">
          <widget class="QDoubleSpinBox" name="goal_pose_theta_spin_box"/>
         </item>
         <item row="2" column="3">
          <widget class="QLabel" name="goal_posex_label">
           <property name="text">
            <string>x</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="2" column="4">
          <widget class="QLabel" name="goal_pose_y_label">
           <property name="text">
            <string>y</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="2" column="5">
          <widget class="QLabel" name="goal_pose_theta_label">
           <property name="text">
            <string>theta</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </widget>
      <widget class="QWidget" name="tab_goal">
       <attribute name="title">
        <string>Goals</string>
       </attribute>
       <widget class="QWidget" name="gridLayoutWidget_5">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>20</y>
          <width>481</width>
          <height>461</height>
         </rect>
        </property>
        <layout class="QGridLayout" name="gridLayout_goal">
         <property name="leftMargin">
          <number>5</number>
         </property>
         <property name="topMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>5</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <item row="3" column="0" colspan="2">
          <widget class="QPushButton" name="planning_selected_goal_btn">
           <property name="text">
            <string>Routing Planning Selected Goal</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLineEdit" name="goal_name_edit">
           <property name="maximumSize">
            <size>
             <width>100</width>
             <height>********</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="label_8">
           <property name="maximumSize">
            <size>
             <width>********</width>
             <height>20</height>
            </size>
           </property>
           <property name="text">
            <string>Goal Pose</string>
           </property>
          </widget>
         </item>
         <item row="9" column="0">
          <spacer name="verticalSpacer_4">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
         <item row="1" column="0" rowspan="2">
          <widget class="QTextBrowser" name="goal_text_browser">
           <property name="maximumSize">
            <size>
             <width>100</width>
             <height>100</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="4" column="0" colspan="2">
          <widget class="QPushButton" name="delete_selected_goal_btn">
           <property name="text">
            <string>Delete Selected Goal</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QPushButton" name="save_goal_btn">
           <property name="maximumSize">
            <size>
             <width>100</width>
             <height>********</height>
            </size>
           </property>
           <property name="text">
            <string>Save Goal</string>
           </property>
          </widget>
         </item>
         <item row="8" column="0" colspan="2">
          <widget class="QLabel" name="label_loop_planning">
           <property name="maximumSize">
            <size>
             <width>10000</width>
             <height>200</height>
            </size>
           </property>
           <property name="text">
            <string>Status:</string>
           </property>
          </widget>
         </item>
         <item row="0" column="2" rowspan="10">
          <widget class="QTableWidget" name="goal_pose_table">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="dragEnabled">
            <bool>true</bool>
           </property>
           <property name="selectionMode">
            <enum>QAbstractItemView::ExtendedSelection</enum>
           </property>
           <attribute name="horizontalHeaderCascadingSectionResizes">
            <bool>true</bool>
           </attribute>
           <attribute name="horizontalHeaderDefaultSectionSize">
            <number>100</number>
           </attribute>
           <column>
            <property name="text">
             <string>Name</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>Pose</string>
            </property>
           </column>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLabel" name="label_9">
           <property name="maximumSize">
            <size>
             <width>100</width>
             <height>20</height>
            </size>
           </property>
           <property name="text">
            <string>Goal Name</string>
           </property>
          </widget>
         </item>
         <item row="5" column="0" colspan="2">
          <widget class="QPushButton" name="loop_planning_goal_btn">
           <property name="text">
            <string>Loop Planning From Selected Goal</string>
           </property>
          </widget>
         </item>
         <item row="6" column="0" colspan="2">
          <widget class="QPushButton" name="loop_planning_stop_btn">
           <property name="text">
            <string>Stop Loop Planning</string>
           </property>
          </widget>
         </item>
         <item row="7" column="0">
          <widget class="QCheckBox" name="one_loop_checkBox">
           <property name="text">
            <string>Run One Loop</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </widget>
      <widget class="QWidget" name="tab_map_node">
       <attribute name="title">
        <string>Maps &amp;&amp; Modes</string>
       </attribute>
       <widget class="QWidget" name="gridLayoutWidget_6">
        <property name="geometry">
         <rect>
          <x>60</x>
          <y>20</y>
          <width>391</width>
          <height>211</height>
         </rect>
        </property>
        <layout class="QGridLayout" name="gridLayout_map_mode">
         <property name="leftMargin">
          <number>5</number>
         </property>
         <property name="topMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>5</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <item row="1" column="1">
          <widget class="QPushButton" name="set_mode_btn">
           <property name="text">
            <string>set current mode</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QComboBox" name="mode_comboBox"/>
         </item>
         <item row="0" column="1">
          <widget class="QPushButton" name="set_map_btn">
           <property name="text">
            <string>set current map</string>
           </property>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QComboBox" name="map_comboBox"/>
         </item>
         <item row="2" column="0">
          <spacer name="verticalSpacer_5">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </widget>
      <widget class="QWidget" name="tab_log">
       <attribute name="title">
        <string>Log</string>
       </attribute>
       <widget class="QWidget" name="layoutWidget">
        <property name="geometry">
         <rect>
          <x>40</x>
          <y>20</y>
          <width>520</width>
          <height>438</height>
         </rect>
        </property>
        <layout class="QGridLayout" name="gridLayout_log">
         <item row="1" column="1" rowspan="3">
          <widget class="QTextBrowser" name="debug_info_text_browser"/>
         </item>
         <item row="1" column="0">
          <widget class="QTextBrowser" name="task_request_text_browser"/>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="label">
           <property name="text">
            <string>Request</string>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QTextBrowser" name="task_response_text_browser"/>
         </item>
         <item row="0" column="1">
          <widget class="QLabel" name="label_4">
           <property name="text">
            <string>Debug Info</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>Response</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </widget>
      <widget class="QWidget" name="tab_simulation">
       <attribute name="title">
        <string>Sim</string>
       </attribute>
       <widget class="QWidget" name="gridLayoutWidget_10">
        <property name="geometry">
         <rect>
          <x>80</x>
          <y>40</y>
          <width>647</width>
          <height>559</height>
         </rect>
        </property>
        <layout class="QGridLayout" name="gridLayout_sim">
         <property name="leftMargin">
          <number>5</number>
         </property>
         <property name="topMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>5</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <item row="5" column="0" colspan="4">
          <widget class="QLabel" name="sim_label_empty">
           <property name="text">
            <string>-----------------------------------------------------------------</string>
           </property>
          </widget>
         </item>
         <item row="4" column="2">
          <widget class="QPushButton" name="sim_edit_btn">
           <property name="text">
            <string>Edit Select</string>
           </property>
          </widget>
         </item>
         <item row="4" column="1">
          <widget class="QPushButton" name="sim_run_btn">
           <property name="text">
            <string>Run</string>
           </property>
          </widget>
         </item>
         <item row="6" column="0">
          <widget class="QLabel" name="label_6">
           <property name="text">
            <string>Name</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0" rowspan="2" colspan="4">
          <widget class="QTableWidget" name="sim_config_table">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="dragEnabled">
            <bool>true</bool>
           </property>
           <property name="selectionMode">
            <enum>QAbstractItemView::ExtendedSelection</enum>
           </property>
           <attribute name="horizontalHeaderCascadingSectionResizes">
            <bool>true</bool>
           </attribute>
           <attribute name="horizontalHeaderDefaultSectionSize">
            <number>100</number>
           </attribute>
           <column>
            <property name="text">
             <string>config</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>description</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>map_name</string>
            </property>
           </column>
          </widget>
         </item>
         <item row="4" column="3">
          <widget class="QPushButton" name="sim_delete_btn">
           <property name="text">
            <string>Delete Select</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0" colspan="3">
          <widget class="QTextEdit" name="sim_folder_edit">
           <property name="maximumSize">
            <size>
             <width>********</width>
             <height>30</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="6" column="3">
          <widget class="QPushButton" name="sim_save_btn">
           <property name="text">
            <string>Save</string>
           </property>
          </widget>
         </item>
         <item row="4" column="0">
          <widget class="QPushButton" name="sim_init_btn">
           <property name="text">
            <string>Init Simulation</string>
           </property>
          </widget>
         </item>
         <item row="1" column="3">
          <widget class="QPushButton" name="sim_set_folder_btn">
           <property name="text">
            <string>Set Scene Folder</string>
           </property>
          </widget>
         </item>
         <item row="7" column="0">
          <spacer name="verticalSpacer_8">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
         <item row="6" column="1" colspan="2">
          <widget class="QTextEdit" name="sim_folder_edit_2">
           <property name="maximumSize">
            <size>
             <width>********</width>
             <height>30</height>
            </size>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QwtTextLabel</class>
   <extends>QFrame</extends>
   <header>qwt_text_label.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
