/********************************************************************************
** Form generated from reading UI file 'task_rviz_plugin.ui'
**
** Created by: Qt User Interface Compiler version 5.15.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef TASK_RVIZ_PLUGIN_H
#define TASK_RVIZ_PLUGIN_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLCDNumber>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QProgressBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QRadioButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QTextBrowser>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QWidget>
#include "qwt_text_label.h"

QT_BEGIN_NAMESPACE

class Ui_Form
{
public:
    QWidget *gridLayoutWidget;
    QGridLayout *gridLayout;
    QLabel *label_planning;
    QLabel *label_battery;
    QLabel *label_routing;
    QLabel *label_joy;
    QCheckBox *checkBox_engage;
    QLabel *label_time;
    QLabel *label_localization;
    QLabel *label_horn;
    QTabWidget *tabWidget;
    QWidget *tab_map;
    QWidget *gridLayoutWidget_2;
    QGridLayout *gridLayout_map;
    QPushButton *start_mapping_btn;
    QPushButton *cancel_mapping_btn;
    QPushButton *save_mapping_btn;
    QLineEdit *map_folder_edit;
    QLabel *label_3;
    QSpacerItem *verticalSpacer;
    QWidget *tab_locate;
    QWidget *gridLayoutWidget_3;
    QGridLayout *gridLayout_locate;
    QLabel *label_5;
    QPushButton *cancel_relocate_btn;
    QPushButton *gps_relocate_btn;
    QPushButton *manual_relocate_btn;
    QTextBrowser *locate_text_browser;
    QSpacerItem *verticalSpacer_2;
    QWidget *tab_planning;
    QWidget *gridLayoutWidget_4;
    QGridLayout *gridLayout_planning;
    QPushButton *turn_right_light;
    QPushButton *turn_left_light;
    QRadioButton *red_light_btn;
    QRadioButton *green_light_btn;
    QLabel *weather_label;
    QPushButton *straight_light;
    QProgressBar *navigation_progressBar;
    QDoubleSpinBox *max_speed_doubleSpinBox;
    QTabWidget *tabWidget_subplanner;
    QWidget *path_planner;
    QWidget *gridLayoutWidget_7;
    QGridLayout *gridLayout_path_subplanner;
    QLabel *backup_planner_status_label;
    QPushButton *off_trailer_planner_btn;
    QPushButton *on_trailer_planner_btn;
    QPushButton *on_backup_planner_btn;
    QPushButton *off_backup_planner_btn;
    QLabel *trailer_planner_status_label;
    QSpacerItem *verticalSpacer_7;
    QWidget *velocity_planner;
    QWidget *gridLayoutWidget_8;
    QGridLayout *gridLayout_velocity_subplanner;
    QPushButton *off_extra_strategy_manager_btn;
    QPushButton *off_trajectory_checker_btn;
    QLabel *speed_bump_manager_status_label;
    QPushButton *on_speed_bump_manager_btn;
    QLabel *trajectory_checker_status_label;
    QLabel *extra_strategy_manager_status_label;
    QLabel *traffic_light_manager_status_label;
    QPushButton *off_traffic_light_manager_btn;
    QPushButton *off_speed_bump_manager_btn;
    QPushButton *on_extra_strategy_manager_btn;
    QSpacerItem *verticalSpacer_6;
    QPushButton *on_trajectory_checker_btn;
    QPushButton *on_traffic_light_manager_btn;
    QLabel *drivable_space_manager_status_label;
    QPushButton *on_drivable_space_manager_btn;
    QPushButton *off_drivable_space_manager_btn;
    QWidget *traffic_device;
    QWidget *gridLayoutWidget_9;
    QGridLayout *gridLayout_traffic_device;
    QwtTextLabel *east_light_status_label;
    QLCDNumber *east_light_time_lcd;
    QComboBox *south_traffic_color_box;
    QComboBox *north_traffic_color_box;
    QComboBox *north_traffic_rule_box;
    QComboBox *east_traffic_color_box;
    QComboBox *west_traffic_rule_box;
    QLCDNumber *south_light_time_lcd;
    QComboBox *south_traffic_rule_box;
    QLCDNumber *west_light_time_lcd;
    QwtTextLabel *west_light_status_label;
    QwtTextLabel *north_light_status_label;
    QwtTextLabel *south_light_status_label;
    QComboBox *east_traffic_rule_box;
    QComboBox *west_traffic_color_box;
    QLCDNumber *north_light_time_lcd;
    QSpinBox *east_light_time_spinBox;
    QSpinBox *south_light_time_spinBox;
    QSpinBox *west_light_time_spinBox;
    QSpinBox *north_light_time_spinBox;
    QPushButton *traffic_device_response_btn;
    QSpacerItem *verticalSpacer_3;
    QPushButton *set_traffic_time_btn;
    QPushButton *deivce_sim_status_show_switch_btn;
    QLabel *max_speed_label_2;
    QLabel *label_7;
    QComboBox *rain_fall_box;
    QPushButton *routing_planning_btn;
    QPushButton *free_navigation_btn;
    QRadioButton *yellow_light_btn;
    QPushButton *cancel_navigation_btn;
    QLabel *max_speed_label;
    QDoubleSpinBox *goal_pose_x_spin_box;
    QDoubleSpinBox *goal_pose_y_spin_box;
    QDoubleSpinBox *goal_pose_theta_spin_box;
    QLabel *goal_posex_label;
    QLabel *goal_pose_y_label;
    QLabel *goal_pose_theta_label;
    QWidget *tab_goal;
    QWidget *gridLayoutWidget_5;
    QGridLayout *gridLayout_goal;
    QPushButton *planning_selected_goal_btn;
    QLineEdit *goal_name_edit;
    QLabel *label_8;
    QSpacerItem *verticalSpacer_4;
    QTextBrowser *goal_text_browser;
    QPushButton *delete_selected_goal_btn;
    QPushButton *save_goal_btn;
    QLabel *label_loop_planning;
    QTableWidget *goal_pose_table;
    QLabel *label_9;
    QPushButton *loop_planning_goal_btn;
    QPushButton *loop_planning_stop_btn;
    QCheckBox *one_loop_checkBox;
    QWidget *tab_map_node;
    QWidget *gridLayoutWidget_6;
    QGridLayout *gridLayout_map_mode;
    QPushButton *set_mode_btn;
    QComboBox *mode_comboBox;
    QPushButton *set_map_btn;
    QComboBox *map_comboBox;
    QSpacerItem *verticalSpacer_5;
    QWidget *tab_log;
    QWidget *layoutWidget;
    QGridLayout *gridLayout_log;
    QTextBrowser *debug_info_text_browser;
    QTextBrowser *task_request_text_browser;
    QLabel *label;
    QTextBrowser *task_response_text_browser;
    QLabel *label_4;
    QLabel *label_2;
    QWidget *tab_simulation;
    QWidget *gridLayoutWidget_10;
    QGridLayout *gridLayout_sim;
    QLabel *sim_label_empty;
    QPushButton *sim_edit_btn;
    QPushButton *sim_run_btn;
    QLabel *label_6;
    QTableWidget *sim_config_table;
    QPushButton *sim_delete_btn;
    QTextEdit *sim_folder_edit;
    QPushButton *sim_save_btn;
    QPushButton *sim_init_btn;
    QPushButton *sim_set_folder_btn;
    QSpacerItem *verticalSpacer_8;
    QTextEdit *sim_folder_edit_2;

    void setupUi(QWidget *Form)
    {
        if (Form->objectName().isEmpty()) Form->setObjectName(QString::fromUtf8("Form"));
        Form->resize(975, 836);
        QFont font;
        font.setBold(false);
        font.setWeight(50);
        Form->setFont(font);
        gridLayoutWidget = new QWidget(Form);
        gridLayoutWidget->setObjectName(QString::fromUtf8("gridLayoutWidget"));
        gridLayoutWidget->setGeometry(QRect(9, 9, 891, 771));
        gridLayout = new QGridLayout(gridLayoutWidget);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        gridLayout->setHorizontalSpacing(6);
        gridLayout->setVerticalSpacing(2);
        gridLayout->setContentsMargins(10, 5, 10, 5);
        label_planning = new QLabel(gridLayoutWidget);
        label_planning->setObjectName(QString::fromUtf8("label_planning"));
        label_planning->setMaximumSize(QSize(300, 20));
        QFont font1;
        font1.setPointSize(14);
        font1.setBold(false);
        font1.setWeight(50);
        label_planning->setFont(font1);
        label_planning->setStyleSheet(QString::fromUtf8("background-color: lightgray;"));

        gridLayout->addWidget(label_planning, 3, 1, 1, 1);

        label_battery = new QLabel(gridLayoutWidget);
        label_battery->setObjectName(QString::fromUtf8("label_battery"));
        label_battery->setMaximumSize(QSize(********, 20));
        label_battery->setFont(font1);

        gridLayout->addWidget(label_battery, 0, 1, 1, 1);

        label_routing = new QLabel(gridLayoutWidget);
        label_routing->setObjectName(QString::fromUtf8("label_routing"));
        label_routing->setMaximumSize(QSize(300, 20));
        label_routing->setFont(font1);
        label_routing->setStyleSheet(QString::fromUtf8("background-color: lightgray;"));

        gridLayout->addWidget(label_routing, 2, 1, 1, 1);

        label_joy = new QLabel(gridLayoutWidget);
        label_joy->setObjectName(QString::fromUtf8("label_joy"));
        label_joy->setMaximumSize(QSize(********, 20));
        label_joy->setFont(font1);
        label_joy->setStyleSheet(QString::fromUtf8("background-color: rgb(115, 210, 22);"));

        gridLayout->addWidget(label_joy, 2, 0, 1, 1);

        checkBox_engage = new QCheckBox(gridLayoutWidget);
        checkBox_engage->setObjectName(QString::fromUtf8("checkBox_engage"));
        checkBox_engage->setMaximumSize(QSize(********, 50));
        QFont font2;
        font2.setPointSize(16);
        font2.setBold(false);
        font2.setWeight(50);
        checkBox_engage->setFont(font2);
        checkBox_engage->setStyleSheet(
            QString::fromUtf8("QCheckBox::indicator \n"
                              "{\n"
                              "width: 32px;\n"
                              "height: 32px;\n"
                              "	font: 16pt \"Ubuntu\";\n"
                              "}"));
        checkBox_engage->setIconSize(QSize(32, 32));
        checkBox_engage->setTristate(false);

        gridLayout->addWidget(checkBox_engage, 3, 0, 1, 1);

        label_time = new QLabel(gridLayoutWidget);
        label_time->setObjectName(QString::fromUtf8("label_time"));
        label_time->setMaximumSize(QSize(********, 20));
        label_time->setFont(font1);

        gridLayout->addWidget(label_time, 0, 0, 1, 1);

        label_localization = new QLabel(gridLayoutWidget);
        label_localization->setObjectName(QString::fromUtf8("label_localization"));
        label_localization->setMaximumSize(QSize(300, 20));
        label_localization->setFont(font1);
        label_localization->setStyleSheet(QString::fromUtf8("background-color: lightgray;"));

        gridLayout->addWidget(label_localization, 1, 1, 1, 1);

        label_horn = new QLabel(gridLayoutWidget);
        label_horn->setObjectName(QString::fromUtf8("label_horn"));
        label_horn->setMaximumSize(QSize(********, 20));
        label_horn->setFont(font1);
        label_horn->setStyleSheet(QString::fromUtf8(""));

        gridLayout->addWidget(label_horn, 1, 0, 1, 1);

        tabWidget = new QTabWidget(gridLayoutWidget);
        tabWidget->setObjectName(QString::fromUtf8("tabWidget"));
        tab_map = new QWidget();
        tab_map->setObjectName(QString::fromUtf8("tab_map"));
        gridLayoutWidget_2 = new QWidget(tab_map);
        gridLayoutWidget_2->setObjectName(QString::fromUtf8("gridLayoutWidget_2"));
        gridLayoutWidget_2->setGeometry(QRect(30, 20, 381, 191));
        gridLayout_map = new QGridLayout(gridLayoutWidget_2);
        gridLayout_map->setObjectName(QString::fromUtf8("gridLayout_map"));
        gridLayout_map->setContentsMargins(5, 5, 5, 5);
        start_mapping_btn = new QPushButton(gridLayoutWidget_2);
        start_mapping_btn->setObjectName(QString::fromUtf8("start_mapping_btn"));

        gridLayout_map->addWidget(start_mapping_btn, 5, 0, 1, 1);

        cancel_mapping_btn = new QPushButton(gridLayoutWidget_2);
        cancel_mapping_btn->setObjectName(QString::fromUtf8("cancel_mapping_btn"));

        gridLayout_map->addWidget(cancel_mapping_btn, 6, 0, 1, 1);

        save_mapping_btn = new QPushButton(gridLayoutWidget_2);
        save_mapping_btn->setObjectName(QString::fromUtf8("save_mapping_btn"));

        gridLayout_map->addWidget(save_mapping_btn, 3, 0, 1, 1);

        map_folder_edit = new QLineEdit(gridLayoutWidget_2);
        map_folder_edit->setObjectName(QString::fromUtf8("map_folder_edit"));

        gridLayout_map->addWidget(map_folder_edit, 2, 0, 1, 1);

        label_3 = new QLabel(gridLayoutWidget_2);
        label_3->setObjectName(QString::fromUtf8("label_3"));

        gridLayout_map->addWidget(label_3, 0, 0, 1, 1);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_map->addItem(verticalSpacer, 7, 0, 1, 1);

        tabWidget->addTab(tab_map, QString());
        tab_locate = new QWidget();
        tab_locate->setObjectName(QString::fromUtf8("tab_locate"));
        gridLayoutWidget_3 = new QWidget(tab_locate);
        gridLayoutWidget_3->setObjectName(QString::fromUtf8("gridLayoutWidget_3"));
        gridLayoutWidget_3->setGeometry(QRect(20, 20, 351, 171));
        gridLayout_locate = new QGridLayout(gridLayoutWidget_3);
        gridLayout_locate->setObjectName(QString::fromUtf8("gridLayout_locate"));
        gridLayout_locate->setContentsMargins(5, 5, 5, 5);
        label_5 = new QLabel(gridLayoutWidget_3);
        label_5->setObjectName(QString::fromUtf8("label_5"));

        gridLayout_locate->addWidget(label_5, 0, 1, 1, 1);

        cancel_relocate_btn = new QPushButton(gridLayoutWidget_3);
        cancel_relocate_btn->setObjectName(QString::fromUtf8("cancel_relocate_btn"));

        gridLayout_locate->addWidget(cancel_relocate_btn, 2, 0, 1, 1);

        gps_relocate_btn = new QPushButton(gridLayoutWidget_3);
        gps_relocate_btn->setObjectName(QString::fromUtf8("gps_relocate_btn"));

        gridLayout_locate->addWidget(gps_relocate_btn, 1, 0, 1, 1);

        manual_relocate_btn = new QPushButton(gridLayoutWidget_3);
        manual_relocate_btn->setObjectName(QString::fromUtf8("manual_relocate_btn"));

        gridLayout_locate->addWidget(manual_relocate_btn, 0, 0, 1, 1);

        locate_text_browser = new QTextBrowser(gridLayoutWidget_3);
        locate_text_browser->setObjectName(QString::fromUtf8("locate_text_browser"));

        gridLayout_locate->addWidget(locate_text_browser, 1, 1, 2, 1);

        verticalSpacer_2 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_locate->addItem(verticalSpacer_2, 3, 0, 1, 1);

        tabWidget->addTab(tab_locate, QString());
        tab_planning = new QWidget();
        tab_planning->setObjectName(QString::fromUtf8("tab_planning"));
        gridLayoutWidget_4 = new QWidget(tab_planning);
        gridLayoutWidget_4->setObjectName(QString::fromUtf8("gridLayoutWidget_4"));
        gridLayoutWidget_4->setGeometry(QRect(0, 0, 482, 521));
        gridLayout_planning = new QGridLayout(gridLayoutWidget_4);
        gridLayout_planning->setObjectName(QString::fromUtf8("gridLayout_planning"));
        gridLayout_planning->setVerticalSpacing(2);
        gridLayout_planning->setContentsMargins(5, 5, 5, 5);
        turn_right_light = new QPushButton(gridLayoutWidget_4);
        turn_right_light->setObjectName(QString::fromUtf8("turn_right_light"));
        turn_right_light->setEnabled(false);

        gridLayout_planning->addWidget(turn_right_light, 6, 3, 1, 1);

        turn_left_light = new QPushButton(gridLayoutWidget_4);
        turn_left_light->setObjectName(QString::fromUtf8("turn_left_light"));
        turn_left_light->setEnabled(false);

        gridLayout_planning->addWidget(turn_left_light, 5, 3, 1, 1);

        red_light_btn = new QRadioButton(gridLayoutWidget_4);
        red_light_btn->setObjectName(QString::fromUtf8("red_light_btn"));

        gridLayout_planning->addWidget(red_light_btn, 4, 4, 1, 2);

        green_light_btn = new QRadioButton(gridLayoutWidget_4);
        green_light_btn->setObjectName(QString::fromUtf8("green_light_btn"));
        green_light_btn->setEnabled(true);
        green_light_btn->setChecked(true);

        gridLayout_planning->addWidget(green_light_btn, 5, 4, 1, 2);

        weather_label = new QLabel(gridLayoutWidget_4);
        weather_label->setObjectName(QString::fromUtf8("weather_label"));
        weather_label->setAlignment(Qt::AlignCenter);

        gridLayout_planning->addWidget(weather_label, 6, 0, 1, 1);

        straight_light = new QPushButton(gridLayoutWidget_4);
        straight_light->setObjectName(QString::fromUtf8("straight_light"));
        straight_light->setEnabled(false);

        gridLayout_planning->addWidget(straight_light, 4, 3, 1, 1);

        navigation_progressBar = new QProgressBar(gridLayoutWidget_4);
        navigation_progressBar->setObjectName(QString::fromUtf8("navigation_progressBar"));
        navigation_progressBar->setEnabled(true);
        QFont font3;
        font3.setBold(true);
        font3.setItalic(false);
        font3.setUnderline(false);
        font3.setWeight(75);
        font3.setStrikeOut(false);
        navigation_progressBar->setFont(font3);
        navigation_progressBar->setMouseTracking(false);
        navigation_progressBar->setStyleSheet(
            QString::fromUtf8("QProgressBar::chunk { background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, "
                              "y2:0, stop:0 rgba(65, 94, 207, 100), stop:1 rgba(65, 94, 207, 255));}"));
        navigation_progressBar->setValue(90);
        navigation_progressBar->setAlignment(Qt::AlignCenter);
        navigation_progressBar->setTextVisible(true);
        navigation_progressBar->setOrientation(Qt::Horizontal);
        navigation_progressBar->setInvertedAppearance(false);
        navigation_progressBar->setTextDirection(QProgressBar::TopToBottom);

        gridLayout_planning->addWidget(navigation_progressBar, 0, 0, 1, 6);

        max_speed_doubleSpinBox = new QDoubleSpinBox(gridLayoutWidget_4);
        max_speed_doubleSpinBox->setObjectName(QString::fromUtf8("max_speed_doubleSpinBox"));
        max_speed_doubleSpinBox->setDecimals(1);
        max_speed_doubleSpinBox->setMaximum(10.000000000000000);
        max_speed_doubleSpinBox->setSingleStep(0.200000000000000);
        max_speed_doubleSpinBox->setValue(2.000000000000000);

        gridLayout_planning->addWidget(max_speed_doubleSpinBox, 4, 1, 1, 1);

        tabWidget_subplanner = new QTabWidget(gridLayoutWidget_4);
        tabWidget_subplanner->setObjectName(QString::fromUtf8("tabWidget_subplanner"));
        tabWidget_subplanner->setMinimumSize(QSize(0, 120));
        path_planner = new QWidget();
        path_planner->setObjectName(QString::fromUtf8("path_planner"));
        path_planner->setEnabled(true);
        gridLayoutWidget_7 = new QWidget(path_planner);
        gridLayoutWidget_7->setObjectName(QString::fromUtf8("gridLayoutWidget_7"));
        gridLayoutWidget_7->setGeometry(QRect(0, 10, 461, 141));
        gridLayout_path_subplanner = new QGridLayout(gridLayoutWidget_7);
        gridLayout_path_subplanner->setObjectName(QString::fromUtf8("gridLayout_path_subplanner"));
        gridLayout_path_subplanner->setContentsMargins(0, 0, 0, 0);
        backup_planner_status_label = new QLabel(gridLayoutWidget_7);
        backup_planner_status_label->setObjectName(QString::fromUtf8("backup_planner_status_label"));
        backup_planner_status_label->setAlignment(Qt::AlignLeading | Qt::AlignLeft | Qt::AlignVCenter);

        gridLayout_path_subplanner->addWidget(backup_planner_status_label, 1, 0, 1, 1);

        off_trailer_planner_btn = new QPushButton(gridLayoutWidget_7);
        off_trailer_planner_btn->setObjectName(QString::fromUtf8("off_trailer_planner_btn"));

        gridLayout_path_subplanner->addWidget(off_trailer_planner_btn, 0, 2, 1, 1);

        on_trailer_planner_btn = new QPushButton(gridLayoutWidget_7);
        on_trailer_planner_btn->setObjectName(QString::fromUtf8("on_trailer_planner_btn"));

        gridLayout_path_subplanner->addWidget(on_trailer_planner_btn, 0, 1, 1, 1);

        on_backup_planner_btn = new QPushButton(gridLayoutWidget_7);
        on_backup_planner_btn->setObjectName(QString::fromUtf8("on_backup_planner_btn"));

        gridLayout_path_subplanner->addWidget(on_backup_planner_btn, 1, 1, 1, 1);

        off_backup_planner_btn = new QPushButton(gridLayoutWidget_7);
        off_backup_planner_btn->setObjectName(QString::fromUtf8("off_backup_planner_btn"));

        gridLayout_path_subplanner->addWidget(off_backup_planner_btn, 1, 2, 1, 1);

        trailer_planner_status_label = new QLabel(gridLayoutWidget_7);
        trailer_planner_status_label->setObjectName(QString::fromUtf8("trailer_planner_status_label"));
        trailer_planner_status_label->setTextFormat(Qt::AutoText);
        trailer_planner_status_label->setAlignment(Qt::AlignLeading | Qt::AlignLeft | Qt::AlignVCenter);

        gridLayout_path_subplanner->addWidget(trailer_planner_status_label, 0, 0, 1, 1);

        verticalSpacer_7 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_path_subplanner->addItem(verticalSpacer_7, 2, 0, 1, 3);

        gridLayout_path_subplanner->setColumnStretch(0, 2);
        tabWidget_subplanner->addTab(path_planner, QString());
        velocity_planner = new QWidget();
        velocity_planner->setObjectName(QString::fromUtf8("velocity_planner"));
        gridLayoutWidget_8 = new QWidget(velocity_planner);
        gridLayoutWidget_8->setObjectName(QString::fromUtf8("gridLayoutWidget_8"));
        gridLayoutWidget_8->setGeometry(QRect(0, 0, 461, 171));
        gridLayout_velocity_subplanner = new QGridLayout(gridLayoutWidget_8);
        gridLayout_velocity_subplanner->setObjectName(QString::fromUtf8("gridLayout_velocity_subplanner"));
        gridLayout_velocity_subplanner->setContentsMargins(0, 0, 0, 0);
        off_extra_strategy_manager_btn = new QPushButton(gridLayoutWidget_8);
        off_extra_strategy_manager_btn->setObjectName(QString::fromUtf8("off_extra_strategy_manager_btn"));

        gridLayout_velocity_subplanner->addWidget(off_extra_strategy_manager_btn, 3, 2, 1, 1);

        off_trajectory_checker_btn = new QPushButton(gridLayoutWidget_8);
        off_trajectory_checker_btn->setObjectName(QString::fromUtf8("off_trajectory_checker_btn"));

        gridLayout_velocity_subplanner->addWidget(off_trajectory_checker_btn, 0, 2, 1, 1);

        speed_bump_manager_status_label = new QLabel(gridLayoutWidget_8);
        speed_bump_manager_status_label->setObjectName(QString::fromUtf8("speed_bump_manager_status_label"));

        gridLayout_velocity_subplanner->addWidget(speed_bump_manager_status_label, 2, 0, 1, 1);

        on_speed_bump_manager_btn = new QPushButton(gridLayoutWidget_8);
        on_speed_bump_manager_btn->setObjectName(QString::fromUtf8("on_speed_bump_manager_btn"));

        gridLayout_velocity_subplanner->addWidget(on_speed_bump_manager_btn, 2, 1, 1, 1);

        trajectory_checker_status_label = new QLabel(gridLayoutWidget_8);
        trajectory_checker_status_label->setObjectName(QString::fromUtf8("trajectory_checker_status_label"));
        trajectory_checker_status_label->setTextFormat(Qt::AutoText);

        gridLayout_velocity_subplanner->addWidget(trajectory_checker_status_label, 0, 0, 1, 1);

        extra_strategy_manager_status_label = new QLabel(gridLayoutWidget_8);
        extra_strategy_manager_status_label->setObjectName(QString::fromUtf8("extra_strategy_manager_status_label"));

        gridLayout_velocity_subplanner->addWidget(extra_strategy_manager_status_label, 3, 0, 1, 1);

        traffic_light_manager_status_label = new QLabel(gridLayoutWidget_8);
        traffic_light_manager_status_label->setObjectName(QString::fromUtf8("traffic_light_manager_status_label"));

        gridLayout_velocity_subplanner->addWidget(traffic_light_manager_status_label, 1, 0, 1, 1);

        off_traffic_light_manager_btn = new QPushButton(gridLayoutWidget_8);
        off_traffic_light_manager_btn->setObjectName(QString::fromUtf8("off_traffic_light_manager_btn"));

        gridLayout_velocity_subplanner->addWidget(off_traffic_light_manager_btn, 1, 2, 1, 1);

        off_speed_bump_manager_btn = new QPushButton(gridLayoutWidget_8);
        off_speed_bump_manager_btn->setObjectName(QString::fromUtf8("off_speed_bump_manager_btn"));

        gridLayout_velocity_subplanner->addWidget(off_speed_bump_manager_btn, 2, 2, 1, 1);

        on_extra_strategy_manager_btn = new QPushButton(gridLayoutWidget_8);
        on_extra_strategy_manager_btn->setObjectName(QString::fromUtf8("on_extra_strategy_manager_btn"));

        gridLayout_velocity_subplanner->addWidget(on_extra_strategy_manager_btn, 3, 1, 1, 1);

        verticalSpacer_6 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_velocity_subplanner->addItem(verticalSpacer_6, 5, 0, 1, 3);

        on_trajectory_checker_btn = new QPushButton(gridLayoutWidget_8);
        on_trajectory_checker_btn->setObjectName(QString::fromUtf8("on_trajectory_checker_btn"));

        gridLayout_velocity_subplanner->addWidget(on_trajectory_checker_btn, 0, 1, 1, 1);

        on_traffic_light_manager_btn = new QPushButton(gridLayoutWidget_8);
        on_traffic_light_manager_btn->setObjectName(QString::fromUtf8("on_traffic_light_manager_btn"));

        gridLayout_velocity_subplanner->addWidget(on_traffic_light_manager_btn, 1, 1, 1, 1);

        drivable_space_manager_status_label = new QLabel(gridLayoutWidget_8);
        drivable_space_manager_status_label->setObjectName(QString::fromUtf8("drivable_space_manager_status_label"));

        gridLayout_velocity_subplanner->addWidget(drivable_space_manager_status_label, 4, 0, 1, 1);

        on_drivable_space_manager_btn = new QPushButton(gridLayoutWidget_8);
        on_drivable_space_manager_btn->setObjectName(QString::fromUtf8("on_drivable_space_manager_btn"));

        gridLayout_velocity_subplanner->addWidget(on_drivable_space_manager_btn, 4, 1, 1, 1);

        off_drivable_space_manager_btn = new QPushButton(gridLayoutWidget_8);
        off_drivable_space_manager_btn->setObjectName(QString::fromUtf8("off_drivable_space_manager_btn"));

        gridLayout_velocity_subplanner->addWidget(off_drivable_space_manager_btn, 4, 2, 1, 1);

        gridLayout_velocity_subplanner->setColumnStretch(0, 2);
        tabWidget_subplanner->addTab(velocity_planner, QString());
        traffic_device = new QWidget();
        traffic_device->setObjectName(QString::fromUtf8("traffic_device"));
        traffic_device->setEnabled(true);
        traffic_device->setLayoutDirection(Qt::LeftToRight);
        traffic_device->setAutoFillBackground(false);
        gridLayoutWidget_9 = new QWidget(traffic_device);
        gridLayoutWidget_9->setObjectName(QString::fromUtf8("gridLayoutWidget_9"));
        gridLayoutWidget_9->setGeometry(QRect(-1, 9, 471, 161));
        gridLayout_traffic_device = new QGridLayout(gridLayoutWidget_9);
        gridLayout_traffic_device->setObjectName(QString::fromUtf8("gridLayout_traffic_device"));
        gridLayout_traffic_device->setContentsMargins(0, 0, 0, 0);
        east_light_status_label = new QwtTextLabel(gridLayoutWidget_9);
        east_light_status_label->setObjectName(QString::fromUtf8("east_light_status_label"));

        gridLayout_traffic_device->addWidget(east_light_status_label, 0, 0, 1, 1);

        east_light_time_lcd = new QLCDNumber(gridLayoutWidget_9);
        east_light_time_lcd->setObjectName(QString::fromUtf8("east_light_time_lcd"));
        east_light_time_lcd->setMaximumSize(QSize(70, ********));

        gridLayout_traffic_device->addWidget(east_light_time_lcd, 0, 1, 1, 1);

        south_traffic_color_box = new QComboBox(gridLayoutWidget_9);
        south_traffic_color_box->addItem(QString());
        south_traffic_color_box->addItem(QString());
        south_traffic_color_box->addItem(QString());
        south_traffic_color_box->addItem(QString());
        south_traffic_color_box->setObjectName(QString::fromUtf8("south_traffic_color_box"));
        south_traffic_color_box->setMaximumSize(QSize(100, ********));

        gridLayout_traffic_device->addWidget(south_traffic_color_box, 1, 3, 1, 1);

        north_traffic_color_box = new QComboBox(gridLayoutWidget_9);
        north_traffic_color_box->addItem(QString());
        north_traffic_color_box->addItem(QString());
        north_traffic_color_box->addItem(QString());
        north_traffic_color_box->addItem(QString());
        north_traffic_color_box->setObjectName(QString::fromUtf8("north_traffic_color_box"));
        north_traffic_color_box->setMaximumSize(QSize(100, ********));

        gridLayout_traffic_device->addWidget(north_traffic_color_box, 3, 3, 1, 1);

        north_traffic_rule_box = new QComboBox(gridLayoutWidget_9);
        north_traffic_rule_box->addItem(QString());
        north_traffic_rule_box->addItem(QString());
        north_traffic_rule_box->addItem(QString());
        north_traffic_rule_box->addItem(QString());
        north_traffic_rule_box->setObjectName(QString::fromUtf8("north_traffic_rule_box"));

        gridLayout_traffic_device->addWidget(north_traffic_rule_box, 3, 2, 1, 1);

        east_traffic_color_box = new QComboBox(gridLayoutWidget_9);
        east_traffic_color_box->addItem(QString());
        east_traffic_color_box->addItem(QString());
        east_traffic_color_box->addItem(QString());
        east_traffic_color_box->addItem(QString());
        east_traffic_color_box->setObjectName(QString::fromUtf8("east_traffic_color_box"));
        east_traffic_color_box->setMaximumSize(QSize(100, ********));

        gridLayout_traffic_device->addWidget(east_traffic_color_box, 0, 3, 1, 1);

        west_traffic_rule_box = new QComboBox(gridLayoutWidget_9);
        west_traffic_rule_box->addItem(QString());
        west_traffic_rule_box->addItem(QString());
        west_traffic_rule_box->addItem(QString());
        west_traffic_rule_box->addItem(QString());
        west_traffic_rule_box->setObjectName(QString::fromUtf8("west_traffic_rule_box"));

        gridLayout_traffic_device->addWidget(west_traffic_rule_box, 2, 2, 1, 1);

        south_light_time_lcd = new QLCDNumber(gridLayoutWidget_9);
        south_light_time_lcd->setObjectName(QString::fromUtf8("south_light_time_lcd"));
        south_light_time_lcd->setMaximumSize(QSize(70, ********));

        gridLayout_traffic_device->addWidget(south_light_time_lcd, 1, 1, 1, 1);

        south_traffic_rule_box = new QComboBox(gridLayoutWidget_9);
        south_traffic_rule_box->addItem(QString());
        south_traffic_rule_box->addItem(QString());
        south_traffic_rule_box->addItem(QString());
        south_traffic_rule_box->addItem(QString());
        south_traffic_rule_box->setObjectName(QString::fromUtf8("south_traffic_rule_box"));

        gridLayout_traffic_device->addWidget(south_traffic_rule_box, 1, 2, 1, 1);

        west_light_time_lcd = new QLCDNumber(gridLayoutWidget_9);
        west_light_time_lcd->setObjectName(QString::fromUtf8("west_light_time_lcd"));
        west_light_time_lcd->setMaximumSize(QSize(70, ********));

        gridLayout_traffic_device->addWidget(west_light_time_lcd, 2, 1, 1, 1);

        west_light_status_label = new QwtTextLabel(gridLayoutWidget_9);
        west_light_status_label->setObjectName(QString::fromUtf8("west_light_status_label"));

        gridLayout_traffic_device->addWidget(west_light_status_label, 2, 0, 1, 1);

        north_light_status_label = new QwtTextLabel(gridLayoutWidget_9);
        north_light_status_label->setObjectName(QString::fromUtf8("north_light_status_label"));

        gridLayout_traffic_device->addWidget(north_light_status_label, 3, 0, 1, 1);

        south_light_status_label = new QwtTextLabel(gridLayoutWidget_9);
        south_light_status_label->setObjectName(QString::fromUtf8("south_light_status_label"));

        gridLayout_traffic_device->addWidget(south_light_status_label, 1, 0, 1, 1);

        east_traffic_rule_box = new QComboBox(gridLayoutWidget_9);
        east_traffic_rule_box->addItem(QString());
        east_traffic_rule_box->addItem(QString());
        east_traffic_rule_box->addItem(QString());
        east_traffic_rule_box->addItem(QString());
        east_traffic_rule_box->setObjectName(QString::fromUtf8("east_traffic_rule_box"));

        gridLayout_traffic_device->addWidget(east_traffic_rule_box, 0, 2, 1, 1);

        west_traffic_color_box = new QComboBox(gridLayoutWidget_9);
        west_traffic_color_box->addItem(QString());
        west_traffic_color_box->addItem(QString());
        west_traffic_color_box->addItem(QString());
        west_traffic_color_box->addItem(QString());
        west_traffic_color_box->setObjectName(QString::fromUtf8("west_traffic_color_box"));
        west_traffic_color_box->setMaximumSize(QSize(100, ********));

        gridLayout_traffic_device->addWidget(west_traffic_color_box, 2, 3, 1, 1);

        north_light_time_lcd = new QLCDNumber(gridLayoutWidget_9);
        north_light_time_lcd->setObjectName(QString::fromUtf8("north_light_time_lcd"));
        north_light_time_lcd->setMaximumSize(QSize(70, ********));

        gridLayout_traffic_device->addWidget(north_light_time_lcd, 3, 1, 1, 1);

        east_light_time_spinBox = new QSpinBox(gridLayoutWidget_9);
        east_light_time_spinBox->setObjectName(QString::fromUtf8("east_light_time_spinBox"));
        east_light_time_spinBox->setMaximum(200);

        gridLayout_traffic_device->addWidget(east_light_time_spinBox, 0, 4, 1, 1);

        south_light_time_spinBox = new QSpinBox(gridLayoutWidget_9);
        south_light_time_spinBox->setObjectName(QString::fromUtf8("south_light_time_spinBox"));
        south_light_time_spinBox->setMaximum(200);

        gridLayout_traffic_device->addWidget(south_light_time_spinBox, 1, 4, 1, 1);

        west_light_time_spinBox = new QSpinBox(gridLayoutWidget_9);
        west_light_time_spinBox->setObjectName(QString::fromUtf8("west_light_time_spinBox"));
        west_light_time_spinBox->setMaximum(200);

        gridLayout_traffic_device->addWidget(west_light_time_spinBox, 2, 4, 1, 1);

        north_light_time_spinBox = new QSpinBox(gridLayoutWidget_9);
        north_light_time_spinBox->setObjectName(QString::fromUtf8("north_light_time_spinBox"));
        north_light_time_spinBox->setMaximum(200);

        gridLayout_traffic_device->addWidget(north_light_time_spinBox, 3, 4, 1, 1);

        traffic_device_response_btn = new QPushButton(gridLayoutWidget_9);
        traffic_device_response_btn->setObjectName(QString::fromUtf8("traffic_device_response_btn"));
        traffic_device_response_btn->setLayoutDirection(Qt::LeftToRight);

        gridLayout_traffic_device->addWidget(traffic_device_response_btn, 0, 5, 1, 1);

        verticalSpacer_3 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_traffic_device->addItem(verticalSpacer_3, 4, 0, 1, 6);

        set_traffic_time_btn = new QPushButton(gridLayoutWidget_9);
        set_traffic_time_btn->setObjectName(QString::fromUtf8("set_traffic_time_btn"));

        gridLayout_traffic_device->addWidget(set_traffic_time_btn, 1, 5, 1, 1);

        deivce_sim_status_show_switch_btn = new QPushButton(gridLayoutWidget_9);
        deivce_sim_status_show_switch_btn->setObjectName(QString::fromUtf8("deivce_sim_status_show_switch_btn"));

        gridLayout_traffic_device->addWidget(deivce_sim_status_show_switch_btn, 3, 5, 1, 1);

        gridLayout_traffic_device->setColumnStretch(5, 1);
        tabWidget_subplanner->addTab(traffic_device, QString());

        gridLayout_planning->addWidget(tabWidget_subplanner, 7, 0, 5, 6);

        max_speed_label_2 = new QLabel(gridLayoutWidget_4);
        max_speed_label_2->setObjectName(QString::fromUtf8("max_speed_label_2"));
        max_speed_label_2->setAlignment(Qt::AlignCenter);

        gridLayout_planning->addWidget(max_speed_label_2, 4, 0, 1, 1);

        label_7 = new QLabel(gridLayoutWidget_4);
        label_7->setObjectName(QString::fromUtf8("label_7"));
        label_7->setTextFormat(Qt::RichText);

        gridLayout_planning->addWidget(label_7, 1, 3, 1, 3);

        rain_fall_box = new QComboBox(gridLayoutWidget_4);
        rain_fall_box->addItem(QString());
        rain_fall_box->addItem(QString());
        rain_fall_box->addItem(QString());
        rain_fall_box->addItem(QString());
        rain_fall_box->setObjectName(QString::fromUtf8("rain_fall_box"));

        gridLayout_planning->addWidget(rain_fall_box, 6, 1, 1, 2);

        routing_planning_btn = new QPushButton(gridLayoutWidget_4);
        routing_planning_btn->setObjectName(QString::fromUtf8("routing_planning_btn"));

        gridLayout_planning->addWidget(routing_planning_btn, 1, 0, 1, 3);

        free_navigation_btn = new QPushButton(gridLayoutWidget_4);
        free_navigation_btn->setObjectName(QString::fromUtf8("free_navigation_btn"));

        gridLayout_planning->addWidget(free_navigation_btn, 2, 0, 1, 3);

        yellow_light_btn = new QRadioButton(gridLayoutWidget_4);
        yellow_light_btn->setObjectName(QString::fromUtf8("yellow_light_btn"));

        gridLayout_planning->addWidget(yellow_light_btn, 6, 4, 1, 2);

        cancel_navigation_btn = new QPushButton(gridLayoutWidget_4);
        cancel_navigation_btn->setObjectName(QString::fromUtf8("cancel_navigation_btn"));

        gridLayout_planning->addWidget(cancel_navigation_btn, 3, 0, 1, 3);

        max_speed_label = new QLabel(gridLayoutWidget_4);
        max_speed_label->setObjectName(QString::fromUtf8("max_speed_label"));
        max_speed_label->setAlignment(Qt::AlignCenter);

        gridLayout_planning->addWidget(max_speed_label, 4, 2, 1, 1);

        goal_pose_x_spin_box = new QDoubleSpinBox(gridLayoutWidget_4);
        goal_pose_x_spin_box->setObjectName(QString::fromUtf8("goal_pose_x_spin_box"));
        goal_pose_x_spin_box->setMaximum(100.989999999999995);

        gridLayout_planning->addWidget(goal_pose_x_spin_box, 3, 3, 1, 1);

        goal_pose_y_spin_box = new QDoubleSpinBox(gridLayoutWidget_4);
        goal_pose_y_spin_box->setObjectName(QString::fromUtf8("goal_pose_y_spin_box"));

        gridLayout_planning->addWidget(goal_pose_y_spin_box, 3, 4, 1, 1);

        goal_pose_theta_spin_box = new QDoubleSpinBox(gridLayoutWidget_4);
        goal_pose_theta_spin_box->setObjectName(QString::fromUtf8("goal_pose_theta_spin_box"));

        gridLayout_planning->addWidget(goal_pose_theta_spin_box, 3, 5, 1, 1);

        goal_posex_label = new QLabel(gridLayoutWidget_4);
        goal_posex_label->setObjectName(QString::fromUtf8("goal_posex_label"));
        goal_posex_label->setAlignment(Qt::AlignCenter);

        gridLayout_planning->addWidget(goal_posex_label, 2, 3, 1, 1);

        goal_pose_y_label = new QLabel(gridLayoutWidget_4);
        goal_pose_y_label->setObjectName(QString::fromUtf8("goal_pose_y_label"));
        goal_pose_y_label->setAlignment(Qt::AlignCenter);

        gridLayout_planning->addWidget(goal_pose_y_label, 2, 4, 1, 1);

        goal_pose_theta_label = new QLabel(gridLayoutWidget_4);
        goal_pose_theta_label->setObjectName(QString::fromUtf8("goal_pose_theta_label"));
        goal_pose_theta_label->setAlignment(Qt::AlignCenter);

        gridLayout_planning->addWidget(goal_pose_theta_label, 2, 5, 1, 1);

        tabWidget->addTab(tab_planning, QString());
        tab_goal = new QWidget();
        tab_goal->setObjectName(QString::fromUtf8("tab_goal"));
        gridLayoutWidget_5 = new QWidget(tab_goal);
        gridLayoutWidget_5->setObjectName(QString::fromUtf8("gridLayoutWidget_5"));
        gridLayoutWidget_5->setGeometry(QRect(20, 20, 481, 461));
        gridLayout_goal = new QGridLayout(gridLayoutWidget_5);
        gridLayout_goal->setObjectName(QString::fromUtf8("gridLayout_goal"));
        gridLayout_goal->setContentsMargins(5, 5, 5, 5);
        planning_selected_goal_btn = new QPushButton(gridLayoutWidget_5);
        planning_selected_goal_btn->setObjectName(QString::fromUtf8("planning_selected_goal_btn"));

        gridLayout_goal->addWidget(planning_selected_goal_btn, 3, 0, 1, 2);

        goal_name_edit = new QLineEdit(gridLayoutWidget_5);
        goal_name_edit->setObjectName(QString::fromUtf8("goal_name_edit"));
        goal_name_edit->setMaximumSize(QSize(100, ********));

        gridLayout_goal->addWidget(goal_name_edit, 1, 1, 1, 1);

        label_8 = new QLabel(gridLayoutWidget_5);
        label_8->setObjectName(QString::fromUtf8("label_8"));
        label_8->setMaximumSize(QSize(********, 20));

        gridLayout_goal->addWidget(label_8, 0, 0, 1, 1);

        verticalSpacer_4 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_goal->addItem(verticalSpacer_4, 9, 0, 1, 1);

        goal_text_browser = new QTextBrowser(gridLayoutWidget_5);
        goal_text_browser->setObjectName(QString::fromUtf8("goal_text_browser"));
        goal_text_browser->setMaximumSize(QSize(100, 100));

        gridLayout_goal->addWidget(goal_text_browser, 1, 0, 2, 1);

        delete_selected_goal_btn = new QPushButton(gridLayoutWidget_5);
        delete_selected_goal_btn->setObjectName(QString::fromUtf8("delete_selected_goal_btn"));

        gridLayout_goal->addWidget(delete_selected_goal_btn, 4, 0, 1, 2);

        save_goal_btn = new QPushButton(gridLayoutWidget_5);
        save_goal_btn->setObjectName(QString::fromUtf8("save_goal_btn"));
        save_goal_btn->setMaximumSize(QSize(100, ********));

        gridLayout_goal->addWidget(save_goal_btn, 2, 1, 1, 1);

        label_loop_planning = new QLabel(gridLayoutWidget_5);
        label_loop_planning->setObjectName(QString::fromUtf8("label_loop_planning"));
        label_loop_planning->setMaximumSize(QSize(10000, 200));

        gridLayout_goal->addWidget(label_loop_planning, 8, 0, 1, 2);

        goal_pose_table = new QTableWidget(gridLayoutWidget_5);
        if (goal_pose_table->columnCount() < 2) goal_pose_table->setColumnCount(2);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        goal_pose_table->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        goal_pose_table->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        goal_pose_table->setObjectName(QString::fromUtf8("goal_pose_table"));
        QSizePolicy sizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(goal_pose_table->sizePolicy().hasHeightForWidth());
        goal_pose_table->setSizePolicy(sizePolicy);
        goal_pose_table->setDragEnabled(true);
        goal_pose_table->setSelectionMode(QAbstractItemView::ExtendedSelection);
        goal_pose_table->horizontalHeader()->setCascadingSectionResizes(true);
        goal_pose_table->horizontalHeader()->setDefaultSectionSize(100);

        gridLayout_goal->addWidget(goal_pose_table, 0, 2, 10, 1);

        label_9 = new QLabel(gridLayoutWidget_5);
        label_9->setObjectName(QString::fromUtf8("label_9"));
        label_9->setMaximumSize(QSize(100, 20));

        gridLayout_goal->addWidget(label_9, 0, 1, 1, 1);

        loop_planning_goal_btn = new QPushButton(gridLayoutWidget_5);
        loop_planning_goal_btn->setObjectName(QString::fromUtf8("loop_planning_goal_btn"));

        gridLayout_goal->addWidget(loop_planning_goal_btn, 5, 0, 1, 2);

        loop_planning_stop_btn = new QPushButton(gridLayoutWidget_5);
        loop_planning_stop_btn->setObjectName(QString::fromUtf8("loop_planning_stop_btn"));

        gridLayout_goal->addWidget(loop_planning_stop_btn, 6, 0, 1, 2);

        one_loop_checkBox = new QCheckBox(gridLayoutWidget_5);
        one_loop_checkBox->setObjectName(QString::fromUtf8("one_loop_checkBox"));

        gridLayout_goal->addWidget(one_loop_checkBox, 7, 0, 1, 1);

        tabWidget->addTab(tab_goal, QString());
        tab_map_node = new QWidget();
        tab_map_node->setObjectName(QString::fromUtf8("tab_map_node"));
        gridLayoutWidget_6 = new QWidget(tab_map_node);
        gridLayoutWidget_6->setObjectName(QString::fromUtf8("gridLayoutWidget_6"));
        gridLayoutWidget_6->setGeometry(QRect(60, 20, 391, 211));
        gridLayout_map_mode = new QGridLayout(gridLayoutWidget_6);
        gridLayout_map_mode->setObjectName(QString::fromUtf8("gridLayout_map_mode"));
        gridLayout_map_mode->setContentsMargins(5, 5, 5, 5);
        set_mode_btn = new QPushButton(gridLayoutWidget_6);
        set_mode_btn->setObjectName(QString::fromUtf8("set_mode_btn"));

        gridLayout_map_mode->addWidget(set_mode_btn, 1, 1, 1, 1);

        mode_comboBox = new QComboBox(gridLayoutWidget_6);
        mode_comboBox->setObjectName(QString::fromUtf8("mode_comboBox"));

        gridLayout_map_mode->addWidget(mode_comboBox, 1, 0, 1, 1);

        set_map_btn = new QPushButton(gridLayoutWidget_6);
        set_map_btn->setObjectName(QString::fromUtf8("set_map_btn"));

        gridLayout_map_mode->addWidget(set_map_btn, 0, 1, 1, 1);

        map_comboBox = new QComboBox(gridLayoutWidget_6);
        map_comboBox->setObjectName(QString::fromUtf8("map_comboBox"));

        gridLayout_map_mode->addWidget(map_comboBox, 0, 0, 1, 1);

        verticalSpacer_5 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_map_mode->addItem(verticalSpacer_5, 2, 0, 1, 1);

        tabWidget->addTab(tab_map_node, QString());
        tab_log = new QWidget();
        tab_log->setObjectName(QString::fromUtf8("tab_log"));
        layoutWidget = new QWidget(tab_log);
        layoutWidget->setObjectName(QString::fromUtf8("layoutWidget"));
        layoutWidget->setGeometry(QRect(40, 20, 520, 438));
        gridLayout_log = new QGridLayout(layoutWidget);
        gridLayout_log->setObjectName(QString::fromUtf8("gridLayout_log"));
        gridLayout_log->setContentsMargins(0, 0, 0, 0);
        debug_info_text_browser = new QTextBrowser(layoutWidget);
        debug_info_text_browser->setObjectName(QString::fromUtf8("debug_info_text_browser"));

        gridLayout_log->addWidget(debug_info_text_browser, 1, 1, 3, 1);

        task_request_text_browser = new QTextBrowser(layoutWidget);
        task_request_text_browser->setObjectName(QString::fromUtf8("task_request_text_browser"));

        gridLayout_log->addWidget(task_request_text_browser, 1, 0, 1, 1);

        label = new QLabel(layoutWidget);
        label->setObjectName(QString::fromUtf8("label"));

        gridLayout_log->addWidget(label, 0, 0, 1, 1);

        task_response_text_browser = new QTextBrowser(layoutWidget);
        task_response_text_browser->setObjectName(QString::fromUtf8("task_response_text_browser"));

        gridLayout_log->addWidget(task_response_text_browser, 3, 0, 1, 1);

        label_4 = new QLabel(layoutWidget);
        label_4->setObjectName(QString::fromUtf8("label_4"));

        gridLayout_log->addWidget(label_4, 0, 1, 1, 1);

        label_2 = new QLabel(layoutWidget);
        label_2->setObjectName(QString::fromUtf8("label_2"));

        gridLayout_log->addWidget(label_2, 2, 0, 1, 1);

        tabWidget->addTab(tab_log, QString());
        tab_simulation = new QWidget();
        tab_simulation->setObjectName(QString::fromUtf8("tab_simulation"));
        gridLayoutWidget_10 = new QWidget(tab_simulation);
        gridLayoutWidget_10->setObjectName(QString::fromUtf8("gridLayoutWidget_10"));
        gridLayoutWidget_10->setGeometry(QRect(80, 40, 647, 559));
        gridLayout_sim = new QGridLayout(gridLayoutWidget_10);
        gridLayout_sim->setObjectName(QString::fromUtf8("gridLayout_sim"));
        gridLayout_sim->setContentsMargins(5, 5, 5, 5);
        sim_label_empty = new QLabel(gridLayoutWidget_10);
        sim_label_empty->setObjectName(QString::fromUtf8("sim_label_empty"));

        gridLayout_sim->addWidget(sim_label_empty, 5, 0, 1, 4);

        sim_edit_btn = new QPushButton(gridLayoutWidget_10);
        sim_edit_btn->setObjectName(QString::fromUtf8("sim_edit_btn"));

        gridLayout_sim->addWidget(sim_edit_btn, 4, 2, 1, 1);

        sim_run_btn = new QPushButton(gridLayoutWidget_10);
        sim_run_btn->setObjectName(QString::fromUtf8("sim_run_btn"));

        gridLayout_sim->addWidget(sim_run_btn, 4, 1, 1, 1);

        label_6 = new QLabel(gridLayoutWidget_10);
        label_6->setObjectName(QString::fromUtf8("label_6"));

        gridLayout_sim->addWidget(label_6, 6, 0, 1, 1);

        sim_config_table = new QTableWidget(gridLayoutWidget_10);
        if (sim_config_table->columnCount() < 3) sim_config_table->setColumnCount(3);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        sim_config_table->setHorizontalHeaderItem(0, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        sim_config_table->setHorizontalHeaderItem(1, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        sim_config_table->setHorizontalHeaderItem(2, __qtablewidgetitem4);
        sim_config_table->setObjectName(QString::fromUtf8("sim_config_table"));
        sizePolicy.setHeightForWidth(sim_config_table->sizePolicy().hasHeightForWidth());
        sim_config_table->setSizePolicy(sizePolicy);
        sim_config_table->setDragEnabled(true);
        sim_config_table->setSelectionMode(QAbstractItemView::ExtendedSelection);
        sim_config_table->horizontalHeader()->setCascadingSectionResizes(true);
        sim_config_table->horizontalHeader()->setDefaultSectionSize(100);

        gridLayout_sim->addWidget(sim_config_table, 2, 0, 2, 4);

        sim_delete_btn = new QPushButton(gridLayoutWidget_10);
        sim_delete_btn->setObjectName(QString::fromUtf8("sim_delete_btn"));

        gridLayout_sim->addWidget(sim_delete_btn, 4, 3, 1, 1);

        sim_folder_edit = new QTextEdit(gridLayoutWidget_10);
        sim_folder_edit->setObjectName(QString::fromUtf8("sim_folder_edit"));
        sim_folder_edit->setMaximumSize(QSize(********, 30));

        gridLayout_sim->addWidget(sim_folder_edit, 1, 0, 1, 3);

        sim_save_btn = new QPushButton(gridLayoutWidget_10);
        sim_save_btn->setObjectName(QString::fromUtf8("sim_save_btn"));

        gridLayout_sim->addWidget(sim_save_btn, 6, 3, 1, 1);

        sim_init_btn = new QPushButton(gridLayoutWidget_10);
        sim_init_btn->setObjectName(QString::fromUtf8("sim_init_btn"));

        gridLayout_sim->addWidget(sim_init_btn, 4, 0, 1, 1);

        sim_set_folder_btn = new QPushButton(gridLayoutWidget_10);
        sim_set_folder_btn->setObjectName(QString::fromUtf8("sim_set_folder_btn"));

        gridLayout_sim->addWidget(sim_set_folder_btn, 1, 3, 1, 1);

        verticalSpacer_8 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_sim->addItem(verticalSpacer_8, 7, 0, 1, 1);

        sim_folder_edit_2 = new QTextEdit(gridLayoutWidget_10);
        sim_folder_edit_2->setObjectName(QString::fromUtf8("sim_folder_edit_2"));
        sim_folder_edit_2->setMaximumSize(QSize(********, 30));

        gridLayout_sim->addWidget(sim_folder_edit_2, 6, 1, 1, 2);

        tabWidget->addTab(tab_simulation, QString());

        gridLayout->addWidget(tabWidget, 4, 0, 2, 2);

        gridLayout->setColumnStretch(0, 2);

        retranslateUi(Form);

        tabWidget->setCurrentIndex(6);
        tabWidget_subplanner->setCurrentIndex(2);

        QMetaObject::connectSlotsByName(Form);
    }  // setupUi

    void retranslateUi(QWidget *Form)
    {
        Form->setWindowTitle(QCoreApplication::translate("Form", "Form", nullptr));
        label_planning->setText(QCoreApplication::translate("Form", "Planning", nullptr));
        label_battery->setText(QCoreApplication::translate("Form", "Battrey: 0%", nullptr));
        label_routing->setText(QCoreApplication::translate("Form", "Routing", nullptr));
        label_joy->setText(QCoreApplication::translate("Form", "Joy Control", nullptr));
        checkBox_engage->setText(QCoreApplication::translate("Form", "Engage Velocity", nullptr));
        label_time->setText(QCoreApplication::translate("Form", "Time: 0.0", nullptr));
        label_localization->setText(QCoreApplication::translate("Form", "Localization", nullptr));
        label_horn->setText(QCoreApplication::translate("Form", "Msg:", nullptr));
        start_mapping_btn->setText(QCoreApplication::translate("Form", "Start Mapping", nullptr));
        cancel_mapping_btn->setText(QCoreApplication::translate("Form", "Cancel Mapping", nullptr));
        save_mapping_btn->setText(QCoreApplication::translate("Form", "Save Mapping", nullptr));
        label_3->setText(QCoreApplication::translate("Form", "Map Folder Name", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab_map), QCoreApplication::translate("Form", "Mapping", nullptr));
        label_5->setText(QCoreApplication::translate("Form", "Initial Pose", nullptr));
        cancel_relocate_btn->setText(QCoreApplication::translate("Form", "Cancel Relocate", nullptr));
        gps_relocate_btn->setText(QCoreApplication::translate("Form", "GPS Relocate", nullptr));
        manual_relocate_btn->setText(QCoreApplication::translate("Form", "Manual Relocate", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab_locate), QCoreApplication::translate("Form", "Localization", nullptr));
        turn_right_light->setText(QCoreApplication::translate("Form", "Right Light", nullptr));
        turn_left_light->setText(QCoreApplication::translate("Form", "Left Light", nullptr));
        red_light_btn->setText(QCoreApplication::translate("Form", "red", nullptr));
        green_light_btn->setText(QCoreApplication::translate("Form", "green", nullptr));
        weather_label->setText(QCoreApplication::translate("Form", "Weather", nullptr));
        straight_light->setText(QCoreApplication::translate("Form", "Straight Light", nullptr));
        navigation_progressBar->setFormat(QCoreApplication::translate("Form", "%p%", nullptr));
        backup_planner_status_label->setText(QCoreApplication::translate("Form", "backup_generator", nullptr));
        off_trailer_planner_btn->setText(QCoreApplication::translate("Form", "OFF", nullptr));
        on_trailer_planner_btn->setText(QCoreApplication::translate("Form", "ON", nullptr));
        on_backup_planner_btn->setText(QCoreApplication::translate("Form", "ON", nullptr));
        off_backup_planner_btn->setText(QCoreApplication::translate("Form", "OFF", nullptr));
        trailer_planner_status_label->setText(QCoreApplication::translate("Form", "trailer_planner", nullptr));
        tabWidget_subplanner->setTabText(tabWidget_subplanner->indexOf(path_planner),
                                         QCoreApplication::translate("Form", "path_planner", nullptr));
        off_extra_strategy_manager_btn->setText(QCoreApplication::translate("Form", "OFF", nullptr));
        off_trajectory_checker_btn->setText(QCoreApplication::translate("Form", "OFF", nullptr));
        speed_bump_manager_status_label->setText(QCoreApplication::translate("Form", "speed_bump_manager", nullptr));
        on_speed_bump_manager_btn->setText(QCoreApplication::translate("Form", "ON", nullptr));
        trajectory_checker_status_label->setText(QCoreApplication::translate("Form", "trajectory_checker", nullptr));
        extra_strategy_manager_status_label->setText(QCoreApplication::translate("Form", "extra_strategy_manager", nullptr));
        traffic_light_manager_status_label->setText(QCoreApplication::translate("Form", "traffic_light_manager", nullptr));
        off_traffic_light_manager_btn->setText(QCoreApplication::translate("Form", "OFF", nullptr));
        off_speed_bump_manager_btn->setText(QCoreApplication::translate("Form", "OFF", nullptr));
        on_extra_strategy_manager_btn->setText(QCoreApplication::translate("Form", "ON", nullptr));
        on_trajectory_checker_btn->setText(QCoreApplication::translate("Form", "ON", nullptr));
        on_traffic_light_manager_btn->setText(QCoreApplication::translate("Form", "ON", nullptr));
        drivable_space_manager_status_label->setText(QCoreApplication::translate("Form", "drivable_space_manager", nullptr));
        on_drivable_space_manager_btn->setText(QCoreApplication::translate("Form", "ON", nullptr));
        off_drivable_space_manager_btn->setText(QCoreApplication::translate("Form", "OFF", nullptr));
        tabWidget_subplanner->setTabText(tabWidget_subplanner->indexOf(velocity_planner),
                                         QCoreApplication::translate("Form", "velocity_planner", nullptr));
        east_light_status_label->setPlainText(QCoreApplication::translate("Form", "East", nullptr));
        south_traffic_color_box->setItemText(0, QCoreApplication::translate("Form", "Red", nullptr));
        south_traffic_color_box->setItemText(1, QCoreApplication::translate("Form", "Green", nullptr));
        south_traffic_color_box->setItemText(2, QCoreApplication::translate("Form", "Yellow", nullptr));
        south_traffic_color_box->setItemText(3, QCoreApplication::translate("Form", "Unknown", nullptr));

        north_traffic_color_box->setItemText(0, QCoreApplication::translate("Form", "Red", nullptr));
        north_traffic_color_box->setItemText(1, QCoreApplication::translate("Form", "Green", nullptr));
        north_traffic_color_box->setItemText(2, QCoreApplication::translate("Form", "Yellow", nullptr));
        north_traffic_color_box->setItemText(3, QCoreApplication::translate("Form", "Unknown", nullptr));

        north_traffic_rule_box->setItemText(0, QCoreApplication::translate("Form", "Straight", nullptr));
        north_traffic_rule_box->setItemText(1, QCoreApplication::translate("Form", "Left", nullptr));
        north_traffic_rule_box->setItemText(2, QCoreApplication::translate("Form", "Right", nullptr));
        north_traffic_rule_box->setItemText(3, QCoreApplication::translate("Form", "Unknown", nullptr));

        east_traffic_color_box->setItemText(0, QCoreApplication::translate("Form", "Red", nullptr));
        east_traffic_color_box->setItemText(1, QCoreApplication::translate("Form", "Green", nullptr));
        east_traffic_color_box->setItemText(2, QCoreApplication::translate("Form", "Yellow", nullptr));
        east_traffic_color_box->setItemText(3, QCoreApplication::translate("Form", "Unknown", nullptr));

        west_traffic_rule_box->setItemText(0, QCoreApplication::translate("Form", "Straight", nullptr));
        west_traffic_rule_box->setItemText(1, QCoreApplication::translate("Form", "Left", nullptr));
        west_traffic_rule_box->setItemText(2, QCoreApplication::translate("Form", "Right", nullptr));
        west_traffic_rule_box->setItemText(3, QCoreApplication::translate("Form", "Unknown", nullptr));

        south_traffic_rule_box->setItemText(0, QCoreApplication::translate("Form", "Straight", nullptr));
        south_traffic_rule_box->setItemText(1, QCoreApplication::translate("Form", "Left", nullptr));
        south_traffic_rule_box->setItemText(2, QCoreApplication::translate("Form", "Right", nullptr));
        south_traffic_rule_box->setItemText(3, QCoreApplication::translate("Form", "Unknown", nullptr));

        west_light_status_label->setPlainText(QCoreApplication::translate("Form", "West", nullptr));
        north_light_status_label->setPlainText(QCoreApplication::translate("Form", "North", nullptr));
        south_light_status_label->setPlainText(QCoreApplication::translate("Form", "South", nullptr));
        east_traffic_rule_box->setItemText(0, QCoreApplication::translate("Form", "Straight", nullptr));
        east_traffic_rule_box->setItemText(1, QCoreApplication::translate("Form", "Left", nullptr));
        east_traffic_rule_box->setItemText(2, QCoreApplication::translate("Form", "Right", nullptr));
        east_traffic_rule_box->setItemText(3, QCoreApplication::translate("Form", "Unknown", nullptr));

        west_traffic_color_box->setItemText(0, QCoreApplication::translate("Form", "Red", nullptr));
        west_traffic_color_box->setItemText(1, QCoreApplication::translate("Form", "Green", nullptr));
        west_traffic_color_box->setItemText(2, QCoreApplication::translate("Form", "Yellow", nullptr));
        west_traffic_color_box->setItemText(3, QCoreApplication::translate("Form", "Unknown", nullptr));

        traffic_device_response_btn->setText(QCoreApplication::translate("Form", "Response", nullptr));
        set_traffic_time_btn->setText(QCoreApplication::translate("Form", "Set Time", nullptr));
        deivce_sim_status_show_switch_btn->setText(QCoreApplication::translate("Form", "Signal Sim", nullptr));
        tabWidget_subplanner->setTabText(tabWidget_subplanner->indexOf(traffic_device),
                                         QCoreApplication::translate("Form", "traffic_device", nullptr));
        max_speed_label_2->setText(QCoreApplication::translate("Form", "Max", nullptr));
        label_7->setText(QCoreApplication::translate("Form", "Goal Pose", nullptr));
        rain_fall_box->setItemText(0, QCoreApplication::translate("Form", "Sunny", nullptr));
        rain_fall_box->setItemText(1, QCoreApplication::translate("Form", "Light_Rain", nullptr));
        rain_fall_box->setItemText(2, QCoreApplication::translate("Form", "Moderate_Rain", nullptr));
        rain_fall_box->setItemText(3, QCoreApplication::translate("Form", "Heavy_Rain", nullptr));

        routing_planning_btn->setText(QCoreApplication::translate("Form", "Routing Planning", nullptr));
        free_navigation_btn->setText(QCoreApplication::translate("Form", "Free Navigation", nullptr));
        yellow_light_btn->setText(QCoreApplication::translate("Form", "yellow", nullptr));
        cancel_navigation_btn->setText(QCoreApplication::translate("Form", "Cancel Navigation", nullptr));
        max_speed_label->setText(QCoreApplication::translate("Form", "m/s", nullptr));
        goal_posex_label->setText(QCoreApplication::translate("Form", "x", nullptr));
        goal_pose_y_label->setText(QCoreApplication::translate("Form", "y", nullptr));
        goal_pose_theta_label->setText(QCoreApplication::translate("Form", "theta", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab_planning), QCoreApplication::translate("Form", "Planning", nullptr));
        planning_selected_goal_btn->setText(QCoreApplication::translate("Form", "Routing Planning Selected Goal", nullptr));
        label_8->setText(QCoreApplication::translate("Form", "Goal Pose", nullptr));
        delete_selected_goal_btn->setText(QCoreApplication::translate("Form", "Delete Selected Goal", nullptr));
        save_goal_btn->setText(QCoreApplication::translate("Form", "Save Goal", nullptr));
        label_loop_planning->setText(QCoreApplication::translate("Form", "Status:", nullptr));
        QTableWidgetItem *___qtablewidgetitem = goal_pose_table->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QCoreApplication::translate("Form", "Name", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = goal_pose_table->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QCoreApplication::translate("Form", "Pose", nullptr));
        label_9->setText(QCoreApplication::translate("Form", "Goal Name", nullptr));
        loop_planning_goal_btn->setText(QCoreApplication::translate("Form", "Loop Planning From Selected Goal", nullptr));
        loop_planning_stop_btn->setText(QCoreApplication::translate("Form", "Stop Loop Planning", nullptr));
        one_loop_checkBox->setText(QCoreApplication::translate("Form", "Run One Loop", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab_goal), QCoreApplication::translate("Form", "Goals", nullptr));
        set_mode_btn->setText(QCoreApplication::translate("Form", "set current mode", nullptr));
        set_map_btn->setText(QCoreApplication::translate("Form", "set current map", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab_map_node),
                              QCoreApplication::translate("Form", "Maps && Modes", nullptr));
        label->setText(QCoreApplication::translate("Form", "Request", nullptr));
        label_4->setText(QCoreApplication::translate("Form", "Debug Info", nullptr));
        label_2->setText(QCoreApplication::translate("Form", "Response", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab_log), QCoreApplication::translate("Form", "Log", nullptr));
        sim_label_empty->setText(QCoreApplication::translate(
            "Form", "-----------------------------------------------------------------", nullptr));
        sim_edit_btn->setText(QCoreApplication::translate("Form", "Edit Select", nullptr));
        sim_run_btn->setText(QCoreApplication::translate("Form", "Run", nullptr));
        label_6->setText(QCoreApplication::translate("Form", "Name", nullptr));
        QTableWidgetItem *___qtablewidgetitem2 = sim_config_table->horizontalHeaderItem(0);
        ___qtablewidgetitem2->setText(QCoreApplication::translate("Form", "config", nullptr));
        QTableWidgetItem *___qtablewidgetitem3 = sim_config_table->horizontalHeaderItem(1);
        ___qtablewidgetitem3->setText(QCoreApplication::translate("Form", "description", nullptr));
        QTableWidgetItem *___qtablewidgetitem4 = sim_config_table->horizontalHeaderItem(2);
        ___qtablewidgetitem4->setText(QCoreApplication::translate("Form", "map_name", nullptr));
        sim_delete_btn->setText(QCoreApplication::translate("Form", "Delete Select", nullptr));
        sim_save_btn->setText(QCoreApplication::translate("Form", "Save", nullptr));
        sim_init_btn->setText(QCoreApplication::translate("Form", "Init Simulation", nullptr));
        sim_set_folder_btn->setText(QCoreApplication::translate("Form", "Set Scene Folder", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab_simulation), QCoreApplication::translate("Form", "Sim", nullptr));
    }  // retranslateUi
};

namespace Ui {
class Form : public Ui_Form
{
};
}  // namespace Ui

QT_END_NAMESPACE

#endif  // TASK_RVIZ_PLUGIN_H
