#pragma once

#include <custom_msgs/msg/perception_objects.hpp>
#include <geometry_msgs/msg/pose_with_covariance_stamped.hpp>
#include <yaml-cpp/yaml.h>
#include <geometry_msgs/msg/pose.hpp>
#include "webots_objects_msgs/srv/update_objects.hpp"
#include "rclcpp/rclcpp.hpp"

class SimulationTool
{
public:
    SimulationTool(rclcpp::Node::SharedPtr node);
    ~SimulationTool() = default;

    void load(const std::string &file_path);
    void save(const std::string &file_path);

    void set_description(const std::string &description) { description_ = description; }
    void set_map_name(const std::string &map_name) { map_name_ = map_name; }
    void set_sumo_net_name(const std::string &sumo_net_name) { sumo_net_name_ = sumo_net_name; }
    void set_vehicle_pose(const geometry_msgs::msg::PoseWithCovarianceStamped &vehicle_pose)
    {
        vehicle_pose_ = vehicle_pose;
    }
    void set_target_pose(const geometry_msgs::msg::Pose &target_pose) { target_pose_ = target_pose; }
    void set_objects(const custom_msgs::msg::PerceptionObjects &objects) { objects_ = objects; }

    std::string description() const { return description_; }
    std::string map_name() const { return map_name_; }
    std::string sumo_net_name() const { return sumo_net_name_; }
    geometry_msgs::msg::PoseWithCovarianceStamped vehicle_pose() const { return vehicle_pose_; }
    geometry_msgs::msg::Pose target_pose() const { return target_pose_; }
    custom_msgs::msg::PerceptionObjects objects() const { return objects_; }

    void callDeleteAll();
    void callSetObjects();

private:
    std::string description_;
    std::string map_name_;
    std::string sumo_net_name_;
    geometry_msgs::msg::PoseWithCovarianceStamped vehicle_pose_;
    geometry_msgs::msg::Pose target_pose_;
    custom_msgs::msg::PerceptionObjects objects_;

    rclcpp::Node::SharedPtr node_;
    rclcpp::Client<webots_objects_msgs::srv::UpdateObjects>::SharedPtr update_objects_client_;

    webots_objects_msgs::srv::UpdateObjects::Response::SharedPtr callService(
        webots_objects_msgs::srv::UpdateObjects::Request::SharedPtr &request);
};
