cmake_minimum_required(VERSION 3.5)
project(task_rviz_plugin)

set(CMAKE_CXX_STANDARD 17)

find_package(Qt5 REQUIRED COMPONENTS Widgets)
find_package(PkgConfig REQUIRED)
pkg_check_modules(<PERSON><PERSON><PERSON><PERSON> jsoncpp)

find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rviz_common REQUIRED)
find_package(std_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(custom_msgs REQUIRED)
find_package(umodule_msgs REQUIRED)
find_package(state_msgs REQUIRED)
find_package(ackermann_msgs REQUIRED)
find_package(chitu_msgs REQUIRED)
find_package(nav_msgs REQUIRED)

set(QWT_INCLUDE_DIRS /usr/include/qwt)
set(QWT_LIBRARIES /usr/lib/libqwt-qt5.so)

qt5_wrap_cpp(MOC_FILES include/task_rviz_plugin_panel.hpp include/loop_planning_thread.hpp)
add_library(${PROJECT_NAME} SHARED ${MOC_FILES} src/task_rviz_plugin_panel.cpp src/loop_planning_thread.cpp)

target_include_directories(${PROJECT_NAME} PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include/${PROJECT_NAME}>
  ${Qt5Widgets_INCLUDE_DIRS}
  ${QWT_INCLUDE_DIRS}
  ${JSONCPP_INCLUDE_DIRS}
)

target_link_libraries(${PROJECT_NAME} PUBLIC ${QT_LIBRARIES} ${JSONCPP_LIBRARIES} ${QWT_LIBRARIES})

ament_target_dependencies(${PROJECT_NAME}
  PUBLIC
  rclcpp
  rviz_common
  std_msgs
  geometry_msgs
  nav_msgs
  chitu_msgs
  ackermann_msgs
  custom_msgs
  umodule_msgs
  state_msgs
)

install(
  TARGETS task_rviz_plugin
  EXPORT task_rviz_plugin
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)

pluginlib_export_plugin_description_file(rviz_common plugins_description.xml)

ament_package()
