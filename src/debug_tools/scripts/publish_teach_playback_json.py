#!/usr/bin/env python3

import json
import time

import rclpy

from std_msgs.msg import String

rclpy.init()
node = rclpy.create_node('publish_teach_playback_json')

cmd_publisher = node.create_publisher(String, '/uslam/task/planning', 1)

command_code = 0
record_id = ""

teach_playback_dict = {"title": "teach_playback", "command": command_code, "record_id": record_id}

while rclpy.ok():
    raw_command = input("Enter command(0-start, 1-stop, 2-load): ")
    if not raw_command.isdigit():
        node.get_logger().info('Invalid input, please enter a number between 0~2.')
        continue
    command_code = int(raw_command)
    if command_code == 0:
        record_id = input("Enter record_id: ")
        if not record_id:
            node.get_logger().warn('Empty target record_id, a auto-generated record_id will be used.')
        print('start teaching, now you can move the robot.')
    elif command_code == 1:
        record_id = ""
        print('stop teaching.')
    elif command_code == 2:
        record_id = input("Enter record_id: ")
        if not record_id:
            node.get_logger().error('A record_id is required for loading.')
            continue
        print('loading record_id: %s to planning module' % record_id)
    else:
        node.get_logger().error('Invalid command code.')
        continue

    teach_playback_dict["command"] = command_code
    teach_playback_dict["record_id"] = record_id
    msg = String()
    msg.data = json.dumps(teach_playback_dict)
    cmd_publisher.publish(msg)
    print('Successfully published teach_playback command: %d' % command_code)
    time.sleep(1)

rclpy.shutdown()