#!/usr/bin/env python3
"""
teach_playback client
"""
import rclpy
from rclpy.node import Node
from custom_msgs.srv import TeachPlaybackService

rclpy.init()
node = rclpy.create_node('teach_playback_client')
teach_playback_client = node.create_client(TeachPlaybackService, "/uslam/teach_playback")

while not teach_playback_client.wait_for_service(timeout_sec=1.0):
    node.get_logger().error("waiting for teach_playback service")

request = TeachPlaybackService.Request()
request.command = 0
request.record_id = "test"

try:
    future = teach_playback_client.call_async(request)
    rclpy.spin_until_future_complete(node, future)
    response = future.result()
    if response.success:
        node.get_logger().info("teach playback success")
    else:
        node.get_logger().error("teach playback failed: %s" % response.reason)
except rclpy.ServiceException as e:
    node.get_logger().error(e)
    node.get_logger().error("teach playback service call failed")

rclpy.shutdown()