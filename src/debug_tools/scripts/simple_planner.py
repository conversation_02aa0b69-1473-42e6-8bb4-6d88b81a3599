#!/usr/bin/env python3

import math
import os
from datetime import datetime
import bisect

import rclpy
from rclpy.node import Node

from rclpy.executors import MultiThreadedExecutor
from rclpy.callback_groups import MutuallyExclusiveCallbackGroup

from tf2_ros import TransformException
from tf2_ros.buffer import Buffer
from tf2_ros.transform_listener import TransformListener

import angles

from custom_msgs.msg import Trajectory, TrajectoryPoint
from nav_msgs.msg import Path
from geometry_msgs.msg import PoseStamped

def quaternion_from_euler(roll, pitch, yaw):
    qx = math.sin(roll/2) * math.cos(pitch/2) * math.cos(yaw/2) - math.cos(roll/2) * math.sin(pitch/2) * math.sin(yaw/2)
    qy = math.cos(roll/2) * math.sin(pitch/2) * math.cos(yaw/2) + math.sin(roll/2) * math.cos(pitch/2) * math.sin(yaw/2)
    qz = math.cos(roll/2) * math.cos(pitch/2) * math.sin(yaw/2) - math.sin(roll/2) * math.sin(pitch/2) * math.cos(yaw/2)
    qw = math.cos(roll/2) * math.cos(pitch/2) * math.cos(yaw/2) + math.sin(roll/2) * math.sin(pitch/2) * math.sin(yaw/2)
   
    return [qx, qy, qz, qw]

def euler_from_quaternion(quaternion):
        """
        Converts quaternion (w in last place) to euler roll, pitch, yaw
        quaternion = [x, y, z, w]
        """
        x = quaternion.x
        y = quaternion.y
        z = quaternion.z
        w = quaternion.w
    
        sinr_cosp = 2 * (w * x + y * z)
        cosr_cosp = 1 - 2 * (x * x + y * y)
        roll = math.atan2(sinr_cosp, cosr_cosp)
    
        sinp = 2 * (w * y - z * x)
        pitch = math.asin(max(-1, min(1, sinp)))
    
        siny_cosp = 2 * (w * z + x * y)
        cosy_cosp = 1 - 2 * (y * y + z * z)
        yaw = math.atan2(siny_cosp, cosy_cosp)
    
        return roll, pitch, yaw

def angle_lerp(start_quat, end_quat, ratio):
    start_heading = angles.normalize_angle(euler_from_quaternion(start_quat)[2])
    end_heading = angles.normalize_angle(euler_from_quaternion(end_quat)[2])
    angle_diff_shortest = angles.shortest_angular_distance(start_heading, end_heading)
    result_angle = angles.normalize_angle(start_heading + ratio * angle_diff_shortest)
    return quaternion_from_euler(0.0, 0.0, result_angle)

def clamp(min, max, input):
    if input < min:
        return min
    elif input > max:
        return max
    else:
        return input
    
def to_path(traj_msg):
    output = Path()
    output.header = traj_msg.header
    for traj_point in traj_msg.points:
        pose_point = PoseStamped()
        pose_point.header = traj_msg.header
        pose_point.pose = traj_point.pose
        output.poses.append(pose_point)
    
    return output

class SimplePlanner(Node):
    def __init__(self):
        super().__init__('test_traj_builder')

        self.target_vel = 2.0
        self.target_acc = 0.5
        self.target_dec = -0.5

        self.time_step = 0.02

        self.is_ref_line_valid = False

        self.planning_frame_id = "map"
        self.child_frame_id = "base_link"

        self.tf_buffer = Buffer()
        self.tf_listener = TransformListener(self.tf_buffer, self)

        self.planning_traj_pub = self.create_publisher(Trajectory, '/planning/trajectory/test', 1)
        self.reference_line_pub = self.create_publisher(Trajectory, '/debug/reference_line/trajectory/test', 1)
        self.planning_debug_pub = self.create_publisher(Path, '/debug/planning/trajectory/test', 1)
        self.ref_line_debug = self.create_publisher(Path, '/debug/reference_line/test', 1)

        timer_and_sub_callback_group = MutuallyExclusiveCallbackGroup()
        #sub_callback_group = MutuallyExclusiveCallbackGroup()

        self.reference_line_sub = self.create_subscription(Trajectory, '/debug/reference_line/trajectory', \
                                                           self.reference_line_callback, 1, callback_group=timer_and_sub_callback_group)
        
        publish_period = 0.1
        self.timer = self.create_timer(publish_period, self.timer_callback, callback_group=timer_and_sub_callback_group)

        self.planning_pub = self.create_publisher(Trajectory, '/planning/trajectory/test', 1)

    def timer_callback(self):
        if not self.is_ref_line_valid:
            return
        
        current_time = self.get_clock().now()
        
        try:
            t = self.tf_buffer.lookup_transform(
                self.planning_frame_id,
                self.child_frame_id,
                rclpy.time.Time())
        except TransformException as ex:
            self.get_logger().info(
                f'Could not transform {self.child_frame_id} to {self.planning_frame_id}: {ex}')
            return
        self.current_pose_x = t.transform.translation.x
        self.current_pose_y = t.transform.translation.y

        traj_base_time = self.planning_traj.header.stamp.sec + self.planning_traj.header.stamp.nanosec * 1.0e-9

        min_distance_idx = self.query_matched_point_by_position(self.planning_traj.points, self.current_pose_x, self.current_pose_y)

        remain_s = self.planning_traj_s[-1] - self.planning_traj_s[min_distance_idx]
        if remain_s < 0.05:
            self.get_logger().info("Closing end point, stop")
            self.is_ref_line_valid = False
            return

        backward_relative_s = 1.0
        forward_relative_s = 10.0
        forward_duration = 5.0
        matched_start_idx_by_s = bisect.bisect_right(self.planning_traj_s, self.planning_traj_s[min_distance_idx] - backward_relative_s)
        matched_start_idx_by_t = bisect.bisect_right(self.planning_traj.points, current_time.nanoseconds * 1.0e-9 - traj_base_time, key=lambda x: x.time_from_start)
        start_idx = min(matched_start_idx_by_s, matched_start_idx_by_t)
        start_idx = clamp(0, len(self.planning_traj.points) - 1, start_idx)

        matched_end_idx_by_s = bisect.bisect_right(self.planning_traj_s, self.planning_traj_s[min_distance_idx] + forward_relative_s)
        matched_end_idx_by_t_and_forward_duration = bisect.bisect_right(self.planning_traj.points, current_time.nanoseconds * 1.0e-9 - traj_base_time + forward_duration, key=lambda x: x.time_from_start)
        end_idx = max(matched_end_idx_by_s, matched_end_idx_by_t_and_forward_duration)
        end_idx = clamp(start_idx, len(self.planning_traj.points) - 1, end_idx)

        self.get_logger().info("min_distance_idx: %d, matched_s: %.3f" % (min_distance_idx, self.planning_traj_s[min_distance_idx]))
        self.get_logger().info("matched_start_idx_by_s: %d, matched_start_idx_by_t: %d, matched_end_idx_by_s: %d, matched_end_idx_by_t_and_forward_duration: %d" % 
                             (matched_start_idx_by_s, matched_start_idx_by_t, matched_end_idx_by_s, matched_end_idx_by_t_and_forward_duration))

        publish_traj = Trajectory()
        publish_traj.header = self.planning_traj.header
        publish_traj.points = self.planning_traj.points[start_idx : end_idx + 1]
        self.planning_traj_pub.publish(publish_traj)
        self.planning_debug_pub.publish(to_path(publish_traj))

        #self.get_logger().info("Timer callback")
        # 1. Check planning trajectory is ready
        # 2. Get current pose from tf
        # 3. Cut planning trajectory according to current pose, and publish it

    def reference_line_callback(self, msg):
        self.get_logger().info("Received reference line message")
        self.is_ref_line_valid = False
        try:
            t = self.tf_buffer.lookup_transform(
                self.planning_frame_id,
                self.child_frame_id,
                rclpy.time.Time())
        except TransformException as ex:
            self.get_logger().info(
                f'Could not transform {self.child_frame_id} to {self.planning_frame_id}: {ex}')
            return
        self.current_pose_x = t.transform.translation.x
        self.current_pose_y = t.transform.translation.y

        if len(msg.points) == 0:
            self.get_logger().warn("Received empty reference line message")
            return
        
        min_distance_idx = self.query_matched_point_by_position(msg.points, self.current_pose_x, self.current_pose_y)

        del msg.points[:min_distance_idx]

        self.reference_line_pub.publish(msg)
        self.ref_line_debug.publish(to_path(msg))

        self.ref_line_w = self.TrajectoryMsgWrapper(msg)
        self.planning_traj = Trajectory()
        self.planning_traj_s = []
        self.planning_traj.header.frame_id = msg.header.frame_id
        self.planning_traj.header.stamp = self.get_clock().now().to_msg()

        if not self.build_trapezoid_velocity_profile():
            self.get_logger().error("Failed to build trapezoid velocity profile")
            self.is_ref_line_valid = False
            return
        self.combine_path_and_velocity_profile()

        planning_traj_debug = self.TrajectoryMsgWrapper(self.planning_traj, derive_s=False)
        planning_traj_debug.set_s(self.planning_traj_s)
        planning_traj_debug.export_csv()

        self.is_ref_line_valid = True

    def query_matched_point_by_position(self, points, x, y):
        min_distance_idx = 0
        min_distance = math.hypot(points[min_distance_idx].pose.position.x - x, \
                                  points[min_distance_idx].pose.position.y - y)
        current_idx = 0
        for point in points:
            distance = math.hypot(point.pose.position.x - x, \
                                  point.pose.position.y - y)
            if distance < min_distance:
                min_distance = distance
                min_distance_idx = current_idx
            
            current_idx += 1
            
        return min_distance_idx
    
    def build_trapezoid_velocity_profile(self):
        total_length = self.ref_line_w.accumulated_s[-1] - self.ref_line_w.accumulated_s[0]
        self.vel_profile = self.TrapezoidVelocityProfile(self.target_vel, self.target_acc, self.target_dec, total_length)
        return self.vel_profile.valid_profile
    
    def combine_path_and_velocity_profile(self):
        self.planning_traj.points.clear()
        self.planning_traj_s.clear()
        query_time = 0.0
        while query_time <= self.vel_profile.total_duration:
            traj_point = TrajectoryPoint()
            vel_point = self.vel_profile.query(query_time)
            if vel_point is None:
                self.get_logger().error("Query velocity profile failed")
            else:
                traj_point.time_from_start = vel_point[0]
                traj_point.longitudinal_velocity_mps = vel_point[2]
                traj_point.acceleration_mps2 = vel_point[3]
                matched_idx_by_s = bisect.bisect_right(self.ref_line_w.accumulated_s, vel_point[1])
                if matched_idx_by_s == 0:
                    traj_point.pose = self.ref_line_w.msg.points[0].pose
                elif matched_idx_by_s == len(self.ref_line_w.accumulated_s):
                    traj_point.pose = self.ref_line_w.msg.points[-1].pose
                else:
                    matched_segment_start_idx = matched_idx_by_s - 1
                    matched_segment_end_idx = matched_idx_by_s
                    matched_segment_start_s = self.ref_line_w.accumulated_s[matched_segment_start_idx]
                    matched_segment_end_s = self.ref_line_w.accumulated_s[matched_segment_end_idx]
                    s_diff = matched_segment_end_s - matched_segment_start_s
                    if s_diff < 1.0e-6:
                        traj_point.pose = self.ref_line_w.msg.points[matched_segment_start_idx].pose
                    else:
                        s_ratio = (vel_point[1] - matched_segment_start_s) / s_diff
                        matched_segment_start_pose = self.ref_line_w.msg.points[matched_segment_start_idx].pose
                        matched_segment_end_pose = self.ref_line_w.msg.points[matched_segment_end_idx].pose
                        traj_point.pose.position.x = matched_segment_start_pose.position.x + s_ratio * (matched_segment_end_pose.position.x - matched_segment_start_pose.position.x)
                        traj_point.pose.position.y = matched_segment_start_pose.position.y + s_ratio * (matched_segment_end_pose.position.y - matched_segment_start_pose.position.y)
                        quat = angle_lerp(matched_segment_start_pose.orientation, matched_segment_end_pose.orientation, s_ratio)
                        traj_point.pose.orientation.x = quat[0]
                        traj_point.pose.orientation.y = quat[1]
                        traj_point.pose.orientation.z = quat[2]
                        traj_point.pose.orientation.w = quat[3]
            
            self.planning_traj.points.append(traj_point)
            self.planning_traj_s.append(vel_point[1])
            query_time += self.time_step
    
    class TrapezoidVelocityProfile:
        def __init__(self, target_v, target_a, target_da, total_length) -> None:
            self.valid_profile = True
            
            target_da = math.fabs(target_da)
            slop_length = 0.5 * target_a * math.pow(target_v / target_a, 2) + \
                                 0.5 * target_da * math.pow(target_v / target_da, 2)
            self.total_duration = 0.0
            if total_length > slop_length:
                
                rising_duration = target_v / target_a
                rising_length = 0.5 * target_a * math.pow(rising_duration, 2)
                rising_start_time = 0.0
                rising_end_time = rising_start_time + rising_duration
                rising_start_s = 0.0
                rising_end_s = rising_start_s + rising_length
                rising_start_v = 0.0
                rising_end_v = target_v
                rising_start_a = target_a
                rising_end_a = target_a
                self.rising_segment = {"start": {"t": rising_start_time, "s": rising_start_s, "v": rising_start_v, "a": rising_start_a}, \
                                       "end": {"t": rising_end_time, "s": rising_end_s, "v": rising_end_v, "a": rising_end_a}}
                
                steady_duration = (total_length - slop_length) / target_v
                steady_length = total_length - slop_length
                steady_start_time = rising_end_time
                steady_end_time = steady_start_time + steady_duration
                steady_start_s = rising_end_s
                steady_end_s = steady_start_s + steady_length
                steady_start_v = target_v
                steady_end_v = target_v
                steady_start_a = 0.0
                steady_end_a = 0.0
                self.steady_segment = {"start": {"t": steady_start_time, "s": steady_start_s, "v": steady_start_v, "a": steady_start_a}, \
                                       "end": {"t": steady_end_time, "s": steady_end_s, "v": steady_end_v, "a": steady_end_a}}
                
                falling_duration = target_v / target_da
                falling_length = 0.5 * target_da * math.pow(falling_duration, 2)
                falling_start_time = steady_end_time
                falling_end_time = falling_start_time + falling_duration
                falling_start_s = steady_end_s
                falling_end_s = falling_start_s + falling_length
                falling_start_v = target_v
                falling_end_v = 0.0
                falling_start_a = -target_da
                falling_end_a = -target_da
                self.falling_segment = {"start": {"t": falling_start_time, "s": falling_start_s, "v": falling_start_v, "a": falling_start_a}, \
                                        "end": {"t": falling_end_time, "s": falling_end_s, "v": falling_end_v, "a": falling_end_a}}
                
                self.total_duration = falling_end_time
            else:
                self.valid_profile = False
        
        def quadratic_polynomial(self, coefs, t):
            """
            coefs = (c0, c1, c2)
            s = c0 + c1 * t + c2 * t^2
            v = c1 + 2 * c2 * t
            a = 2 * c2
            """
            return (coefs[0] + coefs[1] * t + coefs[2] * t * t, coefs[1] + 2.0 * coefs[2] * t, 2 * coefs[2])
        
        def query(self, t):
            """
            c0 = s_begin;
            c1 = v0;
            c2 = signed_acc / 2;
            """
            if not self.valid_profile:
                return None

            if t >= self.rising_segment["start"]["t"] and t < self.rising_segment["end"]["t"]:
                segment = self.rising_segment
            elif t >= self.steady_segment["start"]["t"] and t < self.steady_segment["end"]["t"]:
                segment = self.steady_segment
            elif t >= self.falling_segment["start"]["t"] and t <= self.falling_segment["end"]["t"]:
                segment = self.falling_segment
            else:
                return None
            
            segment_coefs = (segment["start"]["s"], segment["start"]["v"], segment["start"]["a"] / 2.0)
            query_t = t
            query_s, query_v, query_a = self.quadratic_polynomial(segment_coefs, t - segment["start"]["t"])

            return query_t, query_s, query_v, query_a
    

    class TrajectoryMsgWrapper:
        def __init__(self, msg, derive_s=True):
            self.msg = msg
            if derive_s and len(msg.points) != 0:
                self.accumulated_s = []
                prev_point = msg.points[0]
                for traj_point in msg.points:
                    ds = math.hypot(traj_point.pose.position.x - prev_point.pose.position.x, traj_point.pose.position.y - prev_point.pose.position.y)
                    if len(self.accumulated_s) == 0:
                        self.accumulated_s.append(0.0)
                    else:
                        prev_s = self.accumulated_s[-1]
                        self.accumulated_s.append(prev_s + ds)
                    prev_point = traj_point

        def set_s(self, s_list):
            self.accumulated_s = s_list
        
        def export_csv(self):
            csv_file = os.getenv("HOME") + "/uautopilot/debug_data/" + \
                       datetime.now().isoformat().replace(':', '-') + \
                       "_length_" + str(self.accumulated_s[-1]) + ".csv"
            try:
                csv_handle = open(csv_file, 'w')
                header = "timestamp,relative_time,x,y,heading,v,s\n"
                csv_handle.write(header)
                base_time = self.msg.header.stamp.sec + self.msg.header.stamp.nanosec / 1.0e9
                idx = 0
                for point in self.msg.points:
                    point_item = ("%.3f,%.3f,%.3f,%.3f,%.3f,%.3f,%.3f\n" % \
                                  (base_time + point.time_from_start, \
                                   point.time_from_start, \
                                   point.pose.position.x, \
                                   point.pose.position.y, \
                                   euler_from_quaternion(point.pose.orientation)[2],
                                   point.longitudinal_velocity_mps,
                                   self.accumulated_s[idx]))
                    csv_handle.write(point_item)
                    idx += 1
                csv_handle.flush()
            except IOError:
                print("Open file %s failed" % (csv_file))
                self.file_handler.close()

if __name__ == '__main__':
    rclpy.init()
    executor = MultiThreadedExecutor()
    test_traj_builder = SimplePlanner()
    executor.add_node(test_traj_builder)

    try:
        executor.spin()
    except KeyboardInterrupt:
        test_traj_builder.get_logger().info("Keyboard interrupt, shutting down")
    
    test_traj_builder.destroy_node()
    rclpy.try_shutdown()