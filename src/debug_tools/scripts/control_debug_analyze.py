#!/usr/bin/env python3

"""
Record chassis pose
"""

from cmath import sqrt
import math
import os
import sys
from datetime import datetime

import rclpy
from rclpy.node import Node

from rclpy.executors import MultiThreadedExecutor
from rclpy.callback_groups import MutuallyExclusiveCallbackGroup

from custom_msgs.msg import ControlDebug

class DebugRecord(Node):
    """DebugRecord class"""    
    def write(self, data):
        """Wrap file write function to flush data to disk"""
        self.file_handler.write(data)
        self.file_handler.flush()
    def __init__(self):
        super().__init__('navi_debug_record')

        self.declare_parameter("mission_id", "default_id")
        self.mission_id = self.get_parameter("mission_id").value
        self.get_logger().info("mission_id: %s" % (self.mission_id))

        self.csv_header = "timestamp,control_time,current_pose_x,current_pose_y,current_pose_yaw," + \
                          "matched_pose_error_x,matched_pose_error_y,matched_pose_error_yaw," + \
                          "reference_pose_error_x,reference_pose_error_y,reference_pose_error_yaw," + \
                          "vel_x,vel_y,steering_angle," + \
                          "cmd_vel_x,cmd_steering_angle," + \
                          "matched_point_x,matched_point_y,matched_point_yaw,matched_point_s,matched_point_v," + \
                          "reference_point_x,reference_point_y,reference_point_yaw,reference_point_s,reference_point_v," +\
                          "ref_line_base_link_error_x,ref_line_base_link_error_y,ref_line_base_link_error_yaw," + \
                          "ref_line_cog_error_x,ref_line_cog_error_y,ref_line_cog_error_yaw," + \
                          "ref_line_front_axis_error_x,ref_line_front_axis_error_y,ref_line_front_axis_error_yaw\n"

        timer_callback_group = MutuallyExclusiveCallbackGroup()
        sub_callback_group = MutuallyExclusiveCallbackGroup()
        
        self.navi_debug_sub = self.create_subscription(ControlDebug, '/control/debug', \
                                                       self.control_debug_callback, 1, callback_group=sub_callback_group)
        
        timer_period = 1.0
        self.timer = self.create_timer(timer_period, self.timer_callback, callback_group=timer_callback_group)
        
        self.ackermann_motor_flag = False
        self.terminating = False
        self.is_recording = False
        self.last_finish_recording_time = self.get_clock().now()
        self.last_navi_time = rclpy.time.Time()

        self.record_file_path = os.getenv("HOME") + "/uautopilot/debug_data/"
        if not os.path.exists(self.record_file_path):
            os.makedirs(self.record_file_path)
    
    def timer_callback(self):
        """
        timer
        """
        if self.is_recording:
            current_navi_time = self.get_clock().now()
            if current_navi_time - self.last_navi_time > rclpy.duration.Duration(seconds=2.0):
                self.get_logger().warn("Time out receiving message, stop recording to file")
                self.get_logger().warn("File is written into %s" % self.record_file)
                self.file_handler.flush()
                self.file_handler.close()
                self.is_recording = False

    def control_debug_callback(self, data):
        """
        New message received
        """
        if self.terminating is True:
            self.get_logger().info("terminating when receive navi_debug msg")
            return

        if not self.is_recording:
            if self.get_clock().now() - self.last_finish_recording_time < rclpy.duration.Duration(seconds=1.0):
                self.get_logger().warn("Wait for last mission getting finished")
                return
            self.record_file = self.record_file_path +  \
                               datetime.now().isoformat().replace(':', '-') + \
                               '_of_' + self.mission_id + '.csv'
            self.get_logger().info("Record file to: " + self.record_file)
            try:
                self.file_handler = open(self.record_file, 'w')
            except IOError:
                self.get_logger().error("Open file %s failed" % (self.record_file))
                self.file_handler.close()
                sys.exit(1)
            
            self.write(self.csv_header)
            self.is_recording = True
        
        self.last_navi_time = self.get_clock().now()
        
        format_item = "%f,%f,%f,%f,%f,%f,%f,%f,%f,%f," + \
                      "%f,%f,%f,%f,%f,%f,%f,%f,%f,%f," + \
                      "%f,%f,%f,%f,%f,%f,%f,%f,%f,%f," + \
                      "%f,%f,%f,%f,%f\n"
        time_stamp = data.header.stamp.sec + data.header.stamp.nanosec / 1.0e9
        self.write(format_item % \
                   (time_stamp, \
                    data.control_time, \
                    data.current_pose.position.x, \
                    data.current_pose.position.y, \
                    math.degrees(self.euler_from_quaternion(data.current_pose.orientation)[2]), \
                    data.matched_pose_error.position.x, \
                    data.matched_pose_error.position.y, \
                    math.degrees(self.euler_from_quaternion(data.matched_pose_error.orientation)[2]), \
                    data.reference_pose_error.position.x, \
                    data.reference_pose_error.position.y, \
                    math.degrees(self.euler_from_quaternion(data.reference_pose_error.orientation)[2]), \
                    data.velocity_in_base_link.linear.x, \
                    data.velocity_in_base_link.linear.y, \
                    math.degrees(data.velocity_in_base_link.angular.z), \
                    data.cmd_vel.linear.x, \
                    math.degrees(data.cmd_vel.angular.z), \
                    data.matched_point.pose.position.x, \
                    data.matched_point.pose.position.y, \
                    math.degrees(self.euler_from_quaternion(data.matched_point.pose.orientation)[2]), \
                    0.0, \
                    data.matched_point.longitudinal_velocity_mps, \
                    data.reference_point.pose.position.x, \
                    data.reference_point.pose.position.y, \
                    math.degrees(self.euler_from_quaternion(data.reference_point.pose.orientation)[2]), \
                    0.0, \
                    data.reference_point.longitudinal_velocity_mps,
                    data.reference_line_base_link_matched_error.position.x,
                    data.reference_line_base_link_matched_error.position.y,
                    math.degrees(self.euler_from_quaternion(data.reference_line_base_link_matched_error.orientation)[2]),
                    data.reference_line_gravity_center_matched_error.position.x,
                    data.reference_line_gravity_center_matched_error.position.y,
                    math.degrees(self.euler_from_quaternion(data.reference_line_gravity_center_matched_error.orientation)[2]),
                    data.reference_line_front_axis_center_matched_error.position.x,
                    data.reference_line_front_axis_center_matched_error.position.y,
                    math.degrees(self.euler_from_quaternion(data.reference_line_front_axis_center_matched_error.orientation)[2])
                    ))

    def euler_from_quaternion(self, quaternion):
        """
        Converts quaternion (w in last place) to euler roll, pitch, yaw
        quaternion = [x, y, z, w]
        """
        x = quaternion.x
        y = quaternion.y
        z = quaternion.z
        w = quaternion.w
    
        sinr_cosp = 2 * (w * x + y * z)
        cosr_cosp = 1 - 2 * (x * x + y * y)
        roll = math.atan2(sinr_cosp, cosr_cosp)
    
        sinp = 2 * (w * y - z * x)
        pitch = math.asin(max(-1, min(1, sinp)))
    
        siny_cosp = 2 * (w * z + x * y)
        cosy_cosp = 1 - 2 * (y * y + z * z)
        yaw = math.atan2(siny_cosp, cosy_cosp)
    
        return roll, pitch, yaw
    
    def shutdown(self):
        """
        shutdown node
        """
        self.terminating = True
        self.get_logger().info("Shutting Down...")
        self.file_handler.flush()
        self.file_handler.close()

if __name__ == '__main__':
    rclpy.init()
    executor = MultiThreadedExecutor()
    record = DebugRecord()
    executor.add_node(record)

    try:
        executor.spin()
    except KeyboardInterrupt:
        record.get_logger().info("Keyboard interrupt, shutting Down...")
    record.destroy_node()
    rclpy.try_shutdown()