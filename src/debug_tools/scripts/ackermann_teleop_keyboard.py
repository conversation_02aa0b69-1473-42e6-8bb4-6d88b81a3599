#!/usr/bin/env python3
import rclpy
from rclpy.qos import qos_profile_system_default

from ackermann_msgs.msg import AckermannDriveStamped

import sys, select, termios, tty, math

settings = termios.tcgetattr(sys.stdin)

msg = """
Reading from the keyboard  and Publishing to AckermannDriveStamped!
---------------------------
Moving around:
i : move forward
k : move backward
u : steer left + move forward
o : steer right + move forward
j : steer left + move backward
l : steer right + move backward

anything else : stop

w/x : increase/decrease only linear speed by 10%
e/c : increase/decrease only steering angle by 10%

CTRL-C to quit
"""

moveBindings = {
        'i':(1,0,0,0),
        'k':(-1,0,0,0),
        'u':(1,0,0,1),
        'o':(1,0,0,-1),
        'j':(-1,0,0,1),
        'l':(-1,0,0,-1)
          }

speedBindings={
        'w':(1.1,1),
        'x':(.9,1),
        'e':(1,1.1),
        'c':(1,.9)
          }

def getKey():
    tty.setraw(sys.stdin.fileno())
    select.select([sys.stdin], [], [], 0)
    key = sys.stdin.read(1)
    termios.tcsetattr(sys.stdin, termios.TCSADRAIN, settings)
    return key


def vels(speed,turn):
    return "currently:\tspeed %s\tturn %s " % (speed,turn)

def main(args=None):	
    #if args is None:
    #    args = sys.argv

    #rclpy.init(args)
    rclpy.init()
    node = rclpy.create_node('ackermann_teleop_keyboard')
		
    pub = node.create_publisher(AckermannDriveStamped, '/uslam/control/ackermann_cmd_vel', 1)

    speed = 0.5
    turn = 0.5
    x = 0
    th = 0
    status = 0
    MAX_SPEED = 2.0
    MAX_STEERING_ANGLE = 0.52359

    try:
        print(msg)
        print(vels(speed,turn))
        while(1):
            key = getKey()
            if key in moveBindings.keys():
                x = moveBindings[key][0]
                th = moveBindings[key][3]
            elif key in speedBindings.keys():
                speed = speed * speedBindings[key][0]
                turn = turn * speedBindings[key][1]
                if abs(speed) > MAX_SPEED:
                    speed = math.copysign(MAX_SPEED, speed)
                    print("reach max speed: %f" % MAX_SPEED)
                if abs(turn) > MAX_STEERING_ANGLE:
                    turn = math.copysign(MAX_STEERING_ANGLE, turn)
                    print("reach max steering angle: %f" % MAX_STEERING_ANGLE)
                print(vels(speed,turn))
                if (status == 14):
                    print(msg)
                status = (status + 1) % 15
            else:
                x = 0
                th = 0
                if (key == '\x03'):
                    break    
            twist = AckermannDriveStamped()
            twist.drive.speed = x*speed
            twist.drive.steering_angle = th*turn
            pub.publish(twist)    
    except:
        print(e)    
    finally:
        twist = AckermannDriveStamped()
        twist.drive.speed = 0.0
        twist.drive.steering_angle = 0.0
        pub.publish(twist)    
        termios.tcsetattr(sys.stdin, termios.TCSADRAIN, settings)

if __name__ == '__main__':
    main()