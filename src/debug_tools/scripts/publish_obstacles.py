#!/usr/bin/env python3

import time

import rclpy

from custom_msgs.msg import PerceptionObject, PerceptionObjects
from custom_msgs.msg import ObjectClassification
from geometry_msgs.msg import PolygonStamped, Point32

rclpy.init()
node = rclpy.create_node('publish_obstacles')

obstacles_publisher = node.create_publisher(PerceptionObjects, '/uslam/perception/perception_objects', 1)
obs_rviz_publisher = node.create_publisher(PolygonStamped, '/uslam/perception/obstacles_rviz', 1)

def publish_obstacles():
    fake_obstacle = PerceptionObject()
    fake_obstacle.id = 1
    fake_obstacle.pose.position.x = -126.0
    fake_obstacle.pose.position.y = 127.0
    fake_obstacle.pose.position.z = -1.565
    fake_obstacle.pose.orientation.x = 0.0
    fake_obstacle.pose.orientation.y = -0.001
    fake_obstacle.pose.orientation.z = 0.976
    fake_obstacle.pose.orientation.w = -0.219
    fake_obstacle.size.x = 2.0
    fake_obstacle.size.y = 2.0
    fake_obstacle.size.z = 2.0
    fake_obstacle.oc.probability = 1.0
    fake_obstacle.oc.classification = ObjectClassification.CAR

    obstacles_msg = PerceptionObjects()
    obstacles_msg.header.frame_id = "map"
    obstacles_msg.header.stamp = node.get_clock().now().to_msg()
    obstacles_msg.objects.append(fake_obstacle)

    obs_rviz_msg = PolygonStamped()
    obs_rviz_msg.header = obstacles_msg.header
    front_left = Point32()
    front_left.x = fake_obstacle.pose.position.x + fake_obstacle.size.x / 2
    front_left.y = fake_obstacle.pose.position.y + fake_obstacle.size.y / 2
    front_right = Point32()
    front_right.x = fake_obstacle.pose.position.x + fake_obstacle.size.x / 2
    front_right.y = fake_obstacle.pose.position.y - fake_obstacle.size.y / 2
    rear_right = Point32()
    rear_right.x = fake_obstacle.pose.position.x - fake_obstacle.size.x / 2
    rear_right.y = fake_obstacle.pose.position.y - fake_obstacle.size.y / 2
    rear_left = Point32()
    rear_left.x = fake_obstacle.pose.position.x - fake_obstacle.size.x / 2
    rear_left.y = fake_obstacle.pose.position.y + fake_obstacle.size.y / 2
    obs_rviz_msg.polygon.points = [front_left, front_right, rear_right, rear_left]

    obstacles_publisher.publish(obstacles_msg)
    obs_rviz_publisher.publish(obs_rviz_msg)

pub_timer = node.create_timer(1, publish_obstacles)

rclpy.spin(node)

rclpy.shutdown()
