<?xml version="1.0"?>
<package format="3">
  <name>debug_tools</name>
  <version>0.0.0</version>
  <description>scripts for debugging</description>

  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>TODO</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <!--Refer to https://roboticsbackend.com/ros2-package-for-both-python-and-cpp-nodes/-->
  <buildtool_depend>ament_cmake_python</buildtool_depend>
  <depend>rclpy</depend>
  
  <!-- The export tag contains other, unspecified, tags -->
  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
