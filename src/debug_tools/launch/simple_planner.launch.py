import launch
import launch.actions
import launch_ros.actions
from launch.substitutions import LaunchConfiguration

def generate_launch_description():
    ld = launch.LaunchDescription()

    in_sim_arg = launch.actions.DeclareLaunchArgument(
        'in_sim',
        default_value='False',
    )

    node = launch_ros.actions.Node(
        package='debug_tools',
        executable='simple_planner.py',
        name='simple_planner',
        parameters=[
            {'use_sim_time': LaunchConfiguration('in_sim')}
        ]
    )

    ld.add_action(in_sim_arg)
    ld.add_action(node)

    return ld