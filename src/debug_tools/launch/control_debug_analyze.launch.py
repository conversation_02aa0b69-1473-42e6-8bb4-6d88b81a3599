import launch
import launch.actions
import launch_ros.actions
from launch.substitutions import LaunchConfiguration

def generate_launch_description():
    ld = launch.LaunchDescription()

    mission_id_arg = launch.actions.DeclareLaunchArgument(
        'mission_id',
        default_value='default_id',
    )

    node = launch_ros.actions.Node(
        package='debug_tools',
        executable='control_debug_analyze.py',
        name='control_debug_analyze',
        parameters=[
            {'mission_id': LaunchConfiguration('mission_id')}
        ]
    )

    ld.add_action(mission_id_arg)
    ld.add_action(node)

    return ld