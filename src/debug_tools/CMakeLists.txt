cmake_minimum_required(VERSION 3.5)
project(debug_tools)

## Default to C++17
#if(NOT CMAKE_CXX_STANDARD)
#  set(CMAKE_CXX_STANDARD 17)
#endif()
#
#if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
#  add_compile_options(-Wall -Wextra -Wpedantic)
#endif()
#
find_package(ament_cmake REQUIRED)

find_package(ament_cmake_python REQUIRED)
find_package(rclpy REQUIRED)

ament_python_install_package(${PROJECT_NAME})

install(PROGRAMS
  scripts/control_debug_analyze.py
  scripts/ackermann_teleop_keyboard.py
  scripts/simple_planner.py
  scripts/publish_teach_playback_json.py
  scripts/teach_playback_client.py
  scripts/publish_obstacles.py
  DESTINATION lib/${PROJECT_NAME}
)

# Install launch files.
install(DIRECTORY
  launch
  DESTINATION share/${PROJECT_NAME}
)

ament_package()
