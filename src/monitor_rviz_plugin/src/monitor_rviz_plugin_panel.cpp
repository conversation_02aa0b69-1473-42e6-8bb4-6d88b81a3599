#include <stdio.h>
#include <sstream>
#include <QPainter>
#include <QLineEdit>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QTimer>
#include <QDebug>
#include "qwt_plot_curve.h"
#include <qwt_legend.h>
#include "monitor_rviz_plugin_panel.hpp"
#include <iostream>
#include <fstream>

namespace monitor_rviz_plugin {
typedef unsigned char uint8;

// 构造函数，初始化变量
MonitorRvizPanel::MonitorRvizPanel(QWidget *parent) : rviz_common::Panel(parent)
{
    ui_.setupUi(this);
}

void MonitorRvizPanel::onInitialize()
{
    auto ros_node_abstraction = (getDisplayContext()->getRosNodeAbstraction()).lock();
    rclcpp::Node::SharedPtr node = ros_node_abstraction->get_raw_node();

    rclcpp::QoS qos_profile(10);
    qos_profile.reliable().transient_local();
    //订阅monitor模块启动时使用的HMI工作模式
    sub_hmi_mode_ = node->template create_subscription<custom_msgs::msg::HmiMode>(
        "/uslam/monitor/hmi_mode", qos_profile, std::bind(&MonitorRvizPanel::hmiModeCallback, this, std::placeholders::_1));

    // monitor模块监控的状态
    sub_system_status_ = node->template create_subscription<custom_msgs::msg::SystemStatus>(
        "/uslam/monitor/system_status", 1, std::bind(&MonitorRvizPanel::systemStatusCallback, this, std::placeholders::_1));

    //模块状态更新频繁，单独发布统一话题 /uslam/module_state
    sub_module_state_ = node->template create_subscription<umodule_msgs::msg::ModuleState>(
        "/uslam/module_state", qos_profile, std::bind(&MonitorRvizPanel::moduleStateCallback, this, std::placeholders::_1));
}

void MonitorRvizPanel::systemStatusCallback(const custom_msgs::msg::SystemStatus::SharedPtr msg)
{
    updateTable(msg);
    updateQwtPlot(msg);
}

void MonitorRvizPanel::updateTable(const custom_msgs::msg::SystemStatus::SharedPtr msg)
{
    ui_.tableWidget->setRowCount(msg->monitored_modules.size());
    for (custom_msgs::msg::ModuleStatus module : msg->monitored_modules) {
        int row = module_name_row_map_[module.module_name];

        ui_.tableWidget->setItem(row, 0, new QTableWidgetItem(module.module_name.c_str()));
        ui_.tableWidget->item(row, 0)->setTextAlignment(Qt::AlignCenter);

        if (module.process_status.status == 1) {
            ui_.tableWidget->item(row, 0)->setBackground(Qt::green);
        } else {
            ui_.tableWidget->item(row, 0)->setBackground(Qt::red);
        }

        std::string channel_status;
        if (module.channel_status.status == custom_msgs::msg::StatusMessage::OK) {
            channel_status = status_str[module.channel_status.status];
        } else {
            channel_status = status_str[module.channel_status.status] + ":" + module.channel_status.message;
        }
        ui_.tableWidget->setItem(row, 2, new QTableWidgetItem(channel_status.c_str()));
        ui_.tableWidget->item(row, 2)->setTextAlignment(Qt::AlignCenter);

        std::string resource_status;
        if (module.resource_status.status == custom_msgs::msg::StatusMessage::OK) {
            resource_status = status_str[module.resource_status.status];
        } else {
            resource_status = status_str[module.resource_status.status] + ":" + module.resource_status.message;
        }
        ui_.tableWidget->setItem(row, 3, new QTableWidgetItem(resource_status.c_str()));
        ui_.tableWidget->item(row, 3)->setTextAlignment(Qt::AlignCenter);

        ui_.tableWidget->setItem(row, 4, new QTableWidgetItem(std::to_string(module.cpu_usage).c_str()));
        ui_.tableWidget->setItem(row, 5, new QTableWidgetItem(std::to_string(module.memory_usage).c_str()));
        ui_.tableWidget->item(row, 4)->setTextAlignment(Qt::AlignCenter);
        ui_.tableWidget->item(row, 5)->setTextAlignment(Qt::AlignCenter);
    }
}

void MonitorRvizPanel::updateQwtPlot(const custom_msgs::msg::SystemStatus::SharedPtr msg)
{
    int horizon_size = 30;

    module_cpu_resource_.resize(msg->monitored_modules.size());
    for (custom_msgs::msg::ModuleStatus module : msg->monitored_modules) {
        int row = module_name_row_map_[module.module_name];
        module_cpu_resource_[row].push_back(module.cpu_usage);
        if (module_cpu_resource_[row].size() > 2 * horizon_size) {
            module_cpu_resource_[row].erase(module_cpu_resource_[row].begin(),
                                            module_cpu_resource_[row].begin() + horizon_size);
        }
    }

    if (time_sequence_.empty()) {
        time_sequence_.push_back(0.0);
    } else {
        double next_time = time_sequence_.back() + 1.0;
        time_sequence_.push_back(next_time);
    }

    if (time_sequence_.size() > 2 * horizon_size) {
        time_sequence_.erase(time_sequence_.begin(), time_sequence_.begin() + horizon_size);
    }

    ui_.qwtPlot->detachItems();
    for (int i = 0; i < module_cpu_resource_.size(); i++) {
        //如果ROW_COLOR_MAP没有键值i, 直接使用ROW_COLOR_MAP.at(0)的颜色
        QColor color = ROW_COLOR_MAP.count(i) == 0 ? ROW_COLOR_MAP.at(0) : ROW_COLOR_MAP.at(i);
        QPen pen(color);
        pen.setWidth(2);
        QString module_name = ui_.tableWidget->item(i, 0)->text();
        QwtPlotCurve *curve = new QwtPlotCurve(module_name);  // create a curve
        curve->setPen(pen);
        if (time_sequence_.size() < horizon_size) {
            curve->setSamples(time_sequence_.data(), module_cpu_resource_[i].data(),
                              time_sequence_.size());  // set the data to the curve
        } else {
            curve->setSamples(time_sequence_.data() + time_sequence_.size() - horizon_size,
                              module_cpu_resource_[i].data() + module_cpu_resource_[i].size() - horizon_size,
                              horizon_size);  // set the data to the curve
        }
        curve->attach(ui_.qwtPlot);  // attach the curve to the plot
    }

    QwtLegend *legend = new QwtLegend;
    ui_.qwtPlot->insertLegend(legend, QwtPlot::RightLegend);
    ui_.qwtPlot->replot();  // finally redraw the plot
}

void MonitorRvizPanel::moduleStateCallback(const umodule_msgs::msg::ModuleState::SharedPtr msg)
{
    int row = module_name_row_map_[msg->header.frame_id];
    std::string state_desc;
    if (MOUDLE_STATE_DESC_MAP.count(msg->header.frame_id) != 0 &&
        MOUDLE_STATE_DESC_MAP.at(msg->header.frame_id).count(msg->state) != 0) {
        state_desc = MOUDLE_STATE_DESC_MAP.at(msg->header.frame_id).at(msg->state);
    } else {
        state_desc = std::to_string(msg->state);
    }

    QTableWidgetItem *table_item = new QTableWidgetItem(state_desc.c_str());
    table_item->setTextAlignment(Qt::AlignCenter);
    ui_.tableWidget->setItem(row, 1, table_item);
}

void MonitorRvizPanel::hmiModeCallback(const custom_msgs::msg::HmiMode::SharedPtr msg)
{
    std::cout << "hmiModeCallback" << std::endl;
    ui_.tableWidget->setRowCount(msg->modules.size());
    int row_count = 0;
    for (custom_msgs::msg::Module module : msg->modules) {
        std::cout << "module_name: " << module.module_name << std::endl;
        module_name_row_map_[module.module_name] = row_count;
        ui_.tableWidget->setItem(row_count, 0, new QTableWidgetItem(module.module_name.c_str()));
        row_count++;
    }
}

// 重载父类的功能
void MonitorRvizPanel::save(rviz_common::Config config) const
{
    rviz_common::Panel::save(config);
    // config.mapSetValue("Topic", output_topic_);
}

// 重载父类的功能，加载配置数据
void MonitorRvizPanel::load(const rviz_common::Config &config)
{
    rviz_common::Panel::load(config);
    // QString topic;
    // if (config.mapGetString("Topic", &topic)) {
    //     output_topic_editor_->setText(topic);
    //     updateTopic();
    // }
}

void MonitorRvizPanel::resizeEvent(QResizeEvent *event)
{
    // 获取窗口大小
    QSize windowSize = size();
    // 调用基类的 resizeEvent 函数
    rviz_common::Panel::resizeEvent(event);
    ui_.gridLayoutWidget->setGeometry(0, 0, windowSize.width(), windowSize.height());
}

}  // namespace monitor_rviz_plugin

// 声明此类是一个rviz的插件
#include <pluginlib/class_list_macros.hpp>  // NOLINT
PLUGINLIB_EXPORT_CLASS(monitor_rviz_plugin::MonitorRvizPanel, rviz_common::Panel)
