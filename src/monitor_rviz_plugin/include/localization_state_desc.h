#ifndef __LOCALIZATION_STATE_DESC_H__
#define __LOCALIZATION_STATE_DESC_H__

#include <state_msgs/msg/localization_state.hpp>

// clang-format off
const std::unordered_map<uint8_t, std::string> LOCALIZATION_STATE_DESC_MAP = 
{
    {state_msgs::msg::LocalizationState::LOCATE_UNINIT, "LocateUninit"}, 
    {state_msgs::msg::LocalizationState::LOCATE_READY, "LocateReady"},
    {state_msgs::msg::LocalizationState::LOCATE_RUNNING, "LocateRunning"}, 
    {state_msgs::msg::LocalizationState::LOCATE_LOST, "LocateLost"},
    {state_msgs::msg::LocalizationState::LOCATE_RECOVERING, "LocateRecovering"},
    {state_msgs::msg::LocalizationState::RELOCATE_UNINIT, "RelocateUninit"}, 
    {state_msgs::msg::LocalizationState::RELOCATE_READY, "RelocateReady"},
    {state_msgs::msg::LocalizationState::RELOCATE_RUNNING, "RelocateRunning"}, 
    {state_msgs::msg::LocalizationState::RELOCATE_LOST, "RelocateLost"},
    {state_msgs::msg::LocalizationState::RELOCATE_SUCCESS, "RelocateSuccess"}, 
    {state_msgs::msg::LocalizationState::RELOCATE_ABORT, "RelocateAbort"},
    {state_msgs::msg::LocalizationState::RELOCATE_FAILURE, "RelocateFailure"},
    {state_msgs::msg::LocalizationState::MAP_NOT_INIT, "MapNotInit"},
    {state_msgs::msg::LocalizationState::MAP_INITED, "MapInited"},
    {state_msgs::msg::LocalizationState::ODOM_LOST, "OdomLost"},
    {state_msgs::msg::LocalizationState::IMU_LOST, "ImuLost"},
    {state_msgs::msg::LocalizationState::POINTCLOUD_LOST, "PointcloudLost"}
};
// clang-format on

#endif  //__LOCALIZATION_STATE_DESC_H__