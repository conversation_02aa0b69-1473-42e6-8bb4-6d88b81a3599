#ifndef __MODULE_STATE_DESC_MAP_H__
#define __MODULE_STATE_DESC_MAP_H__

#include "localization_state_desc.h"
#include "planning_state_desc.h"

// clang-format off
const std::unordered_map<std::string, std::unordered_map<uint8_t, std::string>> MOUDLE_STATE_DESC_MAP=
{
    {"localization_manager", LOCALIZATION_STATE_DESC_MAP},
    {"planning_manager", PLANNING_STATE_DESC_MAP}
};
// clang-format on

#endif  //__MODULE_STATE_DESC_MAP_H__
