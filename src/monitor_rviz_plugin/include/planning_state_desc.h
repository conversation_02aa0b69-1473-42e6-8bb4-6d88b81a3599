#ifndef __PLANNING_STATE_DESC_H__
#define __PLANNING_STATE_DESC_H__

#include <state_msgs/msg/planning_state.hpp>

// clang-format off
const std::unordered_map<uint8_t, std::string> PLANNING_STATE_DESC_MAP = 
{
    {state_msgs::msg::PlanningState::PLANNING_UNINIT, "PlanningUinit"}, 
    {state_msgs::msg::PlanningState::PLANNING_RUNNING_LANE, "PlanningRunningLane"},
    {state_msgs::msg::PlanningState::PLANNING_RUNNING_LANE_OBSTACLE_AVOID, "PlanningRunningLaneObstacleAvoid"}, 
    {state_msgs::msg::PlanningState::PLANNING_RUNNING_LANE_OBSTACLE_STOP, "PlanningRunningLaneObstacleStop"},
    {state_msgs::msg::PlanningState::PLANNING_RUNNING_FREE, "PlanningRunningFree"},
    {state_msgs::msg::PlanningState::PLANNING_FINISH, "PlanningFinish"}, 
    {state_msgs::msg::PlanningState::PLANNING_CANCEL, "PlanningCancel"},
    {state_msgs::msg::PlanningState::PLANNING_ERROR, "PlanningError"}
};
// clang-format on

#endif  //__PLANNING_STATE_DESC_H__