#ifndef MONITOR_RVIZ_PLUGIN_PANEL_H_
#define MONITOR_RVIZ_PLUGIN_PANEL_H_

//所需要包含的头文件
#include <QCursor>  // NOLINT cpplint cannot handle the include order here
#include <QObject>  // NOLINT cpplint cannot handle the include order here

#include "rclcpp/rclcpp.hpp"
#include "rclcpp/qos.hpp"
#include "rviz_common/panel.hpp"

#include "std_msgs/msg/string.hpp"
#include "std_msgs/msg/bool.hpp"
#include "geometry_msgs/msg/twist.hpp"
#include "geometry_msgs/msg/pose_stamped.hpp"
#include "geometry_msgs/msg/pose_with_covariance_stamped.hpp"
#include <custom_msgs/msg/hmi_mode.hpp>
#include <custom_msgs/msg/hmi_config.hpp>
#include <custom_msgs/msg/status_message.hpp>
#include <custom_msgs/msg/system_status.hpp>
#include <umodule_msgs/msg/module_state.hpp>
#include "module_state_desc_map.h"
#include "monitor_rviz_plugin.h"

#include "rviz_common/display_context.hpp"
#include <json/json.h>

const std::map<int, QColor> ROW_COLOR_MAP = {
    {0, Qt::black},      {1, Qt::red},       {2, Qt::green},        {3, Qt::blue},        {4, Qt::cyan},
    {5, Qt::magenta},    {6, Qt::yellow},    {7, Qt::gray},         {8, Qt::darkRed},     {9, Qt::darkGreen},
    {10, Qt::darkBlue},  {11, Qt::darkCyan}, {12, Qt::darkMagenta}, {13, Qt::darkYellow}, {14, Qt::darkGray},
    {15, Qt::lightGray}, {16, Qt::white}};

class QLineEdit;

namespace monitor_rviz_plugin {
// 所有的plugin都必须是rviz::Panel的子类
class MonitorRvizPanel : public rviz_common::Panel
{
    // 后边需要用到Qt的信号和槽，都是QObject的子类，所以需要声明Q_OBJECT宏
    Q_OBJECT
public:
    // 构造函数，在类中会用到QWidget的实例来实现GUI界面，这里先初始化为0即可
    MonitorRvizPanel(QWidget *parent = 0);

    // 重载rviz::Panel积累中的函数，用于保存、加载配置文件中的数据，在我们这个plugin
    // 中，数据就是topic的名称
    virtual void load(const rviz_common::Config &config);
    virtual void save(rviz_common::Config config) const;

    void onInitialize() override;

    // 内部槽.
protected Q_SLOTS:
    // 在窗口大小变化时触发
    void resizeEvent(QResizeEvent *event);

private:
    void hmiModeCallback(const custom_msgs::msg::HmiMode::SharedPtr msg);
    void systemStatusCallback(const custom_msgs::msg::SystemStatus::SharedPtr msg);
    void moduleStateCallback(const umodule_msgs::msg::ModuleState::SharedPtr msg);
    void updateTable(const custom_msgs::msg::SystemStatus::SharedPtr msg);
    void updateQwtPlot(const custom_msgs::msg::SystemStatus::SharedPtr msg);
    Ui::Form ui_;
    std::shared_ptr<rclcpp::Node> node_;

    // Subscribers
    rclcpp::Subscription<custom_msgs::msg::HmiMode>::SharedPtr sub_hmi_mode_;
    rclcpp::Subscription<custom_msgs::msg::SystemStatus>::SharedPtr sub_system_status_;
    rclcpp::Subscription<umodule_msgs::msg::ModuleState>::SharedPtr sub_module_state_;

    std::map<std::string, int> module_name_row_map_;
    std::string status_str[6] = {"UNKNOWN", "OK", "INFO", "WARN", "ERROR", "FATAL"};

    std::vector<std::vector<double>> module_cpu_resource_;
    std::vector<double> time_sequence_;
};

}  // namespace monitor_rviz_plugin

#endif  // MONITOR_RVIZ_PLUGIN_PANEL_H_
