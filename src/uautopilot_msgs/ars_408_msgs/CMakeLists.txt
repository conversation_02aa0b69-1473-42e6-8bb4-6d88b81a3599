cmake_minimum_required(VERSION 3.5)
project(ars_408_msgs)

# Default to C99
if(NOT CMAKE_C_STANDARD)
  set(CMAKE_C_STANDARD 99)
endif()

# Default to C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()
# find dependencies
find_package(ament_cmake REQUIRED)
find_package(std_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(rosidl_default_generators REQUIRED)

set(msg_files
"msg/Cluster.msg"
"msg/ClusterList.msg"
"msg/ObjectARS.msg"
"msg/ObjectList.msg"
"msg/RadarStatus.msg"
)

set(srv_files
"srv/MaxDistance.srv"
"srv/OutputType.srv"
"srv/RadarPower.srv"
"srv/RCSThreshold.srv"
"srv/SensorID.srv"
"srv/SortIndex.srv"
)


rosidl_generate_interfaces(${PROJECT_NAME}
	${msg_files}
	${srv_files}
  DEPENDENCIES builtin_interfaces std_msgs geometry_msgs
)

ament_package()
