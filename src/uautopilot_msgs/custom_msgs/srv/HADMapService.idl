/**
 *
 *   @file HADMapService.idl
 *   <AUTHOR> (<EMAIL>)
 *   @brief
 *   @version 0.1
 *   @date 2022-10-11
 *
 *   (C) 2022 UBTECH.Co.Ltd. All rights reserved
 *
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF MERC<PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include "custom_msgs/msg/HADMapBin.idl"

module custom_msgs
{
    module srv
    {
        // enum type not working yet on ROS2 implementation of idl
        // enum HADPrimitive
        // {
        //   FullMap,
        //   AllPrimitives,
        //   DriveableGeometry,
        //   RegulatoryElements,
        //   StaticObjects
        // };

        module HADMapService_Request_Constants
        {
            const uint8 FULL_MAP = 0;
            const uint8 ALL_PRIMITIVES = 1;
            const uint8 DRIVEABLE_GEOMETRY = 2;
            const uint8 REGULATORY_ELEMENTS = 3;
            const uint8 STATIC_OBJECTS = 4;
        };

        struct HADMapService_Request {
            sequence<uint8> requested_primitives;
            @verbatim(language = "comment",
                      text = "Geometric upper bound of map data requested") sequence<double, 3> geom_upper_bound;
            @verbatim(language = "comment",
                      text = "Geometric upper bound of map data requested") sequence<double, 3> geom_lower_bound;
        };
        
        struct HADMapService_Response {
            custom_msgs::msg::HADMapBin amap; //不能命名为map关键字，否则fastddsgen会报错
            int32 answer;
        };
    };
};
