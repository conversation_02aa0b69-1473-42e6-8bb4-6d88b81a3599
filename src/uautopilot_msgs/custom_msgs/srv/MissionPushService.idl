module custom_msgs
{
    module srv
    {
        module MissionType_Constants
        {
            const uint8 AUTO_LANE_NAVIGATION = 0;
            const uint8 FREE_SPACE_NAVIGATION = 1;
            const uint8 STATIC_LANE_NAVIGATION = 2;
            const uint8 TEACH_START = 3;
            const uint8 TEACH_STOP = 4;
            const uint8 PLAYBACK_LOAD = 5;
        };

        struct MissionPushService_Request {
            string mission_id;
            uint8 mission_type;

            boolean auto_start;
        };

        struct MissionPushService_Response {
            boolean success;
            string reason;
        };
    };
};