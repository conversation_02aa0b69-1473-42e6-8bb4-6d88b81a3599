//#ifndef __custom_msgs__srv__traffic_light_service__idl__
//#define __custom_msgs__srv__traffic_light_service__idl__

#include "custom_msgs/msg/TrafficLight.idl"
#include "custom_msgs/msg/TrafficGuide.idl"


module custom_msgs
{
    module srv
    {

        struct TrafficLightService_Request
        {
            uint32 ask_id;
            boolean ask;
        };
        
        struct TrafficLightService_Response
        {
            custom_msgs::msg::TrafficLight traffic_light;
            // custom_msgs::msg::TrafficGuide traffic_guide;
            sequence<custom_msgs::msg::TrafficGuide, 100> traffic_guide_sequence;
        };
        
    };
};



//#endif