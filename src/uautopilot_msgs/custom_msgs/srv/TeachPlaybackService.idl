#include "custom_msgs/msg/Trajectory.idl"

module custom_msgs
{
    module srv
    {
        module TeachPlaybackService_Request_Command_Constants
        {
            const uint8 TEACH_START = 0;
            const uint8 TEACH_STOP = 1;
            const uint8 PLAYBACK_LOAD = 2;
        };

        struct TeachPlaybackService_Request {
            uint8 command;
            string record_id;
        };

        struct TeachPlaybackService_Response {
            boolean success;
            string reason;
            custom_msgs::msg::Trajectory trajectory;
        };
    };
};