/**
 *   
 *   @file PerceptionObstacle.idl
 *   <AUTHOR> (<EMAIL>)
 *   @brief 
 *   @version 0.1
 *   @date 2022-09-19
 *   
 *   (C) 2022 UBTECH.Co.Ltd. All rights reserved
 *   
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *   
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF ME<PERSON>HANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SU<PERSON><PERSON>TU<PERSON> GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *   
 */

#ifndef __custom_msgs__msg__perception_obstacle__idl__
#define __custom_msgs__msg__perception_obstacle__idl__

#include "std_msgs/msg/Header.idl"
#include "custom_msgs/msg/ErrorCode.idl"

module custom_msgs {

module msg{


struct PerceptionObstacle{
    long id;
};

struct LaneMarker{
    //apollo.hdmap.LaneBoundaryType.Type lane_type,
    double quality;  // range = [0,1]; 1 = the best quality
    long model_degree;

    // equation X = c3 * Z^3 + c2 * Z^2 + c1 * Z + c0
    double c0_position;
    double c1_heading_angle;
    double c2_curvature;
    double c3_curvature_derivative;
    double view_range;
    double longitude_start;
    double longitude_end;
};

struct LaneMarkers{
    LaneMarker left_lane_marker;
    LaneMarker right_lane_marker;
    sequence<LaneMarker> next_left_lane_marker;
    sequence<LaneMarker> next_right_lane_marker;
};

struct CIPVInfo{
    long cipv_id;
    long potential_cipv_id;
};

struct PerceptionObstacles{
    sequence<PerceptionObstacle> perception_obstacle;
    std_msgs::msg::Header header;
    custom_msgs::msg::error::ErrorCode error_code;
    LaneMarkers lane_marker;
    CIPVInfo cipv_info;
};

};  // module msg

};  // module custom_msgs
#endif  // __custom_msgs__msg__perception_obstacle__idl__
