#include "custom_msgs/msg/ModuleStatus.idl"
#include "custom_msgs/msg/StatusMessage.idl"
#include "std_msgs/msg/Header.idl"

module custom_msgs
{
    module msg
    {
        struct SystemStatus {
            std_msgs::msg::Header header;
            // map<string, StatusMessage> hmi_modules;
            //map<string, custom_msgs::msg::ModuleStatus> monitored_modules;
            sequence<custom_msgs::msg::ModuleStatus> monitored_modules;
            string passenger_msg;
            double safety_mode_trigger_time;
            boolean require_emergency_stop;
            boolean is_realtime_in_simulation;
            // map<string, StatusMessage> other_components;
        };

    };  // module msg

};      // module custom_msgs