/**
 *   
 *   @file ObjectClassification.idl
 *   <AUTHOR> (<EMAIL>)
 *   @brief 
 *   @version 0.1
 *   @date 2022-10-19
 *   
 *   (C) 2022 UBTECH.Co.Ltd. All rights reserved
 *   
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *   
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF ME<PERSON>HANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *   
 */

// #ifndef __custom_msgs__msg__object_classification__idl__
// #define __custom_msgs__msg__object_classification__idl__

module custom_msgs {
  module msg {
    module ObjectClassification_Constants {
      const uint8 UNKNOWN = 0;
      const uint8 CAR = 1;
      const uint8 TRUCK = 2;
      const uint8 BUS = 3;
      const uint8 TRAILER = 4;
      const uint8 MOTORCYCLE = 5;
      const uint8 BICYCLE = 6;
      const uint8 PEDESTRIAN = 7;
      const uint8 OTHER_VEHICLE = 8;
      const uint8 TRAFFIC_CONE = 9;
      const uint8 RAILING = 10;
      const uint8 GARBAGE_BIN = 11;
      const uint8 RACK = 12;
      const uint8 SHELVES = 13;
      const uint8 GOODS = 14;
      const uint8 TRAFFIC_LIGHT = 15;
      const uint8 FORKLIFT = 16;
      const uint8 AGV = 17;
    };

    struct ObjectClassification {
      @verbatim (language="comment", text=
        " Valid values for the classification field are provided in"
        " ObjectClassification_Constants.")
      uint8 classification;

      @range (min=0.0, max=1.0)
      float probability;
    };
  };
};

// #endif //__custom_msgs__msg__object_classification__idl__