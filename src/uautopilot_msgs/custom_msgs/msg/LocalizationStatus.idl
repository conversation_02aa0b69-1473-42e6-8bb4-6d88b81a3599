/**
 *   
 *   @file Component.idl
 *   <AUTHOR> (<EMAIL>)
 *   @brief 
 *   @version 0.1
 *   @date 2022-09-13
 *   
 *   (C) 2022 UBTECH.Co.Ltd. All rights reserved
 *   
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *   
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF ME<PERSON>HA<PERSON><PERSON><PERSON>ITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *   
 */

#ifndef __custom_msgs__msg__localization_status__idl__
#define __custom_msgs__msg__localization_status__idl__

#include "std_msgs/msg/Header.idl"
#include "geometry_msgs/msg/Point.idl"
#include "geometry_msgs/msg/Pose.idl"
#include "geometry_msgs/msg/Vector3.idl"
#include "custom_msgs/msg/TrajectoryPoint.idl"


module custom_msgs {
module msg {

// LiDAR-based loclaization module status
enum LocalLidarStatus {
  MSF_LOCAL_LIDAR_NORMAL,       // Localization result satisfy threshold
  MSF_LOCAL_LIDAR_MAP_MISSING,  // Can't find localization map (config.xml)
  MSF_LOCAL_LIDAR_EXTRINSICS_MISSING,  // Missing extrinsic parameters
  MSF_LOCAL_LIDAR_MAP_LOADING_FAILED,  // Fail to load localization map
  MSF_LOCAL_LIDAR_NO_OUTPUT,    // No output (comparing to timestamp of imu msg)
  MSF_LOCAL_LIDAR_OUT_OF_MAP,   // Coverage of online pointcloud and map is lower than threshold
  MSF_LOCAL_LIDAR_NOT_GOOD,     // Localization result do not meet threshold
  MSF_LOCAL_LIDAR_UNDEFINED_STATUS  // others
};

enum LocalLidarQuality {
  MSF_LOCAL_LIDAR_VERY_GOOD,
  MSF_LOCAL_LIDAR_GOOD,
  MSF_LOCAL_LIDAR_NOT_BAD,
  MSF_LOCAL_LIDAR_BAD
};

// LiDAR-based localization result check (the difference between lidar and sins
// result)
enum LocalLidarConsistency {
  MSF_LOCAL_LIDAR_CONSISTENCY_00,  // The difference is less than threshold 1
  MSF_LOCAL_LIDAR_CONSISTENCY_01,  // The difference is bigger than threshold 1 but less than threshold 2
  MSF_LOCAL_LIDAR_CONSISTENCY_02,  // The difference is bigger than threshold 2
  MSF_LOCAL_LIDAR_CONSISTENCY_03   // others
};

// GNSS-based localization result check (the difference between GNSS and sins
// result)
enum GnssConsistency {
  MSF_GNSS_CONSISTENCY_00,      // The difference is less than threshold 1
  MSF_GNSS_CONSISTENCY_01,      // The difference is bigger than threshold 1 but less than threshold 2
  MSF_GNSS_CONSISTENCY_02,      // The difference is bigger than threshold 2
  MSF_GNSS_CONSISTENCY_03       // others
};

enum GnssPositionType {
  NONE,                // No solution
  FIXEDPOS,            // Position has been fixed by the FIX POSITION command or by
                       // position averaging
  FIXEDHEIGHT,         // Position has been fixed by the FIX HEIGHT, or FIX AUTO,
                       // command or by position averaging
  FLOATCONV,           // Solution from floating point carrier phase anbiguities
  WIDELANE,            // Solution from wide-lane ambiguities
  NARROWLANE,          // Solution from narrow-lane ambiguities
  DOPPLER_VELOCITY,    // Velocity computed using instantaneous Doppler
  SINGLE,              // Single point position
  PSRDIFF,             // Pseudorange differential solution
  WAAS,                // Solution calculated using corrections from an SBAS
  PROPOGATED,          // Propagated by a Kalman filter without new observations
  OMNISTAR,            // OmniSTAR VBS position
  L1_FLOAT,            // Floating L1 albiguity solution
  IONOFREE_FLOAT,      // Floating ionospheric free ambiguity solution
  NARROW_FLOAT,        // Floating narrow-lane anbiguity solution
  L1_INT,              // Integer L1 ambiguity solution
  WIDE_INT,            // Integer wide-lane ambiguity solution
  NARROW_INT,          // Integer narrow-lane ambiguity solution
  RTK_DIRECT_INS,      // RTK status where RTK filter is directly initialized
                       // from the INS filter
  INS_SBAS,            // INS calculated position corrected for the antenna
  INS_PSRSP,           // INS pseudorange single point solution - no DGPS corrections
  INS_PSRDIFF,          // INS pseudorange differential solution
  INS_RTKFLOAT,        // INS RTK float point ambiguities solution
  INS_RTKFIXED,        // INS RTK fixed ambiguities solution
  INS_OMNISTAR,        // INS OmniSTAR VBS solution
  INS_OMNISTAR_HP,     // INS OmniSTAR high precision solution
  INS_OMNISTAR_XP,     // INS OmniSTAR extra precision solution
  OMNISTAR_HP,         // OmniSTAR high precision
  OMNISTAR_XP,         // OmniSTAR extra precision
  PPP_CONVERGING,      // Precise Point Position(PPP) solution converging
  PPP,                 // Precise Point Position(PPP)solution
  INS_PPP_Converging,  // INS NovAtel CORRECT Precise Point Position(PPP)
                       // solution converging
  INS_PPP,             // INS NovAtel CORRECT Precise Point Position(PPP) solution
  MSG_LOSS             // Gnss position message loss
};

// IMU msg status
enum ImuMsgDelayStatus {
  IMU_DELAY_NORMAL,
  IMU_DELAY_1,
  IMU_DELAY_2,
  IMU_DELAY_3,
  IMU_DELAY_ABNORMAL
};

enum ImuMsgMissingStatus {
  IMU_MISSING_NORMAL,
  IMU_MISSING_1,
  IMU_MISSING_2,
  IMU_MISSING_3,
  IMU_MISSING_4,
  IMU_MISSING_5,
  IMU_MISSING_ABNORMAL
};

enum ImuMsgDataStatus {
  IMU_DATA_NORMAL,
  IMU_DATA_ABNORMAL,
  IMU_DATA_OTHER
};

// The running status of localization module
enum MsfRunningStatus {
  MSF_SOL_LIDAR_GNSS,
  MSF_SOL_X_GNSS,
  MSF_SOL_LIDAR_X,
  MSF_SOL_LIDAR_XX,
  MSF_SOL_LIDAR_XXX,
  MSF_SOL_X_X,
  MSF_SOL_X_XX,
  MSF_SOL_X_XXX,
  MSF_SSOL_LIDAR_GNSS,
  MSF_SSOL_X_GNSS,
  MSF_SSOL_LIDAR_X,
  MSF_SSOL_LIDAR_XX,
  MSF_SSOL_LIDAR_XXX,
  MSF_SSOL_X_X,
  MSF_SSOL_X_XX,
  MSF_SSOL_X_XXX,
  MSF_NOSOL_LIDAR_GNSS,
  MSF_NOSOL_X_GNSS,
  MSF_NOSOL_LIDAR_X,
  MSF_NOSOL_LIDAR_XX,
  MSF_NOSOL_LIDAR_XXX,
  MSF_NOSOL_X_X,
  MSF_NOSOL_X_XX,
  MSF_NOSOL_X_XXX,
  MSF_RUNNING_INIT
};

// The status of sensor msg
struct MsfSensorMsgStatus {
  ImuMsgDelayStatus imu_delay_status;
  ImuMsgMissingStatus imu_missing_status;
  ImuMsgDataStatus imu_data_status;
};

// The status of msf localization module
struct MsfStatus {
  LocalLidarConsistency local_lidar_consistency;
  GnssConsistency gnss_consistency;
  LocalLidarStatus local_lidar_status;
  LocalLidarQuality local_lidar_quality;
  GnssPositionType gnsspos_position_type;
  MsfRunningStatus msf_running_status;
};

struct Uncertainty{
    geometry_msgs::msg::Vector3 position_std_dev;
    geometry_msgs::msg::Vector3 orientation_std_dev;
    geometry_msgs::msg::Vector3 linear_velocity_std_dev;
    geometry_msgs::msg::Vector3 linear_acceleration_std_dev;
    geometry_msgs::msg::Vector3 angular_velocity_std_dev;
};

struct Pose{
    geometry_msgs::msg::Pose pose;
    geometry_msgs::msg::Vector3 linear_velocity;
    geometry_msgs::msg::Vector3 linear_acceleration;
    geometry_msgs::msg::Vector3 angular_velocity;
    double heading;
    geometry_msgs::msg::Vector3 linear_acceleration_vrf;
    geometry_msgs::msg::Vector3 angular_velocity_vrf;
    geometry_msgs::msg::Vector3 euler_angles;
};


struct LocalizationEstimate{
    std_msgs::msg::Header header;
    Pose pose;
    Uncertainty uncertainty;
    double measurement_time;
    custom_msgs::msg::TrajectoryPoint trajectory_point; 
    MsfStatus msf_status;
    MsfSensorMsgStatus sensor_status;
};


module measure{
    enum MeasureState{
        OK,
        WARNING,
        ERROR,
        CRITICAL_ERROR,
        FATAL_ERROR
    };
};

struct LocalizationStatus{
    std_msgs::msg::Header header;
    measure::MeasureState fusion_status;
    measure::MeasureState gnss_status;
    measure::MeasureState lidar_status;
    double measurement_time;
    string state_message;
};

};  // module msg

};  // module custom_msgs
#endif  // __custom_msgs__msg__localization_status__idl__