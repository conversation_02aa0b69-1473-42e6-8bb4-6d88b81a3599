/**
 *   
 *   @file Trajectory.idl
 *   <AUTHOR> (<EMAIL>)
 *   @brief 
 *   @version 0.1
 *   @date 2022-10-20
 *   
 *   (C) 2022 UBTECH.Co.Ltd. All rights reserved
 *   
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *   
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF ME<PERSON>HANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *   
 */
// #ifndef __custom_msgs__msg__trajectory__idl__
// #define __custom_msgs__msg__trajectory__idl__

#include "custom_msgs/msg/TrajectoryPoint.idl"
#include "std_msgs/msg/Header.idl"

module custom_msgs {
  module msg {
    @verbatim (language="comment", text=
      " A set of trajectory points for the controller")
    struct Trajectory {
      std_msgs::msg::Header header;

      sequence<custom_msgs::msg::TrajectoryPoint> points;
    };
  };
};


// #endif //__custom_msgs__msg__trajectory__idl__
