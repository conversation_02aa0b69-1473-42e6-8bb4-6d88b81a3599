
//#ifndef __custom_msgs__msg__post_process_objects__idl__
//#define __custom_msgs__msg__post_process_objects__idl__

#include "std_msgs/msg/Header.idl"
#include "custom_msgs/msg/PredictedPath.idl"
#include "custom_msgs/msg/ObjectClassification.idl"
#include "geometry_msgs/msg/Twist.idl"
#include "geometry_msgs/msg/Pose.idl"
#include "geometry_msgs/msg/Point32.idl"

module custom_msgs{
module msg{
module PostProcessObject_Constants{
    const uint8 PERCEPTION = 0;
    const uint8 CLUSTER = 1;
    const uint8 SAVE_BLIND = 2;
};

struct PostProcessObject{
    uint8 post_process_type;
    unsigned long id;
    geometry_msgs::msg::Point32 size;
    geometry_msgs::msg::Pose pose;
    geometry_msgs::msg::Twist twist;
    custom_msgs::msg::ObjectClassification oc;
    sequence<custom_msgs::msg::PredictedPath> path;
    sequence<geometry_msgs::msg::Point32> vertexes;
};

}; // module msg
}; // module custom_msgs



//#endif //__custom_msgs__msg__post_process_objects__idl__
