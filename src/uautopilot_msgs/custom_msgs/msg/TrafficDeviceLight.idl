
#include "std_msgs/msg/Header.idl"

module custom_msgs{
    module msg{
       
        
        module TrafficDeviceLight_Constants{
            
            const uint8 COLOR_UNKNOWN = 0;
            const uint8 COLOR_RED = 1;
            const uint8 COLOR_YELLOW = 2;
            const uint8 COLOR_GREEN = 3;
        
            const uint8 RULE_UNKNOWN = 0;
            const uint8 RULE_STRAIGHT = 1;
            const uint8 RULE_TURN_LEFT = 2;
            const uint8 RULE_TURN_RIGHT = 3;
        
            const uint8 LOCATION_UNKNOWN = 0;
            const uint8 LOCATION_EAST = 1;
            const uint8 LOCATION_SOUTH = 2;
            const uint8 LOCATION_WEST = 3;
            const uint8 LOCATION_NORTH = 4;
        };

        struct TrafficDeviceLight
        {
            uint8 location;
            int16 time;
            boolean twinkle;
            uint8 color;
            uint8 rule;
        };
    };
};

