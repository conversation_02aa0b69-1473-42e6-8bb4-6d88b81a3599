/**
 *   
 *   @file ErrorCode.idl
 *   <AUTHOR> (<EMAIL>)
 *   @brief 
 *   @version 0.1
 *   @date 2022-09-09
 *   
 *   (C) 2022 UBTECH.Co.Ltd. All rights reserved
 *   
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *   
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF ME<PERSON>HANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *   
 */

#ifndef __custom_msgs__msg__error_code__idl__
#define __custom_msgs__msg__error_code__idl__
#include "std_msgs/msg/Header.idl"
module custom_msgs {

module msg{

module error{


enum ErrorCode {
  // No error, returns on success.
  OK,

  // Control module error codes start from here.
  CONTROL_ERROR,
  CONTROL_INIT_ERROR,
  CONTROL_COMPUTE_ERROR,
  CONTROL_ESTOP_ERROR,
  PERFECT_CONTROL_ERROR,

  // Canbus module error codes start from here.
  CANBUS_ERROR,
  CAN_CLIENT_ERROR_BASE,
  CAN_CLIENT_ERROR_OPEN_DEVICE_FAILED,
  CAN_CLIENT_ERROR_FRAME_NUM,
  CAN_CLIENT_ERROR_SEND_FAILED,
  CAN_CLIENT_ERROR_RECV_FAILED,

  // Localization module error codes start from here.
  LOCALIZATION_ERROR,
  LOCALIZATION_ERROR_MSG,
  LOCALIZATION_ERROR_LIDAR,
  LOCALIZATION_ERROR_INTEG,
  LOCALIZATION_ERROR_GNSS,

  // Perception module error codes start from here.
  PERCEPTION_ERROR,
  PERCEPTION_ERROR_TF,
  PERCEPTION_ERROR_PROCESS,
  PERCEPTION_FATAL,
  PERCEPTION_ERROR_NONE,
  PERCEPTION_ERROR_UNKNOWN,

  // Prediction module error codes start from here.
  PREDICTION_ERROR,

  // Planning module error codes start from here
  PLANNING_ERROR,
  PLANNING_ERROR_NOT_READY,

  // HDMap module error codes start from here
  HDMAP_DATA_ERROR,

  // Routing module error codes
  ROUTING_ERROR,
  ROUTING_ERROR_REQUEST,
  ROUTING_ERROR_RESPONSE,
  ROUTING_ERROR_NOT_READY,

  // Indicates an input has been exhausted.
  END_OF_INPUT,

  // HTTP request error codes.
  HTTP_LOGIC_ERROR,
  HTTP_RUNTIME_ERROR,

  // Relative Map error codes.
  RELATIVE_MAP_ERROR,  // general relative map error code
  RELATIVE_MAP_NOT_READY,

  // Driver error codes.
  DRIVER_ERROR_GNSS,
  DRIVER_ERROR_VELODYNE,

  // Storytelling error codes.
  STORYTELLING_ERROR

};
};
struct ErrorCode{
     std_msgs::msg::Header header;

};

};  // module msg

};  // module custom_msgs
#endif  // __custom_msgs__msg__error_code__idl__