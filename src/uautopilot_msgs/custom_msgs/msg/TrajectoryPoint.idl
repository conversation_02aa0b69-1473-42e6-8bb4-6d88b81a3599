/**
 *   
 *   @file TrajectoryPoint.idl
 *   <AUTHOR> (<EMAIL>)
 *   @brief 
 *   @version 0.1
 *   @date 2022-10-19
 *   
 *   (C) 2022 UBTECH.Co.Ltd. All rights reserved
 *   
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *   
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF ME<PERSON>HANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SU<PERSON><PERSON>TUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *   
 */

// #ifndef __custom_msgs__msg__trajectory_point__idl__
// #define __custom_msgs__msg__trajectory_point__idl__

// #include "builtin_interfaces/msg/Duration.idl"
#include "geometry_msgs/msg/Pose.idl"

module custom_msgs {

module msg {
    @verbatim (language="comment", text=
      " Representation of a trajectory point for the controller")
    struct TrajectoryPoint {
      float time_from_start;

      geometry_msgs::msg::Pose pose;

      @default (value=0.0)
      float longitudinal_velocity_mps;

      @default (value=0.0)
      float lateral_velocity_mps;

      @default (value=0.0)
      float acceleration_mps2;

      @default (value=0.0)
      float heading_rate_rps;

      @default (value=0.0)
      float front_wheel_angle_rad;

      @default (value=0.0)
      float rear_wheel_angle_rad;
    };

};  //msg

}; //custom_msgs



// #endif //__custom_msgs__msg__trajectory_point__idl__