/**
 *   
 *   @file HADMapRoute.idl
 *   <AUTHOR> (<EMAIL>)
 *   @brief 
 *   @version 0.1
 *   @date 2022-10-12
 *   
 *   (C) 2022 UBTECH.Co.Ltd. All rights reserved
 *   
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *   
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF MERC<PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *   
 */

#ifndef __custom_msgs__msg__had_map_route__idl__
#define __custom_msgs__msg__had_map_route__idl__

#include "custom_msgs/msg/HADMapSegment.idl"
#include "geometry_msgs/msg/Pose.idl"
#include "std_msgs/msg/Header.idl"

module custom_msgs {
  module msg {
    @verbatim (language="comment", text=
      " A route within a high-definition map defined by"
      " the start and goal points and map primitives"
      " describing the route between the two.")
    struct HADMapRoute {
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        " The start_pose must exist within the bounds of the primitives in the first"
        " segment defined in the route_segments array.")
      geometry_msgs::msg::Pose start_pose;

      @verbatim (language="comment", text=
        " The goal_pose must exist within the bounds of the primitives in the last"
        " segment defined in the route_semgents array.")
      geometry_msgs::msg::Pose goal_pose; 

      sequence<custom_msgs::msg::HADMapSegment> segments;
    };
  };
};

#endif //__custom_msgs__msg__had_map_route__idl__