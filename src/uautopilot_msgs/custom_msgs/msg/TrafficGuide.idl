//#ifndef __custom_msgs__msg__traffic_light__idl__
//#define __custom_msgs__msg__traffic_light__idl__

#include "std_msgs/msg/Header.idl"
#include "custom_msgs/msg/TrafficLight.idl"

module custom_msgs
{
    module msg
    {
        module TrafficGuide_Constants{
            const uint8 UNKNOWN = 0;
            const uint8 STRAIGHT = 1;
            const uint8 TURN_LEFT = 2;
            const uint8 TURN_RIGHT = 3;
        };

        // 带颜色和箭头的交通灯
        struct TrafficGuide
        {
            std_msgs::msg::Header header;
            uint32 id;
            custom_msgs::msg::TrafficLight light_color; 
            uint8 rule;
        };
        
        
    };
};







//#endif