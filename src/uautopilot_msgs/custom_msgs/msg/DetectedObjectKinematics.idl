/**
 *   
 *   @file DetectedObjectKinematics.idl
 *   <AUTHOR> <PERSON><PERSON> (<EMAIL>)
 *   @brief 
 *   @version 0.1
 *   @date 2022-10-19
 *   
 *   (C) 2022 UBTECH.Co.Ltd. All rights reserved
 *   
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *   
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *   
 */

#ifndef __custom_msgs__msg__detected_object_kinematics__idl__
#define __custom_msgs__msg__detected_object_kinematics__idl__

#include "geometry_msgs/msg/Point.idl"
#include "geometry_msgs/msg/PoseWithCovariance.idl"
#include "geometry_msgs/msg/Quaternion.idl"
#include "geometry_msgs/msg/TwistWithCovariance.idl"

module custom_msgs {
  module msg {
    module DetectedObjectKinematics_Constants {
      /**
       * Only position is available, orientation is empty. Note that the shape can be an oriented
       * bounding box but the direction the object is facing is unknown, in which case
       * orientation should be empty.
       */
      const uint8 UNAVAILABLE = 0;
      /**
       * The orientation is determined only up to a sign flip. For instance, assume that cars are
       * longer than they are wide, and the perception pipeline can accurately estimate the
       * dimensions of a car. It should set the orientation to coincide with the major axis, with
       * the sign chosen arbitrarily, and use this tag to signify that the orientation could
       * point to the front or the back.
       */
      const uint8 SIGN_UNKNOWN = 1;
      /**
       * The full orientation is available. Use e.g. for machine-learning models that can
       * differentiate between the front and back of a vehicle.
       */
      const uint8 AVAILABLE = 2;
    };

    struct DetectedObjectKinematics {
      geometry_msgs::msg::PoseWithCovariance pose_with_covariance;

      boolean has_position_covariance;
      uint8 orientation_availability;

      geometry_msgs::msg::TwistWithCovariance twist;

      boolean has_twist;
      boolean has_twist_covariance;
    };
  };
};



#endif //__custom_msgs__msg__detected_object_kinematics__idl__