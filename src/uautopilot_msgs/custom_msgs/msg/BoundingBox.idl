/**
 *
 *   @file BoundingBox.idl
 *   <AUTHOR> (<EMAIL>)
 *   @brief
 *   @version 0.1
 *   @date 2022-10-19
 *
 *   (C) 2022 UBTECH.Co.Ltd. All rights reserved
 *
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF ME<PERSON>HANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

// #ifndef __custom_msgs__msg__bounding_box__idl__
// #define __custom_msgs__msg__bounding_box__idl__
#include "geometry_msgs/msg/Quaternion.idl"
#include "geometry_msgs/msg/Point32.idl"
#include "custom_msgs/msg/ObjectClassification.idl"

module custom_msgs
{
    module msg
    {
        typedef geometry_msgs::msg::Point32 geometry_msgs__msg__Point32;
        typedef geometry_msgs__msg__Point32 geometry_msgs__msg__Point32__4[4];

        @verbatim(language = "comment", text = " Oriented bounding box representation") struct BoundingBox {
            geometry_msgs::msg::Point32 centroid;

            geometry_msgs::msg::Point32 size;

            geometry_msgs::msg::Quaternion orientation;

            geometry_msgs__msg__Point32__4 corners;

            @verbatim(language = "comment",
                      text = " can hold arbitrary value, e.g. likelihood, area, perimeter") float value;

            custom_msgs::msg::ObjectClassification oc;
        };
    };
};

// #endif //__custom_msgs__msg__bounding_box__idl__