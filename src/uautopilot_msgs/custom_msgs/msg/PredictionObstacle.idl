/**
 *   
 *   @file PredictionObstacle.idl
 *   <AUTHOR> (<EMAIL>)
 *   @brief 
 *   @version 0.1
 *   @date 2022-09-19
 *   
 *   (C) 2022 UBTECH.Co.Ltd. All rights reserved
 *   
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *   
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF ME<PERSON>HANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SU<PERSON><PERSON>TU<PERSON> GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *   
 */

#ifndef __custom_msgs__msg__prediction_obstacle__idl__
#define __custom_msgs__msg__prediction_obstacle__idl__

#include "std_msgs/msg/Header.idl"
#include "custom_msgs/msg/ErrorCode.idl"
#include "custom_msgs/msg/PerceptionObstacle.idl"

module custom_msgs {

module msg{

/*
// estimated obstacle intent
message ObstacleIntent {
  enum Type {
    UNKNOWN = 0;
    STOP = 1;
    STATIONARY = 2;
    MOVING = 3;
    CHANGE_LANE = 4;
    LOW_ACCELERATION = 5;
    HIGH_ACCELERATION = 6;
    LOW_DECELERATION = 7;
    HIGH_DECELERATION = 8;
  }
  optional Type type = 1 [default = UNKNOWN];
}

// self driving car intent
message Intent {
  enum Type {
    UNKNOWN = 0;
    STOP = 1;
    CRUISE = 2;
    CHANGE_LANE = 3;
  }
  optional Type type = 1 [default = UNKNOWN];
}
*/


struct PredictionObstacle{
    custom_msgs::msg::PerceptionObstacle perception_obstacle;
    double timestamp;  // GPS time in seconds
    // the length of the time for this prediction (e.g. 10s)
    double predicted_period;
    // can have multiple trajectories per obstacle
    //sequence<Trajectory> trajectory;

    // estimated obstacle intent
    //ObstacleIntent intent;

    //ObstaclePriority priority;

    //ObstacleInteractiveTag interactive_tag;

    boolean is_static;

    // Feature history latest -> earliest sequence
    //sequence<Feature> feature;
};

struct PredictionObstacles {
    // timestamp is included in header
    std_msgs::msg::Header header;

    // make prediction for multiple obstacles
    sequence<PredictionObstacle> prediction_obstacle;

    // perception error code
    custom_msgs::msg::error::ErrorCode perception_error_code;

    // start timestamp
    double start_timestamp;

    // end timestamp
    double end_timestamp;

    // self driving car intent
    //Intent intent;

    // Scenario
    //Scenario scenario;
};

};  // module msg

};  // module custom_msgs
#endif  // __custom_msgs__msg__prediction_obstacle__idl__