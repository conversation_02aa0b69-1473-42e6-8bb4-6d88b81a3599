#include "custom_msgs/msg/ResourceMonitorConfig.idl"
#include "custom_msgs/msg/ChannelMonitorConfig.idl"
#include "custom_msgs/msg/ProcessMonitorConfig.idl"

module custom_msgs{
    module msg{
        struct Module {
            string module_name;
            string executable_name;
            string config_file_path;
            boolean console_print_log;
            boolean required_for_safety;
            custom_msgs::msg::ResourceMonitorConfig resource_config;
            sequence<custom_msgs::msg::ChannelMonitorConfig> channel_config;
            custom_msgs::msg::ProcessMonitorConfig process_config;
        };
    };
};