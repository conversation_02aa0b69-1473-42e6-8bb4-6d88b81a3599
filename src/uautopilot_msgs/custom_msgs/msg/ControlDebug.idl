// In ros2, "#ifndef" is not supported, refer to https://github.com/ros2/rosidl/blob/master/rosidl_parser/rosidl_parser/grammar.lark

#include "custom_msgs/msg/TrajectoryPoint.idl"
#include "std_msgs/msg/Header.idl"
#include "geometry_msgs/msg/Pose.idl"
#include "geometry_msgs/msg/Twist.idl"

module custom_msgs {
    module msg {
        struct ControlDebug {
            std_msgs::msg::Header header;
            double control_time;

            geometry_msgs::msg::Pose current_pose;
            custom_msgs::msg::TrajectoryPoint matched_point;
            custom_msgs::msg::TrajectoryPoint reference_point;

            geometry_msgs::msg::Twist velocity_in_base_link;
            geometry_msgs::msg::Twist cmd_vel;
            
            geometry_msgs::msg::Pose matched_pose_error;
            geometry_msgs::msg::Pose reference_pose_error;

            geometry_msgs::msg::Pose reference_line_base_link_matched_error;
            geometry_msgs::msg::Pose reference_line_gravity_center_matched_error;
            geometry_msgs::msg::Pose reference_line_front_axis_center_matched_error;
        };
    };
};