cmake_minimum_required(VERSION 3.5)
project(custom_msgs)

# Default to C99
if(NOT CMAKE_C_STANDARD)
  set(CMAKE_C_STANDARD 99)
endif()

# Default to C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(std_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(unique_identifier_msgs REQUIRED)

find_package(rosidl_default_generators REQUIRED)

rosidl_generate_interfaces(${PROJECT_NAME}
  "msg/BoundingBox.idl"
  "msg/ChannelMonitorConfig.idl"
  "msg/CPUUsage.idl"
  "msg/DiskLoad.idl"
  "msg/DiskSpace.idl"
  "msg/HmiConfig.idl"
  "msg/HmiMode.idl"
  "msg/HADMapBin.idl"
  "msg/LatencyRecord.idl"
  "msg/LatencyRecordMap.idl"
  "msg/MemoryUsage.idl"
  "msg/Module.idl"
  "msg/ModuleStatus.idl"
  "msg/ObjectClassification.idl"
  "msg/PerceptionObject.idl"
  "msg/PerceptionObjects.idl"
  "msg/PredictedPath.idl"
  "msg/ProcessMonitorConfig.idl"
  "msg/ResourceMonitorConfig.idl"
  "msg/StatusMessage.idl"
  "msg/SystemStatus.idl"
  "msg/Trajectory.idl"
  "msg/TrajectoryPoint.idl"
  "msg/UbagConfig.idl"
  "msg/TrafficLight.idl"
  "msg/TrafficGuide.idl"
  "msg/ControlDebug.idl"
  "msg/PostProcessObject.idl"
  "msg/PostProcessObjects.idl"
  "srv/TrafficLightService.idl"
  "msg/TrafficDeviceLight.idl"
  "msg/TrafficDevice.idl"
  "srv/TrafficDeviceService.idl"
  "srv/HADMapService.idl"
  "srv/TeachPlaybackService.idl"
  "srv/MissionPushService.idl"

  DEPENDENCIES
  std_msgs
  geometry_msgs
  unique_identifier_msgs
)

ament_package()
