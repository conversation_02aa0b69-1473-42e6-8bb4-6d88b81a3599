#                  -<1>--<2>--<3>-
#                  ^             ^
#                  7             4
#                  v      ^      v
#                  |      |      |
#                  |      |      |
#                  |             |
#                  -<6>-------<5>-

uint32 lamp_code #Lamp  word 32 bits; It is divided into 8 partitions, 4 bits for each partition

                 #The partitions are arranged according to the small end, corresponding to the figure above. 
                 #The eighth partition is the script partition
                 #script partition:0--Built in default mode for program,1~14--the mode defined in the script,
                 #At this time, the script_file should give the full path of the script file, otherwise use the default mode;
uint8 BLACK                 = 0     # The vehicle is shut down,                 
uint8 RED_TWINKLE           = 1     # The vehicle is shut down,  
uint8 YELLOW_TWINKLE        = 2     # The vehicle is shut down,  
uint8 GREEN_BREATHE         = 3     # The vehicle is shut down,  
uint8 RED                   = 4     # The vehicle is shut down,  
uint8 GREEN                 = 5     # The vehicle is shut down,  
uint8 YELLOW                = 6     # The vehicle is shut down,  
uint8 CYAN                  = 7     # The vehicle is shut down,  
uint8 WHITE                 = 8     # The vehicle is shut down,  


uint32 BUMPER_TRIGGER           = 0x01111111
                                   #s7654321
uint32 DOOR_ABNORMAL            = 0x01111111
                                   #s7654321
uint32 CHASSIS_OVERCURRENT      = 0x01111111
                                   #s7654321
uint32 EMERGENCY_STOP_PRESSED   = 0x02222222
                                   #s7654321                                  
uint32 FORWARD                  = 0x08888888
                                   #s7654321
uint32 BACKWARD                 = 0x00440000
                                   #s7654321
uint32 RIGHT                    = 0x00022200
                                   #s7654321
uint32 LEFT                     = 0x02200002
                                   #s7654321
uint32 CHARGING                 = 0x00000333
                                   #s7654321
uint32 FULLY_CHARGED            = 0x00000555
                                   #s7654321
uint32 ULTRA_LOW_POWER          = 0x00000444
                                   #s7654321
uint32 LOW_POWER                = 0x00000666
                                   #s7654321
uint32 NORMAL_POWER             = 0x00000777
                                   #s7654321



string script_file