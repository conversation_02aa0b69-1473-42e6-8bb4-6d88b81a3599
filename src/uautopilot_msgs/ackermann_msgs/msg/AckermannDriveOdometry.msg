std_msgs/Header          header
float32 steering_angle          # Feedback  virtual angle (radians)
float32 steering_angle_velocity # Feedback  rate of change (radians/s)


float32 speed                   # Feedback  forward speed (m/s)
float32 acceleration            # Feedback  acceleration (m/s^2)
float32 odometer                # Feedback linear odometer, set to zero every time it is started, with sign, unit: m/s, accuracy: 0.00001m/s

float32 absolute_odometer       # Feedback absolute linear odometer, mileage accumulation after delivery, unsigned, unit: km/s, accuracy: 0.1km/s
