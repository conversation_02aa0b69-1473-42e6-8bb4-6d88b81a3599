std_msgs/Header         header
# This topic shall be reported every 5 seconds, and any status change shall be reported immediately (except battery information)
uint32  steering_status          # Feedback  virtual angle (radians)
string steering_status_describe  # Feedback  rate of change (radians/s)


uint32 drive_motor_status                  # Feedback  forward speed (m/s)
string drive_motor_status_describe            # Feedback  acceleration (m/s^2)


uint8 sw                        # Vehicle start key
uint8 SW_OFF            = 0     # The vehicle is shut down, 
                                    # and all 12V and power batteries are powered off
uint8 SW_STANDBY        = 1     # Start the vehicle, 
                                    # power on the 12v control power supply and power off the power battery
uint8 SW_ON             = 2     # When the vehicle is started, 
                                    # 12v control power supply and power battery are powered on


uint8  brake                                # When reporting, the two signal sources (emergency stop switch and anti-collision strip) are closed by phase or, 
                                            # such as: brake = EMERGENCY_BRAKING | FRONT_BUMPER_BRAKING;

uint8 BRAKE_RELEASE                   = 0     # No brake, driving normally

uint8 BRAKE_EMERGENCY                 = 1     # Press the emergency stop button to trigger the brake

uint8 BRAKE_FRONT_BUMPER              = 2     # Front anti-collision strip triggers the brake

#uint8 FRONT_RIGHT_BUMPER_BRAKING       = 2    # Front right anti-collision strip triggers the brake

#uint8 FRONT_LEFT_BUMPER_BRAKING        = 4    # Front left anti-collision strip triggers the brake

float32 battery_voltage                     # Battery voltage, unit: V

float32 battery_current                     # Battery voltage, unit: A

uint8 battery_soc                         # Battery capacity, unit:%, range: 0~100

uint32 battery_status                       # Battery status code


uint8 BAT_STATUS_IDLE           = 0     # Battery idle
uint8 BAT_STATUS_CHARGING       = 1     # Battery charging
uint8 BAT_STATUS_DISCHARING     = 2     # Battery discharge
uint8 BAT_STATUS_COM_ERR        = 3     # Abnormal battery communication
uint8 BAT_STATUS_FULL           = 4     # Fully charged
uint8 BAT_CHARGER_STATUS_ERR    = 5     # Abnormal charging

float32 battery_temperature             #  unit: ℃

uint8 battery_cell_number

float32[] battery_cell_voltage          # Battery voltage, unit: V

string  battery_info                   

