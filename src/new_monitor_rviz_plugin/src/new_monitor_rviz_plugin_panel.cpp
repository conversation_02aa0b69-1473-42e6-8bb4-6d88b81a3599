#include <stdio.h>
#include <sstream>
#include <QPainter>
#include <QLineEdit>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QTimer>
#include <QDebug>
#include "qwt_plot_curve.h"
#include <qwt_legend.h>
#include "new_monitor_rviz_plugin_panel.hpp"
#include <iostream>
#include <fstream>

namespace new_monitor_rviz_plugin {
typedef unsigned char uint8;

// 构造函数，初始化变量
NEWMonitorRvizPanel::NEWMonitorRvizPanel(QWidget *parent) : rviz_common::Panel(parent)
{
    ui_.setupUi(this);
}

void NEWMonitorRvizPanel::onInitialize()
{
    auto ros_node_abstraction = (getDisplayContext()->getRosNodeAbstraction()).lock();
    rclcpp::Node::SharedPtr node = ros_node_abstraction->get_raw_node();

    //monitor监控模块结果sub
    sub_monitormanager_status_ = node->template create_subscription<monitor_msgs::msg::MonitorManagerMsg>(
        "/uslam/monitor_manager/monitor_status", 1,
        std::bind(&NEWMonitorRvizPanel::monitorManagerMsgCallback, this, std::placeholders::_1));
}

void NEWMonitorRvizPanel::monitorManagerMsgCallback(const monitor_msgs::msg::MonitorManagerMsg::SharedPtr msg)
{
    if (!msg->pro_contents.empty()) {
        // update process name first
        if (checkProcessInfo(msg)) {
            updateProcessName(msg);
            updateProcessRunningState(msg);
        }

        updateTable(msg);
        updateQwtPlot(msg);
    }
}

bool NEWMonitorRvizPanel::checkProcessInfo(const monitor_msgs::msg::MonitorManagerMsg::SharedPtr msg)
{
    if (msg->pro_contents.size() != last_monitormanager_msg_.pro_contents.size()) {
        std::cout << "Process Info was change" << std::endl;
        last_monitormanager_msg_ = *msg;
        return true;
    } else {
        for (int i = 0; i < msg->pro_contents.size(); i++) {
            if (msg->pro_contents[i].process_info != last_monitormanager_msg_.pro_contents[i].process_info) {
                std::cout << "Process Info was change" << std::endl;
                last_monitormanager_msg_ = *msg;
                return true;
            }
        }
    }
    return false;
}

void NEWMonitorRvizPanel::updateProcessName(const monitor_msgs::msg::MonitorManagerMsg::SharedPtr msg)
{
    std::cout << "Update Process Name" << std::endl;
    ui_.tableWidget->setRowCount(msg->pro_contents.size());
    module_name_row_map_.clear();
    int row_count = 0;
    for (const auto process : msg->pro_contents) {
        std::cout << "module_name: " << process.process_info.name << std::endl;
        module_name_row_map_[process.process_info.name] = row_count;
        ui_.tableWidget->setItem(row_count, 0, new QTableWidgetItem(process.process_info.name.c_str()));
        row_count++;
    }
}

void NEWMonitorRvizPanel::updateProcessRunningState(const monitor_msgs::msg::MonitorManagerMsg::SharedPtr msg)
{
    for (const auto procontent : msg->pro_contents) {
        int row = module_name_row_map_[procontent.process_info.name];
        std::string state_desc = procontent.process_info.runstate;

        QTableWidgetItem *table_item = new QTableWidgetItem(state_desc.c_str());
        table_item->setTextAlignment(Qt::AlignCenter);
        ui_.tableWidget->setItem(row, 1, table_item);
    }
}

void NEWMonitorRvizPanel::updateQwtPlot(const monitor_msgs::msg::MonitorManagerMsg::SharedPtr msg)
{
    int horizon_size = 30;

    module_cpu_resource_.resize(msg->pro_contents.size());
    for (const auto procontent : msg->pro_contents) {
        int row = module_name_row_map_[procontent.process_info.name];
        module_cpu_resource_[row].push_back(procontent.resource_status.cpu_usage);
        if (module_cpu_resource_[row].size() > 2 * horizon_size) {
            module_cpu_resource_[row].erase(module_cpu_resource_[row].begin(),
                                            module_cpu_resource_[row].begin() + horizon_size);
        }
    }

    if (time_sequence_.empty()) {
        time_sequence_.push_back(0.0);
    } else {
        double next_time = time_sequence_.back() + 1.0;
        time_sequence_.push_back(next_time);
    }

    if (time_sequence_.size() > 2 * horizon_size) {
        time_sequence_.erase(time_sequence_.begin(), time_sequence_.begin() + horizon_size);
    }

    ui_.qwtPlot->detachItems();
    for (int i = 0; i < module_cpu_resource_.size(); i++) {
        //如果ROW_COLOR_MAP没有键值i, 直接使用ROW_COLOR_MAP.at(0)的颜色
        QColor color = ROW_COLOR_MAP.count(i) == 0 ? ROW_COLOR_MAP.at(0) : ROW_COLOR_MAP.at(i);
        QPen pen(color);
        pen.setWidth(2);
        QString module_name = ui_.tableWidget->item(i, 0)->text();
        QwtPlotCurve *curve = new QwtPlotCurve(module_name);  // create a curve
        curve->setPen(pen);
        if (time_sequence_.size() < horizon_size) {
            curve->setSamples(time_sequence_.data(), module_cpu_resource_[i].data(),
                              time_sequence_.size());  // set the data to the curve
        } else {
            curve->setSamples(time_sequence_.data() + time_sequence_.size() - horizon_size,
                              module_cpu_resource_[i].data() + module_cpu_resource_[i].size() - horizon_size,
                              horizon_size);  // set the data to the curve
        }
        curve->attach(ui_.qwtPlot);  // attach the curve to the plot
    }

    QwtLegend *legend = new QwtLegend;
    ui_.qwtPlot->insertLegend(legend, QwtPlot::RightLegend);
    ui_.qwtPlot->replot();  // finally redraw the plot
}

void NEWMonitorRvizPanel::updateTable(const monitor_msgs::msg::MonitorManagerMsg::SharedPtr msg)
{
    ui_.tableWidget->setRowCount(msg->pro_contents.size());
    for (const auto pro_content : msg->pro_contents) {
        int row = module_name_row_map_[pro_content.process_info.name];

        ui_.tableWidget->setItem(row, 0, new QTableWidgetItem(pro_content.process_info.name.c_str()));
        ui_.tableWidget->item(row, 0)->setTextAlignment(Qt::AlignCenter);

        if (pro_content.resource_status.resource_status == 1) {
            ui_.tableWidget->item(row, 0)->setBackground(Qt::green);
        } else {
            ui_.tableWidget->item(row, 0)->setBackground(Qt::red);
        }

        std::string channel_status;
        if (pro_content.channel_status.channel_status == monitor_msgs::msg::MonitorStatus::OK) {
            channel_status = status_str[pro_content.channel_status.channel_status];
        } else {
            channel_status =
                status_str[pro_content.channel_status.channel_status] + ":" + pro_content.channel_status.channel_msg;
        }
        ui_.tableWidget->setItem(row, 2, new QTableWidgetItem(channel_status.c_str()));
        ui_.tableWidget->item(row, 2)->setTextAlignment(Qt::AlignCenter);

        std::string resource_status;
        if (pro_content.resource_status.resource_status == monitor_msgs::msg::MonitorStatus::OK) {
            resource_status = status_str[pro_content.resource_status.resource_status];
        } else {
            resource_status =
                status_str[pro_content.resource_status.resource_status] + ":" + pro_content.resource_status.resource_msg;
        }
        ui_.tableWidget->setItem(row, 3, new QTableWidgetItem(resource_status.c_str()));
        ui_.tableWidget->item(row, 3)->setTextAlignment(Qt::AlignCenter);

        ui_.tableWidget->setItem(row, 4,
                                 new QTableWidgetItem(std::to_string(pro_content.resource_status.cpu_usage).c_str()));
        ui_.tableWidget->setItem(
            row, 5, new QTableWidgetItem(std::to_string(pro_content.resource_status.memory_usage).c_str()));
        ui_.tableWidget->item(row, 4)->setTextAlignment(Qt::AlignCenter);
        ui_.tableWidget->item(row, 5)->setTextAlignment(Qt::AlignCenter);
    }
}
// 重载父类的功能
void NEWMonitorRvizPanel::save(rviz_common::Config config) const
{
    rviz_common::Panel::save(config);
    // config.mapSetValue("Topic", output_topic_);
}

// 重载父类的功能，加载配置数据
void NEWMonitorRvizPanel::load(const rviz_common::Config &config)
{
    rviz_common::Panel::load(config);
    // QString topic;
    // if (config.mapGetString("Topic", &topic)) {
    //     output_topic_editor_->setText(topic);
    //     updateTopic();
    // }
}

void NEWMonitorRvizPanel::resizeEvent(QResizeEvent *event)
{
    // 获取窗口大小
    QSize windowSize = size();
    // 调用基类的 resizeEvent 函数
    rviz_common::Panel::resizeEvent(event);
    ui_.gridLayoutWidget->setGeometry(0, 0, windowSize.width(), windowSize.height());
}

}  // namespace new_monitor_rviz_plugin

// 声明此类是一个rviz的插件
#include <pluginlib/class_list_macros.hpp>  // NOLINT
PLUGINLIB_EXPORT_CLASS(new_monitor_rviz_plugin::NEWMonitorRvizPanel, rviz_common::Panel)
