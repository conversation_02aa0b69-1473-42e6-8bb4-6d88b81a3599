<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>768</width>
    <height>535</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QWidget" name="gridLayoutWidget">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>10</y>
     <width>751</width>
     <height>521</height>
    </rect>
   </property>
   <layout class="QGridLayout" name="gridLayout">
    <property name="leftMargin">
     <number>10</number>
    </property>
    <property name="topMargin">
     <number>10</number>
    </property>
    <property name="rightMargin">
     <number>10</number>
    </property>
    <property name="bottomMargin">
     <number>10</number>
    </property>
    <item row="0" column="0">
     <widget class="QTableWidget" name="tableWidget">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="autoFillBackground">
       <bool>false</bool>
      </property>
      <property name="sizeAdjustPolicy">
       <enum>QAbstractScrollArea::AdjustToContents</enum>
      </property>
      <property name="autoScroll">
       <bool>true</bool>
      </property>
      <property name="editTriggers">
       <set>QAbstractItemView::NoEditTriggers</set>
      </property>
      <property name="alternatingRowColors">
       <bool>true</bool>
      </property>
      <property name="textElideMode">
       <enum>Qt::ElideNone</enum>
      </property>
      <property name="showGrid">
       <bool>true</bool>
      </property>
      <property name="gridStyle">
       <enum>Qt::SolidLine</enum>
      </property>
      <property name="sortingEnabled">
       <bool>false</bool>
      </property>
      <attribute name="horizontalHeaderCascadingSectionResizes">
       <bool>false</bool>
      </attribute>
      <attribute name="horizontalHeaderMinimumSectionSize">
       <number>80</number>
      </attribute>
      <attribute name="horizontalHeaderDefaultSectionSize">
       <number>120</number>
      </attribute>
      <attribute name="horizontalHeaderShowSortIndicator" stdset="0">
       <bool>false</bool>
      </attribute>
      <attribute name="horizontalHeaderStretchLastSection">
       <bool>false</bool>
      </attribute>
      <attribute name="verticalHeaderCascadingSectionResizes">
       <bool>false</bool>
      </attribute>
      <column>
       <property name="text">
        <string>module name</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>module status</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>channel status</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>resource status</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>CPU(%)</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>MEM(GB)</string>
       </property>
      </column>
     </widget>
    </item>
    <item row="1" column="0">
     <widget class="QwtPlot" name="qwtPlot">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QwtPlot</class>
   <extends>QFrame</extends>
   <header>qwt_plot.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
