#ifndef NEW_MONITOR_RVIZ_PLUGIN_PANEL_H_
#define NEW_MONITOR_RVIZ_PLUGIN_PANEL_H_

//所需要包含的头文件
#include <QCursor>  // NOLINT cpplint cannot handle the include order here
#include <QObject>  // NOLINT cpplint cannot handle the include order here

#include "rclcpp/rclcpp.hpp"
#include "rclcpp/qos.hpp"
#include "rviz_common/panel.hpp"

#include "new_monitor_rviz_plugin.h"

#include "monitor_msgs/msg/monitor_status.hpp"
#include "monitor_msgs/msg/monitor_manager_msg.hpp"
#include "rviz_common/display_context.hpp"
#include <json/json.h>

const std::map<int, QColor> ROW_COLOR_MAP = {
    {0, Qt::black},      {1, Qt::red},       {2, Qt::green},        {3, Qt::blue},        {4, Qt::cyan},
    {5, Qt::magenta},    {6, Qt::yellow},    {7, Qt::gray},         {8, Qt::darkRed},     {9, Qt::darkGreen},
    {10, Qt::darkBlue},  {11, Qt::darkCyan}, {12, Qt::darkMagenta}, {13, Qt::darkYellow}, {14, Qt::darkGray},
    {15, Qt::lightGray}, {16, Qt::white}};

class QLineEdit;

namespace new_monitor_rviz_plugin {
// 所有的plugin都必须是rviz::Panel的子类
class NEWMonitorRvizPanel : public rviz_common::Panel
{
    // 后边需要用到Qt的信号和槽，都是QObject的子类，所以需要声明Q_OBJECT宏
    Q_OBJECT
public:
    // 构造函数，在类中会用到QWidget的实例来实现GUI界面，这里先初始化为0即可
    NEWMonitorRvizPanel(QWidget *parent = 0);

    // 重载rviz::Panel积累中的函数，用于保存、加载配置文件中的数据，在我们这个plugin
    // 中，数据就是topic的名称
    virtual void load(const rviz_common::Config &config);
    virtual void save(rviz_common::Config config) const;

    void onInitialize() override;

    // 内部槽.
protected Q_SLOTS:
    // 在窗口大小变化时触发
    void resizeEvent(QResizeEvent *event);

private:
    Ui::Form ui_;
    std::shared_ptr<rclcpp::Node> node_;

    void monitorManagerMsgCallback(const monitor_msgs::msg::MonitorManagerMsg::SharedPtr msg);

    bool checkProcessInfo(const monitor_msgs::msg::MonitorManagerMsg::SharedPtr msg);
    void updateProcessName(const monitor_msgs::msg::MonitorManagerMsg::SharedPtr msg);
    void updateProcessRunningState(const monitor_msgs::msg::MonitorManagerMsg::SharedPtr msg);

    void updateTable(const monitor_msgs::msg::MonitorManagerMsg::SharedPtr msg);
    void updateQwtPlot(const monitor_msgs::msg::MonitorManagerMsg::SharedPtr msg);

    // Subscribers
    rclcpp::Subscription<monitor_msgs::msg::MonitorManagerMsg>::SharedPtr sub_monitormanager_status_;
    monitor_msgs::msg::MonitorManagerMsg last_monitormanager_msg_;

    std::map<std::string, int> module_name_row_map_;
    std::string status_str[6] = {"UNKNOWN", "OK", "INFO", "WARN", "ERROR", "FATAL"};

    std::vector<std::vector<double>> module_cpu_resource_;
    std::vector<double> time_sequence_;
};

}  // namespace new_monitor_rviz_plugin

#endif  // NEW_MONITOR_RVIZ_PLUGIN_PANEL_H_
