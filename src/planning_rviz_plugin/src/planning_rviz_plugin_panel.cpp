#include "planning_rviz_plugin_panel.hpp"

#include <stdio.h>
#include <tf2/utils.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>

#include <QDebug>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QPainter>
#include <QTimer>
#include <QVBoxLayout>
#include <fstream>
// #include <geometry_msgs/msg/twist.hpp>
#include <iostream>
#include <sstream>

#include "qwt_plot.h"
#include <qwt_plot_grid.h>
#include <qwt_plot_shapeitem.h>
#include <qwt_plot_textlabel.h>
#include <qwt_text.h>
#include <qwt_plot_marker.h>
#include <qwt_legend.h>
#include <qwt_symbol.h>
#include <json/json.h>

namespace planning_rviz_plugin {
typedef unsigned char uint8;

// 构造函数，初始化变量
PlanningRvizPanel::PlanningRvizPanel(QWidget *parent) : rviz_common::Panel(parent)
{
    ui_.setupUi(this);
    // connect(ui_.start_mapping_btn, SIGNAL(clicked()), this, SLOT(clickStartMapping()));
    // connect(ui_.save_mapping_btn, SIGNAL(clicked()), this, SLOT(clickSaveMapping()));
    // connect(ui_.cancel_mapping_btn, SIGNAL(clicked()), this, SLOT(clickCancelMapping()));

    // connect(ui_.manual_relocate_btn, SIGNAL(clicked()), this, SLOT(clickManualRelocate()));
    // connect(ui_.gps_relocate_btn, SIGNAL(clicked()), this, SLOT(clickGpsRelocate()));
    // connect(ui_.cancel_relocate_btn, SIGNAL(clicked()), this, SLOT(clickCancelRelocate()));

    // connect(ui_.routing_planning_btn, SIGNAL(clicked()), this, SLOT(clickRoutingPlanning()));

    // connect(ui_.free_navigation_btn, SIGNAL(clicked()), this, SLOT(clickFreeNavigation()));
    // connect(ui_.cancel_navigation_btn, SIGNAL(clicked()), this, SLOT(clickCancelNavigation()));
    // connect(ui_.checkBox_engage, SIGNAL(clicked()), this, SLOT(clickEngageCheckBox()));
    // connect(ui_.save_goal_btn, SIGNAL(clicked()), this, SLOT(clickSaveGoalBtn()));

    // connect(ui_.goal_pose_table, SIGNAL(itemClicked(QTableWidgetItem *)), this, SLOT(clickGoalPoseTable(QTableWidgetItem *)));
    // connect(ui_.planning_selected_goal_btn, SIGNAL(clicked()), this, SLOT(clickPlanningSelectedGoalBtn()));
    // connect(ui_.delete_selected_goal_btn, SIGNAL(clicked()), this, SLOT(clickDeleteSelectedGoalBtn()));
}

void PlanningRvizPanel::onInitialize()
{
    auto ros_node_abstraction = (getDisplayContext()->getRosNodeAbstraction()).lock();
    rclcpp::Node::SharedPtr node = ros_node_abstraction->get_raw_node();

    sub_traj_list_.push_back(node->template create_subscription<custom_msgs::msg::Trajectory>(
        "/planning/trajectory", 1, [this](const custom_msgs::msg::Trajectory::ConstSharedPtr msg) {
            trajCallback(msg, "final_planning", QColor(0, 255, 0, 150), 5);
        }));

    sub_traj_list_.push_back(node->template create_subscription<custom_msgs::msg::Trajectory>(
        "/debug/path_planner/trajectory", 1, [this](const custom_msgs::msg::Trajectory::ConstSharedPtr msg) {
            trajCallback(msg, "path_planner", QColor(0, 0, 255, 180));
        }));

    sub_traj_list_.push_back(node->template create_subscription<custom_msgs::msg::Trajectory>(
        "/debug/velocity_planner/trajectory", 1, [this](const custom_msgs::msg::Trajectory::ConstSharedPtr msg) {
            trajCallback(msg, "velocity_planner", QColor(255, 0, 255, 180));
        }));

    sub_traj_list_.push_back(node->template create_subscription<custom_msgs::msg::Trajectory>(
        "/debug/path_planner/backup_trajectory", 1, [this](const custom_msgs::msg::Trajectory::ConstSharedPtr msg) {
            trajCallback(msg, "backup_trajectory", QColor(0, 255, 255, 180), 4);
        }));

    sub_traj_list_.push_back(node->template create_subscription<custom_msgs::msg::Trajectory>(
        "debug/smoother/trajectory_smoothed", 1, [this](const custom_msgs::msg::Trajectory::ConstSharedPtr msg) {
            trajCallback(msg, "velocity_smoothed", QColor(255, 0, 0, 180));
        }));

    sub_traj_list_.push_back(node->template create_subscription<custom_msgs::msg::Trajectory>(
        "debug/stl_combine_trajectory", 1, [this](const custom_msgs::msg::Trajectory::ConstSharedPtr msg) {
            trajCallback(msg, "stl_combined", QColor(0, 0, 0, 180), 5);
        }));

    // sub_goal_pose_ = node->template create_subscription<geometry_msgs::msg::PoseStamped>("/uslam/hmi/goal_pose", 1,
    // std::bind(&PlanningRvizPanel::goalPoseCallback, this, std::placeholders::_1)); sub_initial_pose_ = node->template
    // create_subscription<geometry_msgs::msg::PoseWithCovarianceStamped>("/uslam/hmi/initial_pose", 1,
    // std::bind(&PlanningRvizPanel::initialPoseCallback, this, std::placeholders::_1)); sub_task_response_ =
    sub_lattice_string_ = node->template create_subscription<std_msgs::msg::String>(
        "/debug/path_planner/string", 1, std::bind(&PlanningRvizPanel::latticeStrCallback, this, std::placeholders::_1));
    sub_behavior_string_ = node->template create_subscription<std_msgs::msg::String>(
        "/planning/behavior_decision_string", 1,
        std::bind(&PlanningRvizPanel::behaviorStrCallback, this, std::placeholders::_1));
    sub_cmd_ = node->template create_subscription<ackermann_msgs::msg::AckermannDriveStamped>(
        "/uslam/monitor/ackermann_cmd_vel", 1, std::bind(&PlanningRvizPanel::cmdCallback, this, std::placeholders::_1));
    sub_control_cmd_ = node->template create_subscription<ackermann_msgs::msg::AckermannDriveStamped>(
        "/uslam/control/ackermann_cmd_vel", 1,
        std::bind(&PlanningRvizPanel::controlCmdCallback, this, std::placeholders::_1));
    sub_odom_ = node->template create_subscription<ackermann_msgs::msg::AckermannDriveOdometry>(
        "/ackermann/odom", 1, std::bind(&PlanningRvizPanel::odomCallback, this, std::placeholders::_1));
    // sub_odom_= node->template create_subscription<nav_msgs::msg::Odometry>("/odom", 1,
    // std::bind(&PlanningRvizPanel::odomCallback, this, std::placeholders::_1)); sub_battrey_ = node->template
    // create_subscription<ackermann_msgs::msg::AckermannDriveStatus>("/ackermann/status", 1,
    // std::bind(&PlanningRvizPanel::batteryCallback, this, std::placeholders::_1)); sub_joy_ = node->template
    // create_subscription<ackermann_msgs::msg::AckermannDriveOdometry>("/joy_speed", 1,
    // std::bind(&PlanningRvizPanel::joyCallback, this, std::placeholders::_1)); sub_horn_ = node->template
    // create_subscription<chitu_msgs::msg::Horn>("/sensor/horn/set", 1, std::bind(&PlanningRvizPanel::hornCallback,
    // this, std::placeholders::_1));

    // rclcpp::QoS qos_profile(10);
    // qos_profile.reliable().transient_local();
    // //订阅monitor模块启动时使用的HMI工作模式
    // sub_hmi_mode_ = node->template create_subscription<custom_msgs::msg::HmiMode>("/uslam/monitor/hmi_mode",
    // qos_profile, std::bind(&PlanningRvizPanel::hmiModeCallback, this, std::placeholders::_1));

    // //monitor模块监控的状态
    // sub_system_status_ = node->template create_subscription<custom_msgs::msg::SystemStatus>("/uslam/monitor/system_status",
    // 1, std::bind(&PlanningRvizPanel::systemStatusCallback, this, std::placeholders::_1));

    // //模块状态更新频繁，单独发布统一话题 /uslam/module_state
    // sub_module_state_ = node->template create_subscription<umodule_msgs::msg::ModuleState>("/uslam/module_state",
    // qos_profile, std::bind(&PlanningRvizPanel::moduleStateCallback, this, std::placeholders::_1));

    // pub_task_request_ = node->template create_publisher<std_msgs::msg::String>("/uslam/task/request", 10);
    // pub_engaged_ = node->template create_publisher<std_msgs::msg::Bool>("/uslam/hmi/engaged", 1);
    // QColor color;
    // color.setRgb(255, 0, 0);
    // ui_.debug_info_text_browser->setTextColor(color);

    // goal_file_ = "/home/<USER>/goal.json";
    // selected_goal_name_ = "";
    // loadGoalFile();
    // ui_.label_joy->setVisible(false);
}

void PlanningRvizPanel::trajCallback(const custom_msgs::msg::Trajectory::ConstSharedPtr msg, std::string name,
                                     QColor color, int pen_width)
{
    // ui_.qwtPlot->detachItems();
    // if (curve_list_[name] != nullptr) {
    //     curve_list_[name]->detach();
    //     delete curve_list_[name];
    //     curve_list_[name] = nullptr;
    // }
    // std::cout << "Get Traj " << name << " size:" << msg->points.size() << std::endl;
    if (ui_.qwtPlot->title().isEmpty()) {
        ui_.qwtPlot->setTitle("");
        ui_.qwtPlot->setAxisScale(QwtPlot::xBottom, 0.0, 40.0);
        ui_.qwtPlot->setAxisScale(QwtPlot::yLeft, -1.0, 5.0);

        ui_.qwtPlot->enableAxis(QwtPlot::xBottom, true);
        ui_.qwtPlot->enableAxis(QwtPlot::xTop, false);
        ui_.qwtPlot->enableAxis(QwtPlot::yLeft, true);
        ui_.qwtPlot->enableAxis(QwtPlot::yRight, false);
    }

    std::vector<double> xData, yData;
    double s_sum = 0;
    double y_max = 0;
    for (int i = 0; i < msg->points.size(); i++) {
        if (i != 0) {
            double dis = std::hypot(msg->points[i].pose.position.x - msg->points[i - 1].pose.position.x,
                                    msg->points[i].pose.position.y - msg->points[i - 1].pose.position.y);

            s_sum += dis;
        }
        xData.push_back(s_sum);
        yData.push_back(msg->points[i].longitudinal_velocity_mps);
        if (msg->points[i].longitudinal_velocity_mps > y_max) y_max = msg->points[i].longitudinal_velocity_mps;
    }

    if (curve_list_[name] == nullptr) {
        QwtPlotCurve *curve = new QwtPlotCurve(name.c_str());
        QPen pen;
        pen.setColor(color);
        pen.setWidth(pen_width);  // 设置线条宽度
        curve->setPen(pen);
        curve->setSamples(xData.data(), yData.data(), yData.size());
        curve->attach(ui_.qwtPlot);
        curve_list_[name] = curve;
    } else {
        curve_list_[name]->setSamples(xData.data(), yData.data(), yData.size());
    }

    if (!ui_.qwtPlot->legend()) {
        QwtLegend *legend = new QwtLegend;
        ui_.qwtPlot->insertLegend(legend, QwtPlot::RightLegend);
    }

    ui_.qwtPlot->replot();
}

void PlanningRvizPanel::latticeStrCallback(const std_msgs::msg::String::SharedPtr msg)
{
    ui_.qwtPlot_2->detachItems();
    ui_.qwtPlot_2->setTitle("");
    ui_.qwtPlot_4->detachItems();
    ui_.qwtPlot_4->setTitle("");

    Json::Reader reader;
    Json::Value root;
    try {
        if (reader.parse(msg->data, root)) {
            {
                // final_lat_traj[size:pt_size][size:4] 0-s 1-l 2-dl 3-ddl
                Json::Value lat_trajs = root["lat_traj"];
                for (int i = 0; i < lat_trajs.size(); i++) {
                    auto &lat_traj = lat_trajs[i];
                    std::vector<double> xData, yData;
                    for (int i = 0; i < lat_traj.size(); i++) {
                        if (lat_traj[i].size() == 4) {
                            xData.push_back(lat_traj[i][0].asDouble());
                            yData.push_back(lat_traj[i][1].asDouble());
                        }
                    }

                    QwtPlotCurve *curve = new QwtPlotCurve((std::string("lat_traj") + std::to_string(i)).c_str());
                    QPen pen(Qt::blue);
                    pen.setWidth(3);  // 设置线条宽度
                    curve->setPen(pen);
                    curve->setSamples(xData.data(), yData.data(), yData.size());
                    curve->attach(ui_.qwtPlot_2);
                }
            }

            {
                // final_lat_traj[size:pt_size][size:4] 0-s 1-l 2-dl 3-ddl
                Json::Value lon_trajs = root["lon_traj"];
                for (int i = 0; i < lon_trajs.size(); i++) {
                    auto &lon_traj = lon_trajs[i];
                    std::vector<double> xData, yData;
                    for (int i = 0; i < lon_traj.size(); i++) {
                        if (lon_traj[i].size() == 4) {
                            xData.push_back(lon_traj[i][0].asDouble());
                            yData.push_back(lon_traj[i][1].asDouble());
                        }
                    }

                    QwtPlotCurve *curve = new QwtPlotCurve((std::string("lon_traj") + std::to_string(i)).c_str());
                    QPen pen(Qt::blue);
                    pen.setWidth(3);  // 设置线条宽度
                    curve->setPen(pen);
                    curve->setSamples(xData.data(), yData.data(), yData.size());
                    curve->attach(ui_.qwtPlot_4);
                }
            }

            {
                // final_lat_traj[size:pt_size][size:4] 0-s 1-l 2-dl 3-ddl
                Json::Value lon_trajs = root["first_cost_lon_traj"];
                for (int i = 0; i < lon_trajs.size(); i++) {
                    auto &lon_traj = lon_trajs[i];
                    std::vector<double> xData, yData;
                    for (int i = 0; i < lon_traj.size(); i++) {
                        if (lon_traj[i].size() == 4) {
                            xData.push_back(lon_traj[i][0].asDouble());
                            yData.push_back(lon_traj[i][1].asDouble());
                        }
                    }

                    QwtPlotCurve *curve =
                        new QwtPlotCurve((std::string("first_cost_lon_traj") + std::to_string(i)).c_str());
                    QPen pen(Qt::red);
                    pen.setWidth(3);  // 设置线条宽度
                    curve->setPen(pen);
                    curve->setSamples(xData.data(), yData.data(), yData.size());
                    curve->attach(ui_.qwtPlot_4);
                }
            }

            {
                // final_lat_traj[size:pt_size][size:4] 0-s 1-l 2-dl 3-ddl
                Json::Value lat_trajs = root["backup_lat_traj"];
                for (int i = 0; i < lat_trajs.size(); i++) {
                    auto &lat_traj = lat_trajs[i];
                    std::vector<double> xData, yData;
                    for (int i = 0; i < lat_traj.size(); i++) {
                        if (lat_traj[i].size() == 4) {
                            xData.push_back(lat_traj[i][0].asDouble());
                            yData.push_back(lat_traj[i][1].asDouble());
                        }
                    }

                    QwtPlotCurve *curve = new QwtPlotCurve((std::string("backup_lat_traj") + std::to_string(i)).c_str());
                    QPen pen(Qt::red);
                    pen.setWidth(3);  // 设置线条宽度
                    curve->setPen(pen);
                    curve->setSamples(xData.data(), yData.data(), yData.size());
                    curve->attach(ui_.qwtPlot_2);
                }
            }

            {
                // lat_bounds[size:bound_size] "s" "lower" "upper"
                Json::Value lat_bounds = root["lat_bounds"];

                std::vector<double> xData, yData_lower, yData_upper, yData_pt;
                for (int i = 0; i < lat_bounds.size(); i++) {
                    xData.push_back(lat_bounds[i]["s"].asDouble());
                    yData_lower.push_back(lat_bounds[i]["lower"].asDouble());
                    yData_upper.push_back(lat_bounds[i]["upper"].asDouble());
                    yData_pt.push_back(lat_bounds[i]["ref_pt"].asDouble());
                }

                QPen pen(Qt::green);
                pen.setWidth(2);
                QwtPlotCurve *curve1 = new QwtPlotCurve("lat_bounds_lower");
                curve1->setPen(pen);
                curve1->setSamples(xData.data(), yData_lower.data(), yData_lower.size());
                curve1->attach(ui_.qwtPlot_2);

                QwtPlotCurve *curve2 = new QwtPlotCurve("lat_bounds_upper");
                curve2->setPen(pen);
                curve2->setSamples(xData.data(), yData_upper.data(), yData_upper.size());
                curve2->attach(ui_.qwtPlot_2);

                QwtPlotCurve *curve3 = new QwtPlotCurve("lat_ref_pt");
                pen.setColor(Qt::gray);
                curve3->setPen(pen);
                curve3->setSamples(xData.data(), yData_pt.data(), yData_pt.size());
                curve3->attach(ui_.qwtPlot_2);
            }

            // B样条拟合轨迹
            {
                Json::Value bspline_trajs = root["bspline_traj"];
                std::vector<double> xData, yData;
                for (int i = 0; i < bspline_trajs.size(); ++i) {
                    auto &bspline_traj = bspline_trajs[i];
                    xData.push_back(bspline_traj["s"].asDouble());
                    yData.push_back(bspline_traj["l"].asDouble());
                }
                QwtPlotCurve *curve = new QwtPlotCurve(QString::fromStdString("bspline_traj"));
                QPen pen(Qt::black);
                pen.setWidth(2);  // 设置线条宽度
                curve->setPen(pen);
                curve->setSamples(xData.data(), yData.data(), yData.size());
                curve->attach(ui_.qwtPlot_2);
            }

            {
                // sl_obstacles[size:obstacle_size] "start_s" "end_s" "start_l" "end_l"
                Json::Value sl_obstacles = root["sl_obstacles"];
                // std::cout << "size:" << sl_obstacles.size() << std::endl;
                for (int i = 0; i < sl_obstacles.size(); i++) {
                    double start_s = sl_obstacles[i]["start_s"].asDouble();
                    double end_s = sl_obstacles[i]["end_s"].asDouble();
                    double start_l = sl_obstacles[i]["start_l"].asDouble();
                    double end_l = sl_obstacles[i]["end_l"].asDouble();
                    std::string id = sl_obstacles[i]["id"].asString();
                    std::string type = sl_obstacles[i]["type"].asString();
                    QRectF box(start_s, start_l, end_s - start_s, end_l - start_l);
                    QwtPlotShapeItem *rectangleItem =
                        new QwtPlotShapeItem((std::string("obs") + std::to_string(i)).c_str());
                    rectangleItem->setPen(Qt::darkGray);  // 设置边框颜色
                    if (type == "drivable_space") {
                        rectangleItem->setBrush(Qt::darkGreen);  // 设置填充颜色
                    } else if (type == "cluster") {
                        rectangleItem->setBrush(Qt::darkGray);  // 设置填充颜色
                    } else {
                        rectangleItem->setBrush(Qt::yellow);  // 设置填充颜色
                    }
                    rectangleItem->setRect(box);
                    rectangleItem->attach(ui_.qwtPlot_2);
                    QwtText id_text(QString::fromStdString(id));
                    id_text.setColor(Qt::red);
                    auto text_pose_left_x =
                        std::max(double(ui_.qwtPlot_2->contentsRect().bottomLeft().x()), box.topLeft().x());
                    auto text_pose_right_x =
                        std::min(double(ui_.qwtPlot_2->contentsRect().bottomRight().x()), box.topRight().x());
                    QPointF textPos((text_pose_left_x + text_pose_right_x) / 2.0,
                                    std::min(double(ui_.qwtPlot_2->contentsRect().center().y()), box.center().y()));  // 设置位置
                    QwtPlotMarker *marker = new QwtPlotMarker();
                    marker->setLabelAlignment(Qt::AlignHCenter | Qt::AlignVCenter);  // 设置文本对齐方式
                    marker->setLabel(id_text);                                       // 设置要显示的文本
                    marker->setValue(textPos);                                       // 设置文本要显示的坐标
                    marker->attach(ui_.qwtPlot_2);                                   // 将marker附加到plot上
                }
            }

            {
                double max_end_s = std::numeric_limits<double>::min();
                // sl_obstacles[size:obstacle_size] "start_s" "end_s" "start_l" "end_l"
                Json::Value st_obstacles = root["st_obstacles"];
                // std::cout << "st_obstacles size:" << st_obstacles.size() << std::endl;
                for (int i = 0; i < st_obstacles.size(); i++) {
                    std::string id = st_obstacles[i]["id"].asString();

                    // QRectF box(start_s, start_l, end_s - start_s, end_l - start_l);
                    QPointF upper_left(st_obstacles[i]["upper_left_t"].asDouble(),
                                       st_obstacles[i]["upper_left_s"].asDouble());
                    QPointF bottom_left(st_obstacles[i]["bottom_left_t"].asDouble(),
                                        st_obstacles[i]["bottom_left_s"].asDouble());
                    QPointF upper_right(st_obstacles[i]["upper_right_t"].asDouble(),
                                        st_obstacles[i]["upper_right_s"].asDouble());
                    QPointF bottom_right(st_obstacles[i]["bottom_right_t"].asDouble(),
                                         st_obstacles[i]["bottom_right_s"].asDouble());
                    QPolygonF polygon;
                    polygon << upper_left << bottom_left << bottom_right << upper_right;
                    QwtPlotShapeItem *rectangleItem =
                        new QwtPlotShapeItem((std::string("st_obs") + std::to_string(i)).c_str());
                    rectangleItem->setPen(Qt::darkGray);  // 设置边框颜色
                    rectangleItem->setBrush(Qt::green);   // 设置填充颜色
                    rectangleItem->setPolygon(polygon);
                    rectangleItem->attach(ui_.qwtPlot_4);
                    QwtText id_text(QString::fromStdString(id));
                    id_text.setColor(Qt::yellow);
                    QFont font = id_text.font();
                    font.setBold(true);  // 设置字体加粗
                    id_text.setFont(font);
                    QPointF textPos(
                        std::min(double(ui_.qwtPlot_4->contentsRect().center().x()), polygon.boundingRect().center().x()),
                        std::min(double(ui_.qwtPlot_4->contentsRect().center().y()),
                                 polygon.boundingRect().center().y()));  // 设置位置
                    QwtPlotMarker *marker = new QwtPlotMarker();
                    marker->setLabelAlignment(Qt::AlignHCenter | Qt::AlignVCenter);  // 设置文本对齐方式
                    marker->setLabel(id_text);                                       // 设置要显示的文本
                    marker->setValue(textPos);                                       // 设置文本要显示的坐标
                    marker->attach(ui_.qwtPlot_4);                                   // 将marker附加到plot上
                }
            }

            // ST图采样点
            {
                Json::Value st_end_conditions = root["st_end_conditions"];
                for (int i = 0; i < st_end_conditions.size(); ++i) {
                    double s_condition = st_end_conditions[i]["s"].asDouble();
                    double t_condition = st_end_conditions[i]["t"].asDouble();
                    QwtPlotMarker *marker = new QwtPlotMarker();
                    marker->setValue(t_condition, s_condition);
                    marker->setSymbol(new QwtSymbol(QwtSymbol::Ellipse, Qt::red, QPen(Qt::black), QSize(10, 10)));
                    marker->attach(ui_.qwtPlot_4);
                }
            }

            {
                Json::Value text = root["text"];
                ui_.label->setText(QString::fromStdString(text.asString()));
                // std::cout << "lattice_str: " << text << std::endl;
            }
        }

    } catch (std::exception &ex) {
        std::cout << "Exception: " << ex.what() << std::endl;
    }

    ui_.qwtPlot_2->setAxisScale(QwtPlot::xBottom, -5.0, 40.0);
    ui_.qwtPlot_2->setAxisAutoScale(QwtPlot::yLeft, true);
    ui_.qwtPlot_4->setAxisScale(QwtPlot::xBottom, 0.0, 10.0);
    ui_.qwtPlot_4->setAxisScale(QwtPlot::yLeft, 0.0, 50);

    QwtPlotGrid *grid = new QwtPlotGrid();
    grid->enableXMin(false);
    grid->enableYMin(false);
    grid->setMajorPen(QPen(Qt::gray, 0, Qt::DotLine));
    grid->attach(ui_.qwtPlot_2);
    grid->attach(ui_.qwtPlot_4);
    ui_.qwtPlot_2->replot();
    ui_.qwtPlot_4->replot();
}

void PlanningRvizPanel::behaviorStrCallback(const std_msgs::msg::String::SharedPtr msg)
{
    std::string behavior_str_ = msg->data;
    ui_.label_behavior->setText(QString::fromStdString(behavior_str_));
}

void PlanningRvizPanel::odomCallback(const ackermann_msgs::msg::AckermannDriveOdometry::SharedPtr msg)
{
    std::unique_lock<std::mutex> lock(odom_mutex_);
    try {
        std::vector<double> xData_cmd, yData_cmd, xData_odom, yData_odom, xData_control_cmd, yData_control_cmd;

        odom_buffer_.push_back(*msg);
        while (!odom_buffer_.empty() && rclcpp::Time(odom_buffer_.front().header.stamp).seconds() <
                                            rclcpp::Time(msg->header.stamp).seconds() - 10.0) {
            odom_buffer_.pop_front();
        }
        if (!odom_buffer_.empty()) {
            if (rclcpp::Time(odom_buffer_.front().header.stamp).seconds() > rclcpp::Time(msg->header.stamp).seconds()) {
                odom_buffer_.clear();
            }
        }
        std::unique_lock<std::mutex> cmd_lock(cmd_mutex_);
        while (!cmd_buffer_.empty() && rclcpp::Time(cmd_buffer_.front().header.stamp).seconds() <
                                           rclcpp::Time(msg->header.stamp).seconds() - 10.0) {
            cmd_buffer_.pop_front();
            if (cmd_buffer_.empty()) break;
        }
        for (int i = 0; i < cmd_buffer_.size(); i++) {
            xData_cmd.push_back(rclcpp::Time(cmd_buffer_[i].header.stamp).seconds() -
                                rclcpp::Time(msg->header.stamp).seconds());
            yData_cmd.push_back(cmd_buffer_[i].drive.speed);
        }

        if (cmd_buffer_.size() > 0) {
            if (rclcpp::Time(cmd_buffer_.front().header.stamp).seconds() > rclcpp::Time(msg->header.stamp).seconds()) {
                cmd_buffer_.clear();
            }
        }
        cmd_lock.unlock();

        std::unique_lock<std::mutex> control_cmd_lock(control_cmd_mutex_);
        while (!control_cmd_buffer_.empty() && rclcpp::Time(control_cmd_buffer_.front().header.stamp).seconds() <
                                                   rclcpp::Time(msg->header.stamp).seconds() - 10.0) {
            control_cmd_buffer_.pop_front();
        }
        for (int i = 0; i < control_cmd_buffer_.size(); ++i) {
            xData_control_cmd.push_back(rclcpp::Time(control_cmd_buffer_[i].header.stamp).seconds() -
                                        rclcpp::Time(msg->header.stamp).seconds());
            yData_control_cmd.push_back(control_cmd_buffer_[i].drive.speed);
        }

        if (!control_cmd_buffer_.empty()) {
            if (rclcpp::Time(control_cmd_buffer_.front().header.stamp).seconds() >
                rclcpp::Time(msg->header.stamp).seconds()) {
                control_cmd_buffer_.clear();
            }
        }
        control_cmd_lock.unlock();
        // std::cout << "oodm size:" << odom_buffer_.size() << " cmd size: " << cmd_buffer_.size() << std::endl;

        ui_.qwtPlot_3->detachItems();
        ui_.qwtPlot_3->setTitle("");
        for (int i = 0; i < odom_buffer_.size(); i++) {
            xData_odom.push_back(rclcpp::Time(odom_buffer_[i].header.stamp).seconds() -
                                 rclcpp::Time(msg->header.stamp).seconds());
            yData_odom.push_back(odom_buffer_[i].speed);
        }
        QwtPlotCurve *curve1 = new QwtPlotCurve("monitor_cmd");
        QPen pen(Qt::green);
        pen.setWidth(3);  // 设置线条宽度
        curve1->setPen(pen);
        curve1->setSamples(xData_cmd.data(), yData_cmd.data(), yData_cmd.size());
        curve1->attach(ui_.qwtPlot_3);

        QwtPlotCurve *curve2 = new QwtPlotCurve("odom");
        QPen pen2(Qt::red);
        pen2.setWidth(2);
        curve2->setPen(pen2);
        curve2->setSamples(xData_odom.data(), yData_odom.data(), yData_odom.size());
        curve2->attach(ui_.qwtPlot_3);

        QwtPlotCurve *curve3 = new QwtPlotCurve("control_cmd");
        QPen pen3(Qt::blue);
        pen3.setWidth(2);
        curve3->setPen(pen3);
        curve3->setSamples(xData_control_cmd.data(), yData_control_cmd.data(), yData_control_cmd.size());
        curve3->attach(ui_.qwtPlot_3);

        QwtLegend *legend = new QwtLegend;
        ui_.qwtPlot_3->insertLegend(legend, QwtPlot::RightLegend);
        QwtPlotGrid *grid = new QwtPlotGrid();
        grid->enableXMin(false);
        grid->enableYMin(false);
        grid->setMajorPen(QPen(Qt::gray, 0, Qt::DotLine));
        grid->attach(ui_.qwtPlot_3);
        ui_.qwtPlot_3->replot();
    } catch (std::exception &e) {
        std::unique_lock<std::mutex> cmd_lock(cmd_mutex_);
        cmd_buffer_.clear();
        cmd_lock.unlock();
        std::unique_lock<std::mutex> control_cmd_lock(control_cmd_mutex_);
        control_cmd_buffer_.clear();
        control_cmd_lock.unlock();
        odom_buffer_.clear();
        std::cout << e.what() << std::endl;
    }
}
void PlanningRvizPanel::cmdCallback(const ackermann_msgs::msg::AckermannDriveStamped::SharedPtr msg)
{
    std::unique_lock<std::mutex> cmd_lock(cmd_mutex_);
    cmd_buffer_.push_back(*msg);
    while (!cmd_buffer_.empty() &&
           rclcpp::Time(cmd_buffer_.front().header.stamp).seconds() < rclcpp::Time(msg->header.stamp).seconds() - 10.0) {
        cmd_buffer_.pop_front();
        if (cmd_buffer_.empty()) break;
    }
}

void PlanningRvizPanel::controlCmdCallback(const ackermann_msgs::msg::AckermannDriveStamped::SharedPtr msg)
{
    std::unique_lock<std::mutex> control_cmd_lock(control_cmd_mutex_);
    control_cmd_buffer_.push_back(*msg);
    while (!control_cmd_buffer_.empty() && rclcpp::Time(control_cmd_buffer_.front().header.stamp).seconds() <
                                               rclcpp::Time(msg->header.stamp).seconds() - 10.0) {
        control_cmd_buffer_.pop_front();
    }
}

void PlanningRvizPanel::resizeEvent(QResizeEvent *event)
{
    // 获取窗口大小
    QSize windowSize = size();

    // 调用基类的 resizeEvent 函数
    rviz_common::Panel::resizeEvent(event);
    ui_.horizontalLayoutWidget->setGeometry(0, 0, windowSize.width(), windowSize.height());
}

// 重载父类的功能
void PlanningRvizPanel::save(rviz_common::Config config) const
{
    rviz_common::Panel::save(config);
    // config.mapSetValue("Topic", output_topic_);
}

// 重载父类的功能，加载配置数据
void PlanningRvizPanel::load(const rviz_common::Config &config)
{
    rviz_common::Panel::load(config);
    // QString topic;
    // if (config.mapGetString("Topic", &topic)) {
    //     output_topic_editor_->setText(topic);
    //     updateTopic();
    // }
}

}  // namespace planning_rviz_plugin

// 声明此类是一个rviz的插件
#include <pluginlib/class_list_macros.hpp>  // NOLINT
PLUGINLIB_EXPORT_CLASS(planning_rviz_plugin::PlanningRvizPanel, rviz_common::Panel)
