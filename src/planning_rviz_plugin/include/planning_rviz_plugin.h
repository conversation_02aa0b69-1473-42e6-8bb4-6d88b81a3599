/********************************************************************************
** Form generated from reading UI file 'planning_rviz_plugin.ui'
**
** Created by: Qt User Interface Compiler version 5.15.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef PLANNING_RVIZ_PLUGIN_H
#define PLANNING_RVIZ_PLUGIN_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QWidget>
#include "qwt_plot.h"

QT_BEGIN_NAMESPACE

class Ui_Form
{
public:
    QWidget *horizontalLayoutWidget;
    QGridLayout *gridLayout;
    QwtPlot *qwtPlot_2;
    QwtPlot *qwtPlot_4;
    QwtPlot *qwtPlot_3;
    QwtPlot *qwtPlot;
    QLabel *label_behavior;
    QLabel *label;

    void setupUi(QWidget *Form)
    {
        if (Form->objectName().isEmpty())
            Form->setObjectName(QString::fromUtf8("Form"));
        Form->resize(1451, 659);
        horizontalLayoutWidget = new QWidget(Form);
        horizontalLayoutWidget->setObjectName(QString::fromUtf8("horizontalLayoutWidget"));
        horizontalLayoutWidget->setGeometry(QRect(30, 70, 1355, 914));
        gridLayout = new QGridLayout(horizontalLayoutWidget);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        gridLayout->setContentsMargins(5, 5, 5, 5);
        qwtPlot_2 = new QwtPlot(horizontalLayoutWidget);
        qwtPlot_2->setObjectName(QString::fromUtf8("qwtPlot_2"));

        gridLayout->addWidget(qwtPlot_2, 2, 0, 1, 1);

        qwtPlot_4 = new QwtPlot(horizontalLayoutWidget);
        qwtPlot_4->setObjectName(QString::fromUtf8("qwtPlot_4"));

        gridLayout->addWidget(qwtPlot_4, 3, 0, 1, 1);

        qwtPlot_3 = new QwtPlot(horizontalLayoutWidget);
        qwtPlot_3->setObjectName(QString::fromUtf8("qwtPlot_3"));

        gridLayout->addWidget(qwtPlot_3, 3, 1, 1, 1);

        qwtPlot = new QwtPlot(horizontalLayoutWidget);
        qwtPlot->setObjectName(QString::fromUtf8("qwtPlot"));

        gridLayout->addWidget(qwtPlot, 2, 1, 1, 1);

        label_behavior = new QLabel(horizontalLayoutWidget);
        label_behavior->setObjectName(QString::fromUtf8("label_behavior"));

        gridLayout->addWidget(label_behavior, 0, 0, 1, 2);

        label = new QLabel(horizontalLayoutWidget);
        label->setObjectName(QString::fromUtf8("label"));

        gridLayout->addWidget(label, 1, 0, 1, 2);


        retranslateUi(Form);

        QMetaObject::connectSlotsByName(Form);
    } // setupUi

    void retranslateUi(QWidget *Form)
    {
        Form->setWindowTitle(QCoreApplication::translate("Form", "Form", nullptr));
        label_behavior->setText(QCoreApplication::translate("Form", "TextLabel", nullptr));
        label->setText(QCoreApplication::translate("Form", "TextLabel", nullptr));
    } // retranslateUi

};

namespace Ui {
    class Form: public Ui_Form {};
} // namespace Ui

QT_END_NAMESPACE

#endif // PLANNING_RVIZ_PLUGIN_H
