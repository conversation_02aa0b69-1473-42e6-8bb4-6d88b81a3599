#ifndef CONTROL_AND_DISPLAY_PANEL_H_
#define CONTROL_AND_DISPLAY_PANEL_H_

// 所需要包含的头文件
#include <QCursor>  // NOLINT cpplint cannot handle the include order here
#include <QObject>  // NOLINT cpplint cannot handle the include order here

#include "rclcpp/rclcpp.hpp"
#include "rclcpp/qos.hpp"
#include "rviz_common/panel.hpp"

#include "std_msgs/msg/string.hpp"
// #include "std_msgs/msg/bool.hpp"
// #include "geometry_msgs/msg/twist.hpp"
// #include "geometry_msgs/msg/pose_stamped.hpp"
// #include "geometry_msgs/msg/pose_with_covariance_stamped.hpp"
// #include <custom_msgs/msg/hmi_mode.hpp>
// #include <custom_msgs/msg/status_message.hpp>
// #include <custom_msgs/msg/system_status.hpp>
// #include <ackermann_msgs/msg/ackermann_drive_status.hpp>
#include <ackermann_msgs/msg/ackermann_drive_odometry.hpp>
#include <ackermann_msgs/msg/ackermann_drive_stamped.hpp>
// #include <ackermann_msgs/msg/ackermann_drive_odometry.hpp>
// #include <chitu_msgs/msg/horn.hpp>
// #include <nav_msgs/msg/odometry.hpp>
#include <custom_msgs/msg/trajectory.hpp>

#include "planning_rviz_plugin.h"

#include "rviz_common/display_context.hpp"
#include "qwt_plot_curve.h"

class QLineEdit;

namespace planning_rviz_plugin {
// 所有的plugin都必须是rviz::Panel的子类
class PlanningRvizPanel : public rviz_common::Panel
{
    // 后边需要用到Qt的信号和槽，都是QObject的子类，所以需要声明Q_OBJECT宏
    Q_OBJECT
public:
    // 构造函数，在类中会用到QWidget的实例来实现GUI界面，这里先初始化为0即可
    PlanningRvizPanel(QWidget *parent = 0);

    // 重载rviz::Panel积累中的函数，用于保存、加载配置文件中的数据，在我们这个plugin
    // 中，数据就是topic的名称
    virtual void load(const rviz_common::Config &config);
    virtual void save(rviz_common::Config config) const;

    void onInitialize() override;

    // 内部槽.
protected Q_SLOTS:
    // 在窗口大小变化时触发
    void resizeEvent(QResizeEvent *event);

private:
    void addTitle(QwtPlot *plot, std::string name);
    void trajCallback(const custom_msgs::msg::Trajectory::ConstSharedPtr msg, std::string name, QColor color,
                      int pen_width = 2);
    // void initialPoseCallback(const geometry_msgs::msg::PoseWithCovarianceStamped::SharedPtr msg);
    void latticeStrCallback(const std_msgs::msg::String::SharedPtr msg);
    void behaviorStrCallback(const std_msgs::msg::String::SharedPtr msg);
    // void hmiModeCallback(const custom_msgs::msg::HmiMode::SharedPtr msg);
    // void systemStatusCallback(const custom_msgs::msg::SystemStatus::SharedPtr msg);
    // void moduleStateCallback(const umodule_msgs::msg::ModuleState::SharedPtr msg);
    // void odomCallback(const nav_msgs::msg::Odometry::SharedPtr msg);
    // void batteryCallback(const ackermann_msgs::msg::AckermannDriveStatus::SharedPtr msg);
    void odomCallback(const ackermann_msgs::msg::AckermannDriveOdometry::SharedPtr msg);
    void cmdCallback(const ackermann_msgs::msg::AckermannDriveStamped::SharedPtr msg);
    void controlCmdCallback(const ackermann_msgs::msg::AckermannDriveStamped::SharedPtr msg);
    // void hornCallback(const chitu_msgs::msg::Horn::SharedPtr msg);
    // void publishJson(Json::Value &value);
    // void clearRequestResponseTextBrowser();
    // void loadGoalFile();
    // void loadGoalFileAndAddGoal(Json::Value one_goal);

    Ui::Form ui_;
    std::shared_ptr<rclcpp::Node> node_;

    // Publishers
    // rclcpp::Publisher<std_msgs::msg::String>::SharedPtr pub_task_request_;
    // rclcpp::Publisher<std_msgs::msg::Bool>::SharedPtr pub_engaged_;

    // Subscribers
    std::vector<rclcpp::Subscription<custom_msgs::msg::Trajectory>::SharedPtr> sub_traj_list_;
    std::map<std::string, QwtPlotCurve *> curve_list_;

    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr sub_lattice_string_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr sub_behavior_string_;
    rclcpp::Subscription<ackermann_msgs::msg::AckermannDriveStamped>::SharedPtr sub_cmd_;
    rclcpp::Subscription<ackermann_msgs::msg::AckermannDriveStamped>::SharedPtr sub_control_cmd_;
    rclcpp::Subscription<ackermann_msgs::msg::AckermannDriveOdometry>::SharedPtr sub_odom_;

    // rclcpp::Subscription<custom_msgs::msg::HmiMode>::SharedPtr sub_hmi_mode_;
    // rclcpp::Subscription<custom_msgs::msg::SystemStatus>::SharedPtr sub_system_status_;
    // rclcpp::Subscription<umodule_msgs::msg::ModuleState>::SharedPtr sub_module_state_;
    // rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr sub_odom_;
    // rclcpp::Subscription<ackermann_msgs::msg::AckermannDriveStatus>::SharedPtr sub_battrey_;
    // rclcpp::Subscription<ackermann_msgs::msg::AckermannDriveOdometry>::SharedPtr sub_joy_;
    // rclcpp::Subscription<chitu_msgs::msg::Horn>::SharedPtr sub_horn_;

    // double last_joy_speed_time_ = 0.0;
    // double last_horn_time_ = 0.0;
    // double current_time_from_odom_ = 0.0;
    std::mutex odom_mutex_, cmd_mutex_, control_cmd_mutex_;
    std::deque<ackermann_msgs::msg::AckermannDriveOdometry> odom_buffer_;
    std::deque<ackermann_msgs::msg::AckermannDriveStamped> cmd_buffer_;
    std::deque<ackermann_msgs::msg::AckermannDriveStamped> control_cmd_buffer_;

    // std::map<std::string, int> module_name_row_map_;

    // geometry_msgs::msg::PoseStamped goal_pose_;
    // geometry_msgs::msg::PoseWithCovarianceStamped initial_pose_;
    // bool is_goal_pose_received_, is_initial_pose_received_;
    // std::vector< std::map< std::string, geometry_msgs::msg::PoseStamped>> goal_pose_vec_;
    // int session_id_ = 1;
    // std::string status_str[6] = {"UNKNOWN", "OK", "INFO", "WARN", "ERROR", "FATAL"};
    // std::string goal_file_;
    // std::string selected_goal_name_;
};

}  // namespace planning_rviz_plugin

#endif  // CONTROL_AND_DISPLAY_PANEL_H_
